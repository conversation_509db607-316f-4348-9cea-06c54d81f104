{include file="customer/header.tpl"}
<!-- Modern User Dashboard -->

<!-- Greeting Section -->
<div class="greeting-section">
    <div class="container-fluid">
        <h1 class="greeting-text">
            <span id="greeting-time">{Lang::T('Good_Evening')}</span> 👋<br>
            <span class="user-name">{$_user['fullname']}</span>
        </h1>
        <p class="greeting-time" id="current-time"></p>
    </div>
</div>

{function showWidget pos=0}
    {foreach $widgets as $w}
        {if $w['position'] == $pos}
            {$w['content']}
        {/if}
    {/foreach}
{/function}


{assign rows explode(".", $_c['dashboard_Customer'])}
{assign pos 1}
{foreach $rows as $cols}
    {if $cols == 12}
        <div class="row">
            <div class="col-md-12">
                {showWidget widgets=$widgets pos=$pos}
            </div>
        </div>
        {assign pos value=$pos+1}
    {else}
        {assign colss explode(",", $cols)}
        <div class="row">
            {foreach $colss as $c}
                <div class="col-md-{$c}">
                    {showWidget widgets=$widgets pos=$pos}
                </div>
                {assign pos value=$pos+1}
            {/foreach}
        </div>
    {/if}
{/foreach}




<script type="text/javascript">
// Language-aware greeting messages
const greetingMessages = {
    morning: '{Lang::T("Good_Morning")}',
    afternoon: '{Lang::T("Good_Afternoon")}',
    evening: '{Lang::T("Good_Evening")}',
    night: '{Lang::T("Good_Night")}'
};

// Get current language and locale settings
const currentLanguage = '{$config.language}';
const dateLocale = currentLanguage === 'indonesia' ? 'id-ID' : 'en-US';
const numberLocale = currentLanguage === 'indonesia' ? 'id-ID' : 'en-US';

// Dynamic greeting based on time
function updateGreeting() {
    const now = new Date();
    const hour = now.getHours();
    const greetingElement = document.getElementById('greeting-time');
    const timeElement = document.getElementById('current-time');

    let greeting;
    if (hour < 12) {
        greeting = greetingMessages.morning;
    } else if (hour < 15) {
        greeting = greetingMessages.afternoon;
    } else if (hour < 18) {
        greeting = greetingMessages.evening;
    } else {
        greeting = greetingMessages.night;
    }

    if (greetingElement) {
        greetingElement.textContent = greeting;
    }

    if (timeElement) {
        timeElement.textContent = now.toLocaleDateString(dateLocale, {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
}

// Update greeting on page load
document.addEventListener('DOMContentLoaded', updateGreeting);



{if isset($hostname) && $hchap == 'true' && $_c['hs_auth_method'] == 'hchap'}
    // Original hotspot authentication code
    var hostname = "http://{$hostname}/login";
    var user = "{$_user['username']}";
    var pass = "{$_user['password']}";
    var dst = "{$apkurl}";
    var authdly = "2";
    var key = hexMD5('{$key1}' + pass + '{$key2}');
    var auth = hostname + '?username=' + user + '&dst=' + dst + '&password=' + key;
    document.write('<meta http-equiv="refresh" target="_blank" content="' + authdly + '; url=' + auth + '">');
{/if}
</script>

{if isset($hostname) && $hchap == 'true' && $_c['hs_auth_method'] == 'hchap'}
    <script type="text/javascript" src="/ui/ui/scripts/md5.js"></script>
{/if}
{include file="customer/footer.tpl"}