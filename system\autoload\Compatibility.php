<?php

/**
 * PHPNuxBill Compatibility Layer
 * Provides fallback functions for disabled system functions
 * by https://t.me/ibnux
 */

class Compatibility
{
    /**
     * Safe wrapper for shell_exec function
     * Returns fallback data if shell_exec is disabled
     */
    public static function shell_exec($command)
    {
        $disabled_functions = array_map('trim', explode(',', ini_get('disable_functions')));
        if (function_exists('shell_exec') && !in_array('shell_exec', $disabled_functions)) {
            // Call native function directly to avoid infinite loop
            return call_user_func('shell_exec', $command);
        }

        // Fallback for common system info commands
        return self::getFallbackSystemInfo($command);
    }
    
    /**
     * Safe wrapper for exec function
     */
    public static function exec($command, &$output = null, &$return_var = null)
    {
        $disabled_functions = array_map('trim', explode(',', ini_get('disable_functions')));
        if (function_exists('exec') && !in_array('exec', $disabled_functions)) {
            // Call native function directly to avoid infinite loop
            return call_user_func_array('exec', [$command, &$output, &$return_var]);
        }

        // Fallback
        $output = [];
        $return_var = 1;
        return self::getFallbackSystemInfo($command);
    }
    
    /**
     * Safe wrapper for system function
     */
    public static function system($command, &$return_var = null)
    {
        $disabled_functions = array_map('trim', explode(',', ini_get('disable_functions')));
        if (function_exists('system') && !in_array('system', $disabled_functions)) {
            // Call native function directly to avoid infinite loop
            return call_user_func_array('system', [$command, &$return_var]);
        }

        // Fallback
        $return_var = 1;
        return self::getFallbackSystemInfo($command);
    }
    
    /**
     * Safe wrapper for passthru function
     */
    public static function passthru($command, &$return_var = null)
    {
        $disabled_functions = array_map('trim', explode(',', ini_get('disable_functions')));
        if (function_exists('passthru') && !in_array('passthru', $disabled_functions)) {
            // Call native function directly to avoid infinite loop
            return call_user_func_array('passthru', [$command, &$return_var]);
        }

        // Fallback
        $return_var = 1;
        echo self::getFallbackSystemInfo($command);
    }
    
    /**
     * Check if system functions are available
     */
    public static function isSystemFunctionsAvailable()
    {
        $functions = ['shell_exec', 'exec', 'system', 'passthru'];
        $disabled = explode(',', ini_get('disable_functions'));
        
        foreach ($functions as $func) {
            if (function_exists($func) && !in_array($func, $disabled)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get fallback system information
     */
    private static function getFallbackSystemInfo($command)
    {
        $command = strtolower(trim($command));
        
        // Common system info commands fallbacks
        if (strpos($command, 'uname') !== false) {
            return self::getSystemUname();
        }
        
        if (strpos($command, 'lsb_release') !== false || strpos($command, '/etc/os-release') !== false) {
            return self::getSystemDistro();
        }
        
        if (strpos($command, 'uptime') !== false) {
            return self::getSystemUptime();
        }
        
        if (strpos($command, 'free') !== false || strpos($command, 'memory') !== false) {
            return self::getSystemMemory();
        }
        
        if (strpos($command, 'df') !== false || strpos($command, 'disk') !== false) {
            return self::getSystemDisk();
        }
        
        if (strpos($command, 'cpu') !== false || strpos($command, 'proc/cpuinfo') !== false) {
            return self::getSystemCPU();
        }
        
        // Default fallback
        return 'N/A (System functions disabled)';
    }
    
    /**
     * Get system uname info
     */
    private static function getSystemUname()
    {
        if (function_exists('php_uname')) {
            return php_uname();
        }
        
        return PHP_OS . ' ' . php_uname('r') . ' ' . php_uname('m');
    }
    
    /**
     * Get system distribution info
     */
    private static function getSystemDistro()
    {
        // Try to read from common files
        $files = [
            '/etc/os-release',
            '/etc/lsb-release',
            '/etc/redhat-release',
            '/etc/debian_version'
        ];
        
        foreach ($files as $file) {
            if (file_exists($file) && is_readable($file)) {
                $content = file_get_contents($file);
                if (!empty($content)) {
                    return trim($content);
                }
            }
        }
        
        return PHP_OS;
    }
    
    /**
     * Get system uptime
     */
    private static function getSystemUptime()
    {
        if (file_exists('/proc/uptime') && is_readable('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptime = explode(' ', $uptime);
            $seconds = (int)$uptime[0];
            
            $days = floor($seconds / 86400);
            $hours = floor(($seconds % 86400) / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            
            return sprintf('%d days, %d hours, %d minutes', $days, $hours, $minutes);
        }
        
        return 'N/A';
    }
    
    /**
     * Get system memory info
     */
    private static function getSystemMemory()
    {
        if (file_exists('/proc/meminfo') && is_readable('/proc/meminfo')) {
            $meminfo = file_get_contents('/proc/meminfo');
            return $meminfo;
        }
        
        return 'N/A';
    }
    
    /**
     * Get system disk info
     */
    private static function getSystemDisk()
    {
        $total = disk_total_space('/');
        $free = disk_free_space('/');
        $used = $total - $free;
        
        if ($total > 0) {
            return sprintf(
                "Total: %s, Used: %s, Free: %s",
                self::formatBytes($total),
                self::formatBytes($used),
                self::formatBytes($free)
            );
        }
        
        return 'N/A';
    }
    
    /**
     * Get system CPU info
     */
    private static function getSystemCPU()
    {
        if (file_exists('/proc/cpuinfo') && is_readable('/proc/cpuinfo')) {
            $cpuinfo = file_get_contents('/proc/cpuinfo');
            
            // Extract CPU model name
            if (preg_match('/model name\s*:\s*(.+)/i', $cpuinfo, $matches)) {
                return trim($matches[1]);
            }
        }
        
        return 'N/A';
    }
    
    /**
     * Format bytes to human readable format
     */
    private static function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Get system load average
     */
    public static function sys_getloadavg()
    {
        // For Windows or systems without sys_getloadavg, return fallback
        if (PHP_OS_FAMILY === 'Windows') {
            return [0, 0, 0];
        }

        // Fallback for Unix systems without sys_getloadavg
        if (file_exists('/proc/loadavg') && is_readable('/proc/loadavg')) {
            $loadavg = file_get_contents('/proc/loadavg');
            $loadavg = explode(' ', $loadavg);
            return array_slice($loadavg, 0, 3);
        }

        return [0, 0, 0];
    }

    /**
     * Check plugin compatibility
     */
    public static function checkPluginCompatibility($pluginFile)
    {
        if (!file_exists($pluginFile)) {
            return ['compatible' => false, 'reason' => 'Plugin file not found'];
        }

        $content = file_get_contents($pluginFile);
        $issues = [];

        // Check for disabled functions
        $disabled_functions = array_map('trim', explode(',', ini_get('disable_functions')));
        $system_functions = ['shell_exec', 'exec', 'system', 'passthru', 'proc_open', 'popen'];

        foreach ($system_functions as $func) {
            if (strpos($content, $func) !== false && in_array($func, $disabled_functions)) {
                $issues[] = "Function '$func' is used but disabled on this server";
            }
        }

        // Check for required extensions
        $required_extensions = [];
        if (strpos($content, 'curl_') !== false && !extension_loaded('curl')) {
            $required_extensions[] = 'curl';
        }
        if (strpos($content, 'json_') !== false && !extension_loaded('json')) {
            $required_extensions[] = 'json';
        }
        if (strpos($content, 'openssl_') !== false && !extension_loaded('openssl')) {
            $required_extensions[] = 'openssl';
        }

        if (!empty($required_extensions)) {
            $issues[] = "Required extensions not loaded: " . implode(', ', $required_extensions);
        }

        return [
            'compatible' => empty($issues),
            'issues' => $issues,
            'has_system_functions' => !empty(array_intersect($system_functions, array_map('trim', explode(',', str_replace(['(', ')'], '', $content)))))
        ];
    }

    /**
     * Get server environment info
     */
    public static function getServerEnvironment()
    {
        $disabled_functions = array_map('trim', explode(',', ini_get('disable_functions')));

        return [
            'php_version' => PHP_VERSION,
            'os' => PHP_OS,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'disabled_functions' => $disabled_functions,
            'system_functions_available' => self::isSystemFunctionsAvailable(),
            'safe_mode' => ini_get('safe_mode') ? 'On' : 'Off',
            'open_basedir' => ini_get('open_basedir') ?: 'None'
        ];
    }
}

// Define compatibility functions if they don't exist or are disabled
$disabled_functions = array_map('trim', explode(',', ini_get('disable_functions')));

if (!function_exists('shell_exec') || in_array('shell_exec', $disabled_functions)) {
    function shell_exec($command) {
        return Compatibility::shell_exec($command);
    }
}

if (!function_exists('exec') || in_array('exec', $disabled_functions)) {
    function exec($command, &$output = null, &$return_var = null) {
        return Compatibility::exec($command, $output, $return_var);
    }
}

if (!function_exists('system') || in_array('system', $disabled_functions)) {
    function system($command, &$return_var = null) {
        return Compatibility::system($command, $return_var);
    }
}

if (!function_exists('passthru') || in_array('passthru', $disabled_functions)) {
    function passthru($command, &$return_var = null) {
        return Compatibility::passthru($command, $return_var);
    }
}

if (!function_exists('sys_getloadavg')) {
    function sys_getloadavg() {
        // Fallback for systems without sys_getloadavg (like Windows)
        if (file_exists('/proc/loadavg') && is_readable('/proc/loadavg')) {
            $loadavg = file_get_contents('/proc/loadavg');
            $loadavg = explode(' ', $loadavg);
            return array_slice($loadavg, 0, 3);
        }

        return [0, 0, 0];
    }
}

// Additional compatibility functions for common plugin needs
if (!function_exists('proc_open') || in_array('proc_open', $disabled_functions)) {
    function proc_open($cmd, $descriptorspec, &$pipes, $cwd = null, $env = null, $other_options = null) {
        // Return false to indicate failure
        return false;
    }
}

if (!function_exists('popen') || in_array('popen', $disabled_functions)) {
    function popen($command, $mode) {
        // Return false to indicate failure
        return false;
    }
}
