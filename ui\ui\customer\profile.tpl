{include file="customer/header.tpl"}
<!-- Modern User Profile -->

<div class="row">
    <div class="col-lg-8 col-lg-offset-2 col-md-10 col-md-offset-1 col-sm-12">
        <div class="modern-card profile-card" style="
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin-bottom: 30px;
        ">
            <!-- Profile Header -->
            <div class="profile-header" style="
                background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                padding: 30px 24px 20px;
                color: white;
                text-align: center;
                position: relative;
            ">
                <div style="
                    position: absolute;
                    top: -50px;
                    right: -50px;
                    width: 150px;
                    height: 150px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                "></div>
                <h3 style="
                    font-size: 1.5rem;
                    font-weight: 600;
                    margin: 0 0 8px 0;
                    position: relative;
                    z-index: 2;
                ">
                    <i class="fa fa-user-edit" style="margin-right: 8px; opacity: 0.9;"></i>
                    {Lang::T('Data Change')}
                </h3>
                <p style="
                    font-size: 0.875rem;
                    opacity: 0.9;
                    margin: 0;
                    position: relative;
                    z-index: 2;
                ">Update your profile information</p>
            </div>

            <!-- Profile Form -->
            <div class="profile-form-container" style="padding: 30px 24px;">
                <form class="modern-form" enctype="multipart/form-data" method="post" role="form"
                    action="{Text::url('accounts/edit-profile-post')}">
                    <input type="hidden" name="csrf_token" value="{$csrf_token}">
                    <input type="hidden" name="id" value="{$_user['id']}">

                    <!-- Profile Photo Section -->
                    <div class="profile-photo-section" style="text-align: center; margin-bottom: 30px;">
                        <div class="profile-photo-container" style="
                            display: inline-block;
                            position: relative;
                            margin-bottom: 20px;
                        ">
                            <img src="{$app_url}/{$UPLOAD_PATH}{$_user['photo']}.thumb.jpg"
                                class="profile-photo"
                                alt="Profile Photo"
                                onclick="return deletePhoto({$_user['id']})"
                                data-avatar="true"
                                data-avatar-name="{$_user['fullname']}"
                                data-avatar-type="customer"
                                data-avatar-size="profile"
                                style="
                                    width: 120px;
                                    height: 120px;
                                    border-radius: 50%;
                                    border: 4px solid #00BFA5;
                                    box-shadow: 0 8px 24px rgba(0, 191, 165, 0.3);
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    object-fit: cover;
                                "
                                onmouseover="this.style.transform='scale(1.05)'"
                                onmouseout="this.style.transform='scale(1)'">
                        </div>
                    </div>
                    <!-- Photo Upload Section -->
                    <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                        <label class="modern-form-label" style="
                            display: block;
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: #374151;
                            margin-bottom: 8px;
                        ">
                            <i class="fa fa-camera" style="margin-right: 8px; color: #00BFA5;"></i>
                            {Lang::T('Photo')}
                        </label>
                        <div class="row">
                            <div class="col-md-8 col-sm-8 col-xs-12">
                                <div class="modern-file-input" style="
                                    position: relative;
                                    display: inline-block;
                                    width: 100%;
                                ">
                                    <input type="file"
                                           class="file-input"
                                           name="photo"
                                           accept="image/*"
                                           id="photo-input"
                                           style="
                                               position: absolute;
                                               opacity: 0;
                                               width: 100%;
                                               height: 100%;
                                               cursor: pointer;
                                           ">
                                    <div class="file-input-display" style="
                                        background: #f8fafc;
                                        border: 2px dashed #d1d5db;
                                        border-radius: 12px;
                                        padding: 16px;
                                        text-align: center;
                                        transition: all 0.3s ease;
                                        cursor: pointer;
                                    " onmouseover="this.style.borderColor='#00BFA5'; this.style.background='#f0fdfa'"
                                       onmouseout="this.style.borderColor='#d1d5db'; this.style.background='#f8fafc'">
                                        <i class="fa fa-cloud-upload" style="font-size: 1.5rem; color: #6b7280; margin-bottom: 8px;"></i>
                                        <div style="font-size: 0.875rem; color: #6b7280;">
                                            Click to upload new photo
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12" style="margin-top: 8px;">
                                <div class="face-detect-option" style="
                                    background: #f0fdfa;
                                    border: 1px solid #a7f3d0;
                                    border-radius: 12px;
                                    padding: 12px;
                                ">
                                    <label class="modern-checkbox" style="
                                        display: flex;
                                        align-items: center;
                                        font-size: 0.75rem;
                                        color: #059669;
                                        cursor: pointer;
                                        margin: 0;
                                    ">
                                        <input type="checkbox"
                                               checked
                                               name="faceDetect"
                                               value="yes"
                                               style="margin-right: 8px;">
                                        <i class="fa fa-eye" style="margin-right: 4px;"></i>
                                        {Lang::T('Face Detect')}
                                    </label>
                                    <div style="font-size: 0.625rem; color: #6b7280; margin-top: 4px;">
                                        Auto-detect face in photo
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Username Section -->
                    <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                        <label class="modern-form-label" style="
                            display: block;
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: #374151;
                            margin-bottom: 8px;
                        ">
                            {if $_c['registration_username'] == 'phone'}
                                <i class="fa fa-phone" style="margin-right: 8px; color: #00BFA5;"></i>
                            {elseif $_c['registration_username'] == 'email'}
                                <i class="fa fa-envelope" style="margin-right: 8px; color: #00BFA5;"></i>
                            {else}
                                <i class="fa fa-user" style="margin-right: 8px; color: #00BFA5;"></i>
                            {/if}
                            {Lang::T('Usernames')}
                        </label>
                        <div class="modern-input-group" style="position: relative;">
                            <input type="text"
                                   readonly
                                   class="modern-form-input"
                                   name="username"
                                   id="username"
                                   value="{$_user['username']}"
                                   style="
                                       width: 100%;
                                       padding: 12px 40px 12px 16px;
                                       border: 2px solid #e5e7eb;
                                       border-radius: 12px;
                                       font-size: 0.875rem;
                                       background: #f9fafb;
                                       color: #6b7280;
                                   ">
                            <div class="input-icon" style="
                                position: absolute;
                                right: 12px;
                                top: 50%;
                                transform: translateY(-50%);
                                color: #9ca3af;
                            ">
                                <i class="fa fa-lock"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Full Name Section -->
                    <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                        <label class="modern-form-label" style="
                            display: block;
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: #374151;
                            margin-bottom: 8px;
                        ">
                            <i class="fa fa-id-card" style="margin-right: 8px; color: #00BFA5;"></i>
                            {Lang::T('Full Name')}
                        </label>
                        <input type="text"
                               class="modern-form-input"
                               id="fullname"
                               name="fullname"
                               value="{$_user['fullname']}"
                               style="
                                   width: 100%;
                                   padding: 12px 16px;
                                   border: 2px solid #e5e7eb;
                                   border-radius: 12px;
                                   font-size: 0.875rem;
                                   transition: all 0.3s ease;
                               "
                               onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                    </div>

                    <!-- Home Address Section -->
                    <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                        <label class="modern-form-label" style="
                            display: block;
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: #374151;
                            margin-bottom: 8px;
                        ">
                            <i class="fa fa-home" style="margin-right: 8px; color: #00BFA5;"></i>
                            {Lang::T('Home Address')}
                        </label>
                        <textarea name="address"
                                  id="address"
                                  class="modern-form-textarea"
                                  rows="3"
                                  style="
                                      width: 100%;
                                      padding: 12px 16px;
                                      border: 2px solid #e5e7eb;
                                      border-radius: 12px;
                                      font-size: 0.875rem;
                                      transition: all 0.3s ease;
                                      resize: vertical;
                                      min-height: 80px;
                                  "
                                  onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                  onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">{$_user['address']}</textarea>
                    </div>
                    <!-- Phone Number Section -->
                    {if $_c['allow_phone_otp'] != 'yes'}
                        <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                            <label class="modern-form-label" style="
                                display: block;
                                font-size: 0.875rem;
                                font-weight: 600;
                                color: #374151;
                                margin-bottom: 8px;
                            ">
                                <i class="fa fa-phone" style="margin-right: 8px; color: #00BFA5;"></i>
                                {Lang::T('Phone Number')}
                            </label>
                            <input type="text"
                                   class="modern-form-input"
                                   name="phonenumber"
                                   id="phonenumber"
                                   value="{$_user['phonenumber']}"
                                   placeholder="{if $_c['country_code_phone']!= ''}{$_c['country_code_phone']}{/if} {Lang::T('Phone Number')}"
                                   style="
                                       width: 100%;
                                       padding: 12px 16px;
                                       border: 2px solid #e5e7eb;
                                       border-radius: 12px;
                                       font-size: 0.875rem;
                                       transition: all 0.3s ease;
                                   "
                                   onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                        </div>
                    {else}
                        <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                            <label class="modern-form-label" style="
                                display: block;
                                font-size: 0.875rem;
                                font-weight: 600;
                                color: #374151;
                                margin-bottom: 8px;
                            ">
                                <i class="fa fa-phone" style="margin-right: 8px; color: #00BFA5;"></i>
                                {Lang::T('Phone Number')}
                            </label>
                            <div class="modern-input-group" style="
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            ">
                                <input type="text"
                                       class="modern-form-input"
                                       name="phonenumber"
                                       id="phonenumber"
                                       value="{$_user['phonenumber']}"
                                       readonly
                                       placeholder="{if $_c['country_code_phone']!= ''}{$_c['country_code_phone']}{/if} {Lang::T('Phone Number')}"
                                       style="
                                           flex: 1;
                                           padding: 12px 16px;
                                           border: 2px solid #e5e7eb;
                                           border-radius: 12px;
                                           font-size: 0.875rem;
                                           background: #f9fafb;
                                           color: #6b7280;
                                       ">
                                <a href="{Text::url('accounts/phone-update')}"
                                   class="modern-btn-secondary"
                                   style="
                                       padding: 12px 20px;
                                       background: #00BFA5;
                                       color: white;
                                       border-radius: 12px;
                                       text-decoration: none;
                                       font-size: 0.875rem;
                                       font-weight: 500;
                                       transition: all 0.3s ease;
                                       white-space: nowrap;
                                   "
                                   onmouseover="this.style.background='#009688'"
                                   onmouseout="this.style.background='#00BFA5'">
                                    <i class="fa fa-edit" style="margin-right: 4px;"></i>
                                    {Lang::T('Change')}
                                </a>
                            </div>
                        </div>
                    {/if}
                    <!-- Email Address Section -->
                    {if $_c['allow_email_otp'] != 'yes'}
                        <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                            <label class="modern-form-label" style="
                                display: block;
                                font-size: 0.875rem;
                                font-weight: 600;
                                color: #374151;
                                margin-bottom: 8px;
                            ">
                                <i class="fa fa-envelope" style="margin-right: 8px; color: #00BFA5;"></i>
                                {Lang::T('Email Address')}
                            </label>
                            <input type="email"
                                   class="modern-form-input"
                                   id="email"
                                   name="email"
                                   value="{$_user['email']}"
                                   style="
                                       width: 100%;
                                       padding: 12px 16px;
                                       border: 2px solid #e5e7eb;
                                       border-radius: 12px;
                                       font-size: 0.875rem;
                                       transition: all 0.3s ease;
                                   "
                                   onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                        </div>
                    {else}
                        <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                            <label class="modern-form-label" style="
                                display: block;
                                font-size: 0.875rem;
                                font-weight: 600;
                                color: #374151;
                                margin-bottom: 8px;
                            ">
                                <i class="fa fa-envelope" style="margin-right: 8px; color: #00BFA5;"></i>
                                {Lang::T('Email Address')}
                            </label>
                            <div class="modern-input-group" style="
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            ">
                                <input type="email"
                                       class="modern-form-input"
                                       name="email"
                                       id="email"
                                       value="{$_user['email']}"
                                       readonly
                                       style="
                                           flex: 1;
                                           padding: 12px 16px;
                                           border: 2px solid #e5e7eb;
                                           border-radius: 12px;
                                           font-size: 0.875rem;
                                           background: #f9fafb;
                                           color: #6b7280;
                                       ">
                                <a href="{Text::url('accounts/email-update')}"
                                   class="modern-btn-secondary"
                                   style="
                                       padding: 12px 20px;
                                       background: #00BFA5;
                                       color: white;
                                       border-radius: 12px;
                                       text-decoration: none;
                                       font-size: 0.875rem;
                                       font-weight: 500;
                                       transition: all 0.3s ease;
                                       white-space: nowrap;
                                   "
                                   onmouseover="this.style.background='#009688'"
                                   onmouseout="this.style.background='#00BFA5'">
                                    <i class="fa fa-edit" style="margin-right: 4px;"></i>
                                    {Lang::T('Change')}
                                </a>
                            </div>
                        </div>
                    {/if}

                    <!-- Custom Fields -->
                    {if $customFields}
                        <div class="custom-fields-section" style="margin-bottom: 24px;">
                            {$customFields}
                        </div>
                    {/if}

                    <!-- Form Actions -->
                    <div class="form-actions" style="
                        margin-top: 32px;
                        padding-top: 24px;
                        border-top: 1px solid #e5e7eb;
                    ">
                        <div class="row">
                            <div class="col-md-6 col-sm-6 col-xs-12" style="margin-bottom: 12px;">
                                <button class="modern-btn-primary"
                                        type="submit"
                                        style="
                                            width: 100%;
                                            padding: 14px 24px;
                                            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                                            color: white;
                                            border: none;
                                            border-radius: 12px;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            cursor: pointer;
                                            transition: all 0.3s ease;
                                            box-shadow: 0 4px 12px rgba(0, 191, 165, 0.3);
                                        "
                                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(0, 191, 165, 0.4)'"
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 191, 165, 0.3)'">
                                    <i class="fa fa-save" style="margin-right: 8px;"></i>
                                    {Lang::T('Save Changes')}
                                </button>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12">
                                <a href="{Text::url('home')}"
                                   class="modern-btn-secondary"
                                   style="
                                       display: block;
                                       width: 100%;
                                       padding: 14px 24px;
                                       background: #f8fafc;
                                       color: #6b7280;
                                       border: 2px solid #e5e7eb;
                                       border-radius: 12px;
                                       font-size: 0.875rem;
                                       font-weight: 500;
                                       text-decoration: none;
                                       text-align: center;
                                       cursor: pointer;
                                       transition: all 0.3s ease;
                                   "
                                   onmouseover="this.style.background='#f1f5f9'; this.style.borderColor='#cbd5e1'"
                                   onmouseout="this.style.background='#f8fafc'; this.style.borderColor='#e5e7eb'">
                                    <i class="fa fa-times" style="margin-right: 8px;"></i>
                                    {Lang::T('Cancel')}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{include file="customer/footer.tpl"}