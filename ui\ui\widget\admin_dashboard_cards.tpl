<!-- Modern Admin Dashboard Cards -->
<div class="row admin-dashboard-stats">
    <!-- Quick Stats Cards -->
    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-6">
        <div class="modern-card dashboard-stat-card" style="background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%); color: white; padding: 0; overflow: hidden;">
            <!-- Card Content -->
            <div style="padding: 24px;">
                <div class="row">
                    <div class="col-xs-8">
                        <div class="stat-label" style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 8px;">{Lang::T('Total_Users')}</div>
                        <div class="stat-value" style="font-size: 2rem; font-weight: 700;">{$c_all}</div>
                    </div>
                    <div class="col-xs-4 text-right">
                        <i class="fa fa-users stat-icon" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
            <!-- Card Footer -->
            <a href="{Text::url('customers')}" class="dashboard-card-footer" style="
                display: block;
                background: rgba(255, 255, 255, 0.1);
                padding: 12px 24px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                color: white;
                text-decoration: none;
                transition: all 0.3s ease;
            " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
               onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <i class="fa fa-users" style="font-size: 0.875rem; opacity: 0.9;"></i>
                    <span style="font-size: 0.875rem; font-weight: 500; opacity: 0.9;">
                        {Lang::T('Manage Customers')}
                    </span>
                    <i class="fa fa-arrow-right" style="font-size: 0.75rem; opacity: 0.8;"></i>
                </div>
            </a>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-6">
        <div class="modern-card dashboard-stat-card" style="background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%); color: white; padding: 0; overflow: hidden;">
            <!-- Card Content -->
            <div style="padding: 24px;">
                <div class="row">
                    <div class="col-xs-8">
                        <div class="stat-label" style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 8px;">{Lang::T('Users_Active')}</div>
                        <div class="stat-value" style="font-size: 2rem; font-weight: 700;">{$u_act}</div>
                    </div>
                    <div class="col-xs-4 text-right">
                        <i class="fa fa-user stat-icon" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
            <!-- Card Footer -->
            <a href="{Text::url('plan/list')}" class="dashboard-card-footer" style="
                display: block;
                background: rgba(255, 255, 255, 0.1);
                padding: 12px 24px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                color: white;
                text-decoration: none;
                transition: all 0.3s ease;
            " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
               onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <i class="fa fa-list" style="font-size: 0.875rem; opacity: 0.9;"></i>
                    <span style="font-size: 0.875rem; font-weight: 500; opacity: 0.9;">
                        {Lang::T('View Active Users')}
                    </span>
                    <i class="fa fa-arrow-right" style="font-size: 0.75rem; opacity: 0.8;"></i>
                </div>
            </a>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-6">
        <div class="modern-card dashboard-stat-card" style="background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%); color: white; padding: 0; overflow: hidden;">
            <!-- Card Content -->
            <div style="padding: 24px;">
                <div class="row">
                    <div class="col-xs-8">
                        <div class="stat-label" style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 8px;">{Lang::T('Hotspot_Online_Users')}</div>
                        <div class="stat-value" style="font-size: 2rem; font-weight: 700;" id="hotspot-online-count">{$hotspot_online|default:0}</div>
                    </div>
                    <div class="col-xs-4 text-right">
                        <i class="fa fa-wifi stat-icon" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
            <!-- Card Footer -->
            <a href="{Text::url('reports/hotspot')}" class="dashboard-card-footer" style="
                display: block;
                background: rgba(255, 255, 255, 0.1);
                padding: 12px 24px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                color: white;
                text-decoration: none;
                transition: all 0.3s ease;
            " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
               onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <i class="fa fa-wifi" style="font-size: 0.875rem; opacity: 0.9;"></i>
                    <span style="font-size: 0.875rem; font-weight: 500; opacity: 0.9;">
                        {Lang::T('Hotspot Reports')}
                    </span>
                    <i class="fa fa-arrow-right" style="font-size: 0.75rem; opacity: 0.8;"></i>
                </div>
            </a>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-6">
        <div class="modern-card dashboard-stat-card" style="background: linear-gradient(135deg, #9C27B0 0%, #E91E63 100%); color: white; padding: 0; overflow: hidden;">
            <!-- Card Content -->
            <div style="padding: 24px;">
                <div class="row">
                    <div class="col-xs-8">
                        <div class="stat-label" style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 8px;">{Lang::T('PPPoE_Online_Users')}</div>
                        <div class="stat-value" style="font-size: 2rem; font-weight: 700;" id="pppoe-online-count">{$pppoe_online|default:0}</div>
                    </div>
                    <div class="col-xs-4 text-right">
                        <i class="fa fa-link stat-icon" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
            <!-- Card Footer -->
            <a href="{Text::url('reports/pppoe')}" class="dashboard-card-footer" style="
                display: block;
                background: rgba(255, 255, 255, 0.1);
                padding: 12px 24px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                color: white;
                text-decoration: none;
                transition: all 0.3s ease;
            " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
               onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <i class="fa fa-link" style="font-size: 0.875rem; opacity: 0.9;"></i>
                    <span style="font-size: 0.875rem; font-weight: 500; opacity: 0.9;">
                        {Lang::T('PPPoE Reports')}
                    </span>
                    <i class="fa fa-arrow-right" style="font-size: 0.75rem; opacity: 0.8;"></i>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Quick Actions for Admin -->
<div class="row admin-quick-actions-section" style="margin-top: 24px;">
    <div class="col-md-12">
        <div class="modern-card" style="padding: 24px;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 20px;">Quick Actions</h3>
            <div class="admin-quick-actions-grid">
                <a href="{Text::url('customers/add')}" class="admin-quick-action-item">
                    <div class="admin-quick-action-icon" style="background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);">
                        <i class="fa fa-user-plus"></i>
                    </div>
                    <div class="admin-quick-action-label">Add Customer</div>
                </a>
                <a href="{Text::url('plan/recharge')}" class="admin-quick-action-item">
                    <div class="admin-quick-action-icon" style="background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);">
                        <i class="fa fa-credit-card"></i>
                    </div>
                    <div class="admin-quick-action-label">Recharge</div>
                </a>
                <a href="{Text::url('services/hotspot')}" class="admin-quick-action-item">
                    <div class="admin-quick-action-icon" style="background: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);">
                        <i class="fa fa-wifi"></i>
                    </div>
                    <div class="admin-quick-action-label">Hotspot Plans</div>
                </a>
                <a href="{Text::url('voucher/add')}" class="admin-quick-action-item">
                    <div class="admin-quick-action-icon" style="background: linear-gradient(135deg, #9C27B0 0%, #BA68C8 100%);">
                        <i class="fa fa-ticket"></i>
                    </div>
                    <div class="admin-quick-action-label">Generate Voucher</div>
                </a>
                <a href="{Text::url('reports')}" class="admin-quick-action-item">
                    <div class="admin-quick-action-icon" style="background: linear-gradient(135deg, #607D8B 0%, #78909C 100%);">
                        <i class="fa fa-bar-chart"></i>
                    </div>
                    <div class="admin-quick-action-label">Reports</div>
                </a>
                <a href="{Text::url('settings/app')}" class="admin-quick-action-item">
                    <div class="admin-quick-action-icon" style="background: linear-gradient(135deg, #795548 0%, #8D6E63 100%);">
                        <i class="fa fa-cogs"></i>
                    </div>
                    <div class="admin-quick-action-label">Settings</div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- System Status Cards -->
<div class="row admin-system-status-section" style="margin-top: 24px;">
    <div class="col-md-6 col-sm-6 col-xs-12">
        <div class="modern-card dashboard-system-card" style="padding: 0; overflow: hidden;">
            <!-- Card Content -->
            <div style="padding: 24px;">
                <h3 style="font-size: 1.125rem; font-weight: 600; color: #1e293b; margin-bottom: 16px;">
                    <i class="fa fa-server" style="margin-right: 8px; color: #00BFA5;"></i>
                    System Status
                </h3>
                <div class="row">
                    <div class="col-xs-6">
                        <div class="system-status-item" style="text-align: center; padding: 16px;">
                            <div style="font-size: 2rem; color: #4CAF50; margin-bottom: 8px;">
                                <i class="fa fa-check-circle"></i>
                            </div>
                            <div style="font-size: 0.875rem; color: #64748b;">Server Online</div>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="system-status-item" style="text-align: center; padding: 16px;">
                            <div style="font-size: 2rem; color: #2196F3; margin-bottom: 8px;">
                                <i class="fa fa-database"></i>
                            </div>
                            <div style="font-size: 0.875rem; color: #64748b;">Database OK</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Card Footer -->
            <a href="{Text::url('routers')}" class="dashboard-card-footer" style="
                display: block;
                background: #f8fafc;
                padding: 12px 24px;
                border-top: 1px solid #e2e8f0;
                color: #64748b;
                text-decoration: none;
                transition: all 0.3s ease;
            " onmouseover="this.style.background='#f1f5f9'; this.style.color='#475569'"
               onmouseout="this.style.background='#f8fafc'; this.style.color='#64748b'">
                <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <i class="fa fa-cogs" style="font-size: 0.875rem;"></i>
                    <span style="font-size: 0.875rem; font-weight: 500;">
                        {Lang::T('Network Settings')}
                    </span>
                    <i class="fa fa-arrow-right" style="font-size: 0.75rem;"></i>
                </div>
            </a>
        </div>
    </div>

    <div class="col-md-6 col-sm-6 col-xs-12">
        <div class="modern-card dashboard-activity-card" style="padding: 0; overflow: hidden;">
            <!-- Card Content -->
            <div style="padding: 24px;">
                <h3 style="font-size: 1.125rem; font-weight: 600; color: #1e293b; margin-bottom: 16px;">
                    <i class="fa fa-clock-o" style="margin-right: 8px; color: #FF9800;"></i>
                    Recent Activity
                </h3>
                <div class="activity-list" style="max-height: 200px; overflow-y: auto;">
                    <div class="activity-item" style="padding: 8px 0; border-bottom: 1px solid #e2e8f0;">
                        <div style="font-size: 0.875rem; color: #1e293b; font-weight: 500;">New customer registered</div>
                        <div style="font-size: 0.75rem; color: #64748b;">2 minutes ago</div>
                    </div>
                    <div class="activity-item" style="padding: 8px 0; border-bottom: 1px solid #e2e8f0;">
                        <div style="font-size: 0.875rem; color: #1e293b; font-weight: 500;">Payment received</div>
                        <div style="font-size: 0.75rem; color: #64748b;">5 minutes ago</div>
                    </div>
                    <div class="activity-item" style="padding: 8px 0;">
                        <div style="font-size: 0.875rem; color: #1e293b; font-weight: 500;">Voucher generated</div>
                        <div style="font-size: 0.75rem; color: #64748b;">10 minutes ago</div>
                    </div>
                </div>
            </div>
            <!-- Card Footer -->
            <a href="{Text::url('reports')}" class="dashboard-card-footer" style="
                display: block;
                background: #f8fafc;
                padding: 12px 24px;
                border-top: 1px solid #e2e8f0;
                color: #64748b;
                text-decoration: none;
                transition: all 0.3s ease;
            " onmouseover="this.style.background='#f1f5f9'; this.style.color='#475569'"
               onmouseout="this.style.background='#f8fafc'; this.style.color='#64748b'">
                <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <i class="fa fa-bar-chart" style="font-size: 0.875rem;"></i>
                    <span style="font-size: 0.875rem; font-weight: 500;">
                        {Lang::T('View All Reports')}
                    </span>
                    <i class="fa fa-arrow-right" style="font-size: 0.75rem;"></i>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- JavaScript for Online Users Count -->
<script type="text/javascript">
function updateOnlineUsersCount() {
    // Update Hotspot Online Users
    $.ajax({
        url: '{$app_url}/?_route=autoload/hotspot_online_count',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data && typeof data.count !== 'undefined') {
                $('#hotspot-online-count').text(data.count);
            }
        },
        error: function() {
            // Fallback: keep current value or use 0
            var current = $('#hotspot-online-count').text();
            if (!current || current === '0') {
                $('#hotspot-online-count').text('0');
            }
        }
    });

    // Update PPPoE Online Users
    $.ajax({
        url: '{$app_url}/?_route=autoload/pppoe_online_count',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data && typeof data.count !== 'undefined') {
                $('#pppoe-online-count').text(data.count);
            }
        },
        error: function() {
            // Fallback: keep current value or use 0
            var current = $('#pppoe-online-count').text();
            if (!current || current === '0') {
                $('#pppoe-online-count').text('0');
            }
        }
    });
}

// Update counts on page load
$(document).ready(function() {
    updateOnlineUsersCount();

    // Update every 30 seconds
    setInterval(updateOnlineUsersCount, 30000);
});
</script>
