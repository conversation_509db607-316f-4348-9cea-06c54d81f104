<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>{Lang::T('Register')} - {$_c['CompanyName']}</title>
    <link rel="shortcut icon" href="{$app_url}/ui/ui/images/logo.png" type="image/x-icon" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>
    <script src="{$app_url}/ui/ui/scripts/jquery.min.js"></script>
    <script src="{$app_url}/ui/ui/scripts/bootstrap.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 50%, #26A69A 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            padding: 20px 0;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        .register-container {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .register-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .register-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00BFA5, #4DB6AC);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(0, 191, 165, 0.3);
        }

        .register-logo i {
            font-size: 2rem;
            color: white;
        }

        .register-title {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .register-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            font-weight: 500;
        }

        .register-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 30px;
            align-items: start;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: sticky;
            top: 20px;
        }

        .info-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }

        .info-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }

        .info-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .info-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
        }

        .info-content {
            color: #475569;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .register-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .register-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00BFA5, #4DB6AC, #26A69A);
            border-radius: 24px 24px 0 0;
        }

        .form-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
        }

        .section-number {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #00BFA5, #4DB6AC);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 12px;
            font-size: 0.9rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input-group {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 500;
            color: #1e293b;
            background: #ffffff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #00BFA5;
            box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
            transform: translateY(-1px);
        }

        .form-input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }

        .form-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .form-input:focus + .form-icon {
            color: #00BFA5;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            position: absolute;
            left: -9999px;
        }

        .file-input-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .file-input-label:hover {
            border-color: #00BFA5;
            background: #f0fdfa;
        }

        .file-input-label i {
            margin-right: 12px;
            color: #64748b;
            font-size: 1.2rem;
        }

        .file-input-text {
            color: #64748b;
            font-weight: 500;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-top: 32px;
            margin-bottom: 24px;
        }

        .btn-cancel {
            padding: 16px 24px;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-register {
            padding: 16px 24px;
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-register:hover::before {
            left: 100%;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .policy-links {
            text-align: center;
            margin-top: 24px;
        }

        .policy-links a {
            color: #00BFA5;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .policy-links a:hover {
            color: #26A69A;
            text-decoration: none;
        }

        .policy-separator {
            color: #cbd5e1;
            margin: 0 12px;
        }

        /* Password Strength Indicator */
        .password-strength {
            margin-top: 8px;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: #ef4444; width: 25%; }
        .strength-fair { background: #f59e0b; width: 50%; }
        .strength-good { background: #10b981; width: 75%; }
        .strength-strong { background: #059669; width: 100%; }

        /* Responsive Design */
        @media (max-width: 992px) {
            .register-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .info-card {
                position: static;
                order: 2;
            }

            .register-form {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .register-title {
                font-size: 1.75rem;
            }
        }

        @media (max-width: 480px) {
            .register-container {
                padding: 10px;
            }

            .register-form,
            .info-card {
                padding: 24px;
                border-radius: 20px;
            }

            .form-input {
                padding: 14px 18px 14px 45px;
            }
        }
    </style>
</head>

<body>
    <div class="register-container">
        <div class="register-header">
            <div class="register-logo">
                <i class="fa fa-user-plus"></i>
            </div>
            <h1 class="register-title">Create Account</h1>
            <p class="register-subtitle">Join {$_c['CompanyName']} today and get started</p>
        </div>

        <div class="register-content">
            <div class="info-card">
                <div class="info-header">
                    <div class="info-icon">
                        <i class="fa fa-info-circle"></i>
                    </div>
                    <h2 class="info-title">{Lang::T('Registration Info')}</h2>
                </div>
                <div class="info-content">
                    {include file="$_path/../pages/Registration_Info.html"}
                </div>
            </div>

            <form enctype="multipart/form-data" action="{Text::url('register/post')}" method="post" class="register-form" id="registerForm">
                {if isset($notify)}
                    <div class="alert alert-{if $notify_t == 's'}success{elseif $notify_t == 'i'}info{elseif $notify_t == 'w'}warning{else}danger{/if}" id="registerNotify">
                        <div class="alert-content">
                            <i class="fa {if $notify_t == 's'}fa-check-circle{elseif $notify_t == 'i'}fa-info-circle{elseif $notify_t == 'w'}fa-exclamation-triangle{else}fa-times-circle{/if}"></i>
                            <span class="alert-message">{$notify}</span>
                        </div>
                        {if $notify_t == 'd'}
                            <div class="alert-suggestion">
                                <small><i class="fa fa-info-circle"></i> Please correct the errors above and try again.</small>
                            </div>
                        {/if}
                    </div>
                {/if}

                <div class="form-section">
                    <h3 class="section-title">
                        <span class="section-number">1</span>
                        {Lang::T('Register as Member')}
                    </h3>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">
                                {if $_c['registration_username'] == 'phone'}
                                    {Lang::T('Phone Number')}
                                {elseif $_c['registration_username'] == 'email'}
                                    {Lang::T('Email')}
                                {else}
                                    {Lang::T('Usernames')}
                                {/if}
                            </label>
                            <div class="form-input-group">
                                <input type="text"
                                       required
                                       class="form-input"
                                       name="username"
                                       placeholder="{if $_c['country_code_phone']!= '' || $_c['registration_username'] == 'phone'}{$_c['country_code_phone']} {Lang::T('Phone Number')}{elseif $_c['registration_username'] == 'email'}{Lang::T('Email')}{else}{Lang::T('Usernames')}{/if}">
                                <i class="fa {if $_c['registration_username'] == 'phone'}fa-phone{elseif $_c['registration_username'] == 'email'}fa-envelope{else}fa-user{/if} form-icon"></i>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">{Lang::T('Full Name')}</label>
                            <div class="form-input-group">
                                <input type="text"
                                       {if $_c['man_fields_fname'] neq 'no'}required{/if}
                                       class="form-input"
                                       id="fullname"
                                       value="{$fullname}"
                                       name="fullname"
                                       placeholder="{Lang::T('Full Name')}">
                                <i class="fa fa-user form-icon"></i>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">{Lang::T('Email')}</label>
                            <div class="form-input-group">
                                <input type="email"
                                       {if $_c['man_fields_email'] neq 'no'}required{/if}
                                       class="form-input"
                                       id="email"
                                       placeholder="<EMAIL>"
                                       value="{$email}"
                                       name="email">
                                <i class="fa fa-envelope form-icon"></i>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">{Lang::T('Home Address')}</label>
                            <div class="form-input-group">
                                <input type="text"
                                       {if $_c['man_fields_address'] neq 'no'}required{/if}
                                       name="address"
                                       id="address"
                                       value="{$address}"
                                       class="form-input"
                                       placeholder="{Lang::T('Home Address')}">
                                <i class="fa fa-home form-icon"></i>
                            </div>
                        </div>

                        {if $_c['photo_register'] == 'yes'}
                            <div class="form-group">
                                <label class="form-label">{Lang::T('Photo')}</label>
                                <div class="file-input-wrapper">
                                    <input type="file" required class="file-input" id="photo" name="photo" accept="image/*">
                                    <label for="photo" class="file-input-label">
                                        <i class="fa fa-camera"></i>
                                        <span class="file-input-text">Choose photo file...</span>
                                    </label>
                                </div>
                            </div>
                        {/if}
                    </div>

                    {$customFields}
                </div>

                <div class="form-section">
                    <h3 class="section-title">
                        <span class="section-number">2</span>
                        {Lang::T('Password')}
                    </h3>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">{Lang::T('Password')}</label>
                            <div class="form-input-group">
                                <input type="password"
                                       required
                                       class="form-input"
                                       id="password"
                                       name="password"
                                       placeholder="{Lang::T('Password')}">
                                <i class="fa fa-lock form-icon"></i>
                            </div>
                            <div class="password-strength">
                                <div class="password-strength-bar" id="passwordStrengthBar"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">{Lang::T('Confirm Password')}</label>
                            <div class="form-input-group">
                                <input type="password"
                                       required
                                       class="form-input"
                                       id="cpassword"
                                       name="cpassword"
                                       placeholder="{Lang::T('Confirm Password')}">
                                <i class="fa fa-lock form-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <a href="{Text::url('login')}" class="btn-cancel">
                        <i class="fa fa-arrow-left" style="margin-right: 8px;"></i>
                        {Lang::T('Cancel')}
                    </a>
                    <button class="btn-register" type="submit" id="registerButton">
                        <span id="registerText">
                            <i class="fa fa-user-plus" style="margin-right: 8px;"></i>
                            {Lang::T('Register')}
                        </span>
                    </button>
                </div>

                <div class="policy-links">
                    <a href="javascript:showPrivacy()">Privacy Policy</a>
                    <span class="policy-separator">•</span>
                    <a href="javascript:showTaC()">Terms & Conditions</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Modern Modal -->
    <div class="modal fade" id="HTMLModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="border-radius: 16px; border: none; overflow: hidden;">
                <div class="modal-header" style="background: linear-gradient(135deg, #00BFA5, #4DB6AC); color: white; border: none;">
                    <h4 class="modal-title" style="font-weight: 600;">Information</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="HTMLModal_konten" style="padding: 24px; line-height: 1.6;"></div>
                <div class="modal-footer" style="border: none; padding: 16px 24px;">
                    <button type="button" class="btn btn-default" data-dismiss="modal" style="border-radius: 8px; padding: 8px 20px;">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const registerButton = document.getElementById('registerButton');
            const registerText = document.getElementById('registerText');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('cpassword');
            const passwordStrengthBar = document.getElementById('passwordStrengthBar');
            const fileInput = document.getElementById('photo');

            // Handle notifications
            const registerNotify = document.getElementById('registerNotify');
            if (registerNotify) {
                // Auto-dismiss notification after 10 seconds
                setTimeout(() => {
                    registerNotify.style.opacity = '0';
                    registerNotify.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        registerNotify.style.display = 'none';
                    }, 300);
                }, 10000);
            }

            // Form submission handling
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                    return false;
                }

                registerButton.classList.add('loading');
                registerText.style.opacity = '0';
                registerButton.disabled = true;

                // Add loading animation
                registerButton.innerHTML = '<div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>';
            });

            // Password strength checker
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                updatePasswordStrengthBar(strength);
            });

            // Password confirmation checker
            confirmPasswordInput.addEventListener('input', function() {
                validatePasswordMatch();
            });

            passwordInput.addEventListener('input', function() {
                if (confirmPasswordInput.value) {
                    validatePasswordMatch();
                }
            });

            // File input handler
            if (fileInput) {
                fileInput.addEventListener('change', function() {
                    const fileName = this.files[0] ? this.files[0].name : 'Choose photo file...';
                    const label = document.querySelector('.file-input-text');
                    if (label) {
                        label.textContent = fileName;
                    }
                });
            }

            // Enhanced form validation
            const inputs = form.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateInput(this);
                });

                input.addEventListener('input', function() {
                    if (this.style.borderColor === 'rgb(239, 68, 68)') {
                        this.style.borderColor = '#e2e8f0';
                        hideError(this);
                    }
                });

                // Enter key navigation
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextInput = getNextInput(this);
                        if (nextInput) {
                            nextInput.focus();
                        } else {
                            form.submit();
                        }
                    }
                });
            });

            function calculatePasswordStrength(password) {
                let strength = 0;

                if (password.length >= 8) strength += 1;
                if (password.match(/[a-z]/)) strength += 1;
                if (password.match(/[A-Z]/)) strength += 1;
                if (password.match(/[0-9]/)) strength += 1;
                if (password.match(/[^a-zA-Z0-9]/)) strength += 1;

                return Math.min(strength, 4);
            }

            function updatePasswordStrengthBar(strength) {
                const strengthClasses = ['', 'strength-weak', 'strength-fair', 'strength-good', 'strength-strong'];

                passwordStrengthBar.className = 'password-strength-bar';
                if (strength > 0) {
                    passwordStrengthBar.classList.add(strengthClasses[strength]);
                }
            }

            function validatePasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (confirmPassword && password !== confirmPassword) {
                    showError(confirmPasswordInput, 'Passwords do not match');
                    return false;
                } else {
                    hideError(confirmPasswordInput);
                    return true;
                }
            }

            function validateForm() {
                let isValid = true;

                inputs.forEach(input => {
                    if (!validateInput(input)) {
                        isValid = false;
                    }
                });

                if (!validatePasswordMatch()) {
                    isValid = false;
                }

                return isValid;
            }

            function validateInput(input) {
                const value = input.value.trim();
                let isValid = true;
                let errorMessage = '';

                if (input.hasAttribute('required') && value === '') {
                    isValid = false;
                    errorMessage = 'This field is required';
                } else if (input.type === 'email' && value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                } else if (input.type === 'password' && value) {
                    if (value.length < 6) {
                        isValid = false;
                        errorMessage = 'Password must be at least 6 characters';
                    }
                } else if (input.name === 'username' && value) {
                    if (value.length < 3) {
                        isValid = false;
                        errorMessage = 'Username must be at least 3 characters';
                    }
                }

                if (!isValid) {
                    showError(input, errorMessage);
                } else {
                    hideError(input);
                }

                return isValid;
            }

            function showError(input, message) {
                input.style.borderColor = '#ef4444';

                // Remove existing error
                hideError(input);

                // Add error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.style.cssText = 'color: #ef4444; font-size: 0.75rem; margin-top: 4px; font-weight: 500;';
                errorDiv.textContent = message;

                input.parentNode.parentNode.appendChild(errorDiv);
            }

            function hideError(input) {
                const errorMessage = input.parentNode.parentNode.querySelector('.error-message');
                if (errorMessage) {
                    errorMessage.remove();
                }
            }

            function getNextInput(currentInput) {
                const inputs = Array.from(form.querySelectorAll('.form-input'));
                const currentIndex = inputs.indexOf(currentInput);
                return inputs[currentIndex + 1] || null;
            }

            // Privacy and Terms functions
            window.showPrivacy = function() {
                fetch('{$app_url}/pages/Privacy_Policy.html')
                    .then(response => response.text())
                    .then(data => {
                        document.getElementById('HTMLModal_konten').innerHTML = data || '<p>Privacy Policy content not available.</p>';
                        $('#HTMLModal').modal('show');
                    })
                    .catch(error => {
                        document.getElementById('HTMLModal_konten').innerHTML = '<p>Privacy Policy content not available.</p>';
                        $('#HTMLModal').modal('show');
                    });
            };

            window.showTaC = function() {
                fetch('{$app_url}/pages/Terms_and_Conditions.html')
                    .then(response => response.text())
                    .then(data => {
                        document.getElementById('HTMLModal_konten').innerHTML = data || '<p>Terms and Conditions content not available.</p>';
                        $('#HTMLModal').modal('show');
                    })
                    .catch(error => {
                        document.getElementById('HTMLModal_konten').innerHTML = '<p>Terms and Conditions content not available.</p>';
                        $('#HTMLModal').modal('show');
                    });
            };

            // Add CSS animation for loading spinner
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                .loading {
                    pointer-events: none;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>

</html>