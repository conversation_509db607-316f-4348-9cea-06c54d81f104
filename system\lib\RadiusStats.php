<?php

/**
 * Radius Data Usage Statistics Helper Class
 * Provides comprehensive statistics and data analysis for Radius accounting
 */
class RadiusStats
{
    /**
     * Get main dashboard statistics
     */
    public static function getStatistics()
    {
        $stats = [];
        
        // Online Radius Users
        $stats['online_users'] = ORM::for_table('rad_acct')
            ->where('acctstatustype', 'Start')
            ->count();
        
        // Failed Login (last 24 hours)
        $yesterday = date('Y-m-d H:i:s', strtotime('-24 hours'));
        $stats['failed_logins'] = ORM::for_table('tbl_logs')
            ->where_like('type', '%failed%')
            ->where_gte('date', $yesterday)
            ->count();
        
        // Radius Status (check if any users are online)
        $stats['radius_status'] = $stats['online_users'] > 0 ? 'Online' : 'Offline';
        
        // Total Radius Accounts
        $stats['total_accounts'] = ORM::for_table('tbl_customers')
            ->join('tbl_user_recharges', ['tbl_customers.id', '=', 'tbl_user_recharges.customer_id'])
            ->where('tbl_user_recharges.status', 'on')
            ->count();
        
        return $stats;
    }
    
    /**
     * Get chart data for different time periods
     */
    public static function getChartData($type = 'daily')
    {
        switch ($type) {
            case 'daily':
                return self::getDailyUsage();
            case 'weekly':
                return self::getWeeklyUsage();
            case 'monthly':
                return self::getMonthlyUsage();
            default:
                return self::getDailyUsage();
        }
    }
    
    /**
     * Get daily usage data for last 30 days
     */
    private static function getDailyUsage()
    {
        $data = [];
        $labels = [];
        $downloads = [];
        $uploads = [];
        
        for ($i = 29; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $labels[] = date('M j', strtotime($date));
            
            $usage = ORM::for_table('rad_acct')
                ->where_like('dateAdded', $date . '%')
                ->find_array();
            
            $totalDownload = 0;
            $totalUpload = 0;
            
            foreach ($usage as $u) {
                $totalDownload += intval($u['acctoutputoctets']);
                $totalUpload += intval($u['acctinputoctets']);
            }
            
            $downloads[] = round($totalDownload / (1024 * 1024 * 1024), 2); // Convert to GB
            $uploads[] = round($totalUpload / (1024 * 1024 * 1024), 2); // Convert to GB
        }
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Download (GB)',
                    'data' => $downloads,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 2
                ],
                [
                    'label' => 'Upload (GB)',
                    'data' => $uploads,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                    'borderColor' => 'rgba(255, 99, 132, 1)',
                    'borderWidth' => 2
                ]
            ]
        ];
    }
    
    /**
     * Get weekly usage data for last 12 weeks
     */
    private static function getWeeklyUsage()
    {
        $data = [];
        $labels = [];
        $downloads = [];
        $uploads = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $startDate = date('Y-m-d', strtotime("-$i weeks monday"));
            $endDate = date('Y-m-d', strtotime("-$i weeks sunday"));
            $labels[] = date('M j', strtotime($startDate));
            
            $usage = ORM::for_table('rad_acct')
                ->where_gte('dateAdded', $startDate . ' 00:00:00')
                ->where_lte('dateAdded', $endDate . ' 23:59:59')
                ->find_array();
            
            $totalDownload = 0;
            $totalUpload = 0;
            
            foreach ($usage as $u) {
                $totalDownload += intval($u['acctoutputoctets']);
                $totalUpload += intval($u['acctinputoctets']);
            }
            
            $downloads[] = round($totalDownload / (1024 * 1024 * 1024), 2);
            $uploads[] = round($totalUpload / (1024 * 1024 * 1024), 2);
        }
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Download (GB)',
                    'data' => $downloads,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 2
                ],
                [
                    'label' => 'Upload (GB)',
                    'data' => $uploads,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                    'borderColor' => 'rgba(255, 99, 132, 1)',
                    'borderWidth' => 2
                ]
            ]
        ];
    }
    
    /**
     * Get monthly usage data for last 12 months
     */
    private static function getMonthlyUsage()
    {
        $data = [];
        $labels = [];
        $downloads = [];
        $uploads = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $date = date('Y-m', strtotime("-$i months"));
            $labels[] = date('M Y', strtotime($date . '-01'));
            
            $usage = ORM::for_table('rad_acct')
                ->where_like('dateAdded', $date . '%')
                ->find_array();
            
            $totalDownload = 0;
            $totalUpload = 0;
            
            foreach ($usage as $u) {
                $totalDownload += intval($u['acctoutputoctets']);
                $totalUpload += intval($u['acctinputoctets']);
            }
            
            $downloads[] = round($totalDownload / (1024 * 1024 * 1024), 2);
            $uploads[] = round($totalUpload / (1024 * 1024 * 1024), 2);
        }
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Download (GB)',
                    'data' => $downloads,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 2
                ],
                [
                    'label' => 'Upload (GB)',
                    'data' => $uploads,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                    'borderColor' => 'rgba(255, 99, 132, 1)',
                    'borderWidth' => 2
                ]
            ]
        ];
    }
    
    /**
     * Format bytes to human readable format
     */
    public static function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Get customer specific usage data
     */
    public static function getCustomerUsage($customerId, $startDate = null, $endDate = null)
    {
        $customer = ORM::for_table('tbl_customers')->find_one($customerId);
        if (!$customer) {
            return null;
        }
        
        $query = ORM::for_table('rad_acct')->where('username', $customer['username']);
        
        if ($startDate) {
            $query->where_gte('dateAdded', $startDate . ' 00:00:00');
        }
        
        if ($endDate) {
            $query->where_lte('dateAdded', $endDate . ' 23:59:59');
        }
        
        $usage = $query->find_array();
        
        $totalDownload = 0;
        $totalUpload = 0;
        $totalTime = 0;
        
        foreach ($usage as $u) {
            $totalDownload += intval($u['acctoutputoctets']);
            $totalUpload += intval($u['acctinputoctets']);
            $totalTime += intval($u['acctsessiontime']);
        }
        
        return [
            'download' => $totalDownload,
            'upload' => $totalUpload,
            'total' => $totalDownload + $totalUpload,
            'time' => $totalTime,
            'download_formatted' => self::formatBytes($totalDownload),
            'upload_formatted' => self::formatBytes($totalUpload),
            'total_formatted' => self::formatBytes($totalDownload + $totalUpload),
            'time_formatted' => self::formatTime($totalTime)
        ];
    }
    
    /**
     * Format seconds to human readable time
     */
    public static function formatTime($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;
        
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }
}
