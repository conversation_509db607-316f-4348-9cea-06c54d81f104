{if empty($_user)}
    {include file="customer/header-public.tpl"}
{else}
    {include file="customer/header.tpl"}
{/if}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.4/jspdf.min.js"></script>
<style>
    /* Enhanced Customer Invoice Preview Styles */
    .customer-invoice-preview {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .customer-invoice-content {
        background: white;
        border-radius: 6px;
        padding: 15px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
        min-height: 300px;
        border: 1px solid #e9ecef;
        text-align: center;
        background-image: url('{$app_url}/system/uploads/paid.png');
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
    }

    .customer-invoice-actions {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
        text-align: center;
    }

    .customer-invoice-actions .btn {
        margin: 5px;
    }

    .customer-invoice-url {
        margin-top: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }

    .customer-invoice-logo {
        text-align: center;
        margin-bottom: 20px;
    }

    .customer-invoice-logo img {
        max-width: 200px;
        height: auto;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 768px) {
        .customer-invoice-preview {
            margin: 10px -15px;
            border-radius: 0;
        }

        .customer-invoice-content {
            font-size: 12px;
            padding: 10px;
        }

        .customer-invoice-actions .btn {
            width: 100%;
            margin: 5px 0;
        }
    }
</style>
<div class="row">
    <div class="col-md-8 col-md-offset-2">
        <div class="panel panel-hovered panel-primary panel-stacked mb30">
            <div class="panel-heading">
                <i class="fa fa-file-text-o"></i> {$in['invoice']} - {Lang::T('Invoice')}
            </div>
            <div class="panel-body">
                <div class="customer-invoice-preview">
                    {if !empty($logo)}
                        <div class="customer-invoice-logo">
                            <img src="{$app_url}/{$logo}" alt="Company Logo">
                        </div>
                    {/if}

                    <pre class="customer-invoice-content" id="content">{$invoice}</pre>

                    <form class="form-horizontal" method="post" action="{Text::url('plan/print')}" target="_blank">
                        <input type="hidden" name="id" value="{$in['id']}">

                        <div class="customer-invoice-actions">
                            {if !empty($_user)}
                                <a href="{Text::url('voucher/list-activated')}" class="btn btn-default btn-sm">
                                    <i class="fa fa-arrow-left"></i> {Lang::T('Back')}
                                </a>
                            {/if}
                            <a href="javascript:download()" class="btn btn-success btn-sm">
                                <i class="fa fa-download"></i> {Lang::T('Download PDF')}
                            </a>
                            <a href="https://api.whatsapp.com/send/?text={$whatsapp}" class="btn btn-primary btn-sm" target="_blank">
                                <i class="fa fa-whatsapp"></i> {Lang::T('Share via WhatsApp')}
                            </a>
                        </div>

                        <div class="customer-invoice-url">
                            <label><i class="fa fa-link"></i> {Lang::T('Invoice URL')}:</label>
                            <input type="text" class="form-control" readonly onclick="this.select()" value="{$public_url}">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext('2d');
    ctx.font = '16px Courier';
    var text = document.getElementById("content").textContent || document.getElementById("content").innerText;
    var lines = text.split(/\r\n|\r|\n/).length;
    var meas = ctx.measureText("A");
    let width = Math.round({$_c['printer_cols']} * 9.6);
    var height = Math.round((14 * lines));
    console.log(width, height, lines);
    var paid = new Image();
    paid.src = '{$app_url}/system/uploads/paid.png';
    {if !empty($logo)}
        var img = new Image();
        img.src = '{$app_url}/{$logo}?{time()}';
        var new_width = (width / 4) * 2;
        var new_height = Math.ceil({$hlogo} * (new_width/{$wlogo}));
        height = height + new_height;
    {/if}

    function download() {
        var doc = new jsPDF('p', 'px', [width, height]);
        {if !empty($logo)}
            try {
                doc.addImage(img, 'PNG', (width - new_width) / 2, 10, new_width, new_height);
            } catch (err) {}
        {/if}
        try {
            doc.addImage(paid, 'PNG', (width - 200) / 2, (height - 145) / 2, 200, 145);
        } catch (err) {}
        doc.setFont("Courier");
        doc.setFontSize(16);
        doc.text($('#content').text(), width / 2, new_height + 30, 'center');
        doc.save('{$in['invoice']}.pdf');
    }
</script>
{include file="customer/footer.tpl"}