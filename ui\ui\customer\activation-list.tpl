{include file="customer/header.tpl"}
<!-- Modern Transaction History -->

<div class="row">
    <div class="col-sm-12">
        <div class="modern-card" style="padding: 24px;">
            <div class="panel-heading" style="margin-bottom: 24px;">
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #1e293b; margin: 0;">
                    <i class="fa fa-history" style="color: #00BFA5; margin-right: 8px;"></i>
                    {Lang::T('Transaction History List')}
                </h3>
            </div>

            {if $d && count($d) > 0}
                <!-- Desktop Table View -->
                <div class="table-responsive" style="display: none;" id="desktop-table">
                    <table class="table" style="margin-bottom: 0;">
                        <thead>
                            <tr style="background: #f8fafc;">
                                <th style="padding: 16px; font-weight: 600; color: #374151; border: none;">{Lang::T('Invoice')}</th>
                                <th style="padding: 16px; font-weight: 600; color: #374151; border: none;">{Lang::T('Package Name')}</th>
                                <th style="padding: 16px; font-weight: 600; color: #374151; border: none;">{Lang::T('Package Price')}</th>
                                <th style="padding: 16px; font-weight: 600; color: #374151; border: none;">{Lang::T('Type')}</th>
                                <th style="padding: 16px; font-weight: 600; color: #374151; border: none;">{Lang::T('Created On')}</th>
                                <th style="padding: 16px; font-weight: 600; color: #374151; border: none;">{Lang::T('Expires On')}</th>
                                <th style="padding: 16px; font-weight: 600; color: #374151; border: none;">{Lang::T('Method')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach $d as $ds}
                                <tr onclick="window.location.href = '{Text::url('voucher/invoice/')}{$ds.id|escape:'html'}'"
                                    style="cursor: pointer; transition: all 0.3s ease;"
                                    onmouseover="this.style.backgroundColor='#f8fafc'"
                                    onmouseout="this.style.backgroundColor='transparent'">
                                    <td style="padding: 16px; border: none; border-bottom: 1px solid #e2e8f0;">{$ds.invoice|escape:'html'}</td>
                                    <td style="padding: 16px; border: none; border-bottom: 1px solid #e2e8f0;">{$ds.plan_name|escape:'html'}</td>
                                    <td style="padding: 16px; border: none; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #00BFA5;">{Lang::moneyFormat($ds.price)}</td>
                                    <td style="padding: 16px; border: none; border-bottom: 1px solid #e2e8f0;">
                                        <span style="background: #e0f2fe; color: #0277bd; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                            {$ds.type|escape:'html'}
                                        </span>
                                    </td>
                                    <td style="padding: 16px; border: none; border-bottom: 1px solid #e2e8f0; font-size: 0.875rem; color: #64748b;">{Lang::dateAndTimeFormat($ds.recharged_on, $ds.recharged_time)}</td>
                                    <td style="padding: 16px; border: none; border-bottom: 1px solid #e2e8f0; font-size: 0.875rem; color: #64748b;">{Lang::dateAndTimeFormat($ds.expiration, $ds.time)}</td>
                                    <td style="padding: 16px; border: none; border-bottom: 1px solid #e2e8f0;">{$ds.method|escape:'html'}</td>
                                </tr>
                            {/foreach}
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card View -->
                <div id="mobile-cards">
                    {foreach $d as $ds}
                        <div class="modern-card"
                             style="margin-bottom: 16px; padding: 16px; cursor: pointer; transition: all 0.3s ease;"
                             onclick="window.location.href = '{Text::url('voucher/invoice/')}{$ds.id|escape:'html'}'"
                             onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(0, 0, 0, 0.1)'"
                             onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.05)'">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
                                <div>
                                    <div style="font-weight: 600; color: #1e293b; margin-bottom: 4px;">{$ds.plan_name|escape:'html'}</div>
                                    <div style="font-size: 0.875rem; color: #64748b;">#{$ds.invoice|escape:'html'}</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 600; color: #00BFA5; font-size: 1.125rem;">{Lang::moneyFormat($ds.price)}</div>
                                    <span style="background: #e0f2fe; color: #0277bd; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                        {$ds.type|escape:'html'}
                                    </span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; font-size: 0.75rem; color: #64748b;">
                                <div>
                                    <i class="fa fa-calendar" style="margin-right: 4px;"></i>
                                    {Lang::dateAndTimeFormat($ds.recharged_on, $ds.recharged_time)}
                                </div>
                                <div>
                                    <i class="fa fa-clock-o" style="margin-right: 4px;"></i>
                                    {Lang::dateAndTimeFormat($ds.expiration, $ds.time)}
                                </div>
                            </div>
                            <div style="margin-top: 8px; font-size: 0.75rem; color: #64748b;">
                                <i class="fa fa-credit-card" style="margin-right: 4px;"></i>
                                {$ds.method|escape:'html'}
                            </div>
                        </div>
                    {/foreach}
                </div>

                {include file="pagination.tpl"}
            {else}
                <div style="text-align: center; padding: 48px 24px;">
                    <div style="font-size: 4rem; color: #e2e8f0; margin-bottom: 16px;">
                        <i class="fa fa-history"></i>
                    </div>
                    <h3 style="color: #64748b; font-weight: 500; margin-bottom: 8px;">Belum Ada Transaksi</h3>
                    <p style="color: #9ca3af; margin-bottom: 24px;">Riwayat transaksi Anda akan muncul di sini setelah melakukan pembelian.</p>
                    <a href="{Text::url('home')}"
                       style="background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                              color: white;
                              padding: 12px 24px;
                              border-radius: 12px;
                              text-decoration: none;
                              font-weight: 600;
                              transition: all 0.3s ease;"
                       onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(0, 191, 165, 0.3)'"
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                        <i class="fa fa-home" style="margin-right: 8px;"></i>
                        Kembali ke Dashboard
                    </a>
                </div>
            {/if}
        </div>
    </div>
</div>

<script>
// Responsive table/card switching
function toggleView() {
    const desktopTable = document.getElementById('desktop-table');
    const mobileCards = document.getElementById('mobile-cards');

    if (window.innerWidth >= 768) {
        desktopTable.style.display = 'block';
        mobileCards.style.display = 'none';
    } else {
        desktopTable.style.display = 'none';
        mobileCards.style.display = 'block';
    }
}

// Initial check and resize listener
document.addEventListener('DOMContentLoaded', toggleView);
window.addEventListener('resize', toggleView);
</script>

{include file="customer/footer.tpl"}
