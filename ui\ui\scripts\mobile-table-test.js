/**
 * PHPNuxBill Mobile Table Test Suite
 * Tests all table functionality and mobile responsiveness
 * Author: PHPNuxBill Team
 * Version: 1.0.0
 */

(function() {
    'use strict';

    class MobileTableTester {
        constructor() {
            this.testResults = [];
            this.totalTests = 0;
            this.passedTests = 0;
            this.failedTests = 0;
        }

        // Run all tests
        runAllTests() {
            console.log('🧪 Starting PHPNuxBill Mobile Table Tests...');
            
            this.testTableDetection();
            this.testResponsiveContainers();
            this.testTouchScrolling();
            this.testColumnSorting();
            this.testRowSelection();
            this.testSearchFunctionality();
            this.testExportFeatures();
            this.testMobileOptimizations();
            this.testBrowserCompatibility();
            
            this.displayResults();
        }

        // Test 1: Table Detection
        testTableDetection() {
            this.log('Testing table detection...');
            
            const tables = document.querySelectorAll('table.table');
            const enhancedTables = document.querySelectorAll('.table-responsive.mobile-enhanced');
            
            this.assert(
                tables.length > 0,
                'Tables found on page',
                `Found ${tables.length} tables`
            );
            
            this.assert(
                enhancedTables.length > 0,
                'Tables enhanced for mobile',
                `Enhanced ${enhancedTables.length} tables`
            );
        }

        // Test 2: Responsive Containers
        testResponsiveContainers() {
            this.log('Testing responsive containers...');
            
            const responsiveContainers = document.querySelectorAll('.table-responsive');
            let hasHorizontalScroll = false;
            
            responsiveContainers.forEach(container => {
                const table = container.querySelector('table');
                if (table && table.scrollWidth > container.clientWidth) {
                    hasHorizontalScroll = true;
                }
            });
            
            this.assert(
                responsiveContainers.length > 0,
                'Responsive containers exist',
                `Found ${responsiveContainers.length} responsive containers`
            );
            
            this.assert(
                hasHorizontalScroll,
                'Horizontal scroll enabled',
                'Tables can scroll horizontally when needed'
            );
        }

        // Test 3: Touch Scrolling
        testTouchScrolling() {
            this.log('Testing touch scrolling...');
            
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            const responsiveContainers = document.querySelectorAll('.table-responsive');
            let touchEventsAttached = false;
            
            responsiveContainers.forEach(container => {
                // Check if touch events are properly attached
                const hasTouch = container.ontouchstart !== undefined || 
                               container.ontouchmove !== undefined;
                if (hasTouch) touchEventsAttached = true;
            });
            
            this.assert(
                isTouchDevice ? touchEventsAttached : true,
                'Touch scrolling support',
                isTouchDevice ? 'Touch events attached' : 'Desktop device - touch not required'
            );
        }

        // Test 4: Column Sorting
        testColumnSorting() {
            this.log('Testing column sorting...');
            
            const sortableHeaders = document.querySelectorAll('th.sortable');
            const hasClickHandlers = Array.from(sortableHeaders).some(header => {
                return header.onclick !== null || header.addEventListener;
            });
            
            this.assert(
                sortableHeaders.length > 0,
                'Sortable columns exist',
                `Found ${sortableHeaders.length} sortable columns`
            );
            
            this.assert(
                hasClickHandlers,
                'Sort click handlers attached',
                'Headers have click event listeners'
            );
        }

        // Test 5: Row Selection
        testRowSelection() {
            this.log('Testing row selection...');
            
            const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
            const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            
            this.assert(
                selectAllCheckbox !== null,
                'Select all checkbox exists',
                'Found select all checkbox in header'
            );
            
            this.assert(
                rowCheckboxes.length > 0,
                'Row checkboxes exist',
                `Found ${rowCheckboxes.length} row checkboxes`
            );
        }

        // Test 6: Search Functionality
        testSearchFunctionality() {
            this.log('Testing search functionality...');
            
            const searchInputs = document.querySelectorAll('.table-search-mobile input');
            const globalSearch = document.querySelector('#searchTerm, input[name="search"]');
            
            this.assert(
                searchInputs.length > 0 || globalSearch !== null,
                'Search functionality exists',
                searchInputs.length > 0 ? 
                    `Found ${searchInputs.length} table search inputs` : 
                    'Found global search input'
            );
        }

        // Test 7: Export Features
        testExportFeatures() {
            this.log('Testing export features...');
            
            const exportContainers = document.querySelectorAll('.table-export-mobile');
            const csvButtons = document.querySelectorAll('.table-export-mobile .btn');
            
            this.assert(
                exportContainers.length >= 0,
                'Export containers available',
                `Found ${exportContainers.length} export containers`
            );
            
            // Test CSV export functionality
            const canExportCSV = typeof window.MobileTableEnhancer !== 'undefined' &&
                               typeof window.MobileTableEnhancer.exportTableToCSV === 'function';
            
            this.assert(
                canExportCSV,
                'CSV export function available',
                'Export functionality is loaded'
            );
        }

        // Test 8: Mobile Optimizations
        testMobileOptimizations() {
            this.log('Testing mobile optimizations...');
            
            const isMobile = window.innerWidth <= 768;
            const mobileStyles = document.querySelector('style, link[href*="modern-components.css"]');
            
            this.assert(
                mobileStyles !== null,
                'Mobile CSS loaded',
                'Mobile optimization styles are available'
            );
            
            if (isMobile) {
                const stickyHeaders = document.querySelectorAll('.table-responsive thead th');
                let hasStickyHeaders = false;
                
                stickyHeaders.forEach(header => {
                    const styles = window.getComputedStyle(header);
                    if (styles.position === 'sticky') {
                        hasStickyHeaders = true;
                    }
                });
                
                this.assert(
                    hasStickyHeaders,
                    'Sticky headers on mobile',
                    'Table headers stick to top on mobile'
                );
            }
        }

        // Test 9: Browser Compatibility
        testBrowserCompatibility() {
            this.log('Testing browser compatibility...');
            
            const userAgent = navigator.userAgent;
            const isModernBrowser = 'querySelector' in document && 
                                  'addEventListener' in window &&
                                  'classList' in document.createElement('div');
            
            this.assert(
                isModernBrowser,
                'Modern browser features',
                'Browser supports required APIs'
            );
            
            // Test CSS Grid/Flexbox support
            const supportsFlexbox = CSS.supports('display', 'flex');
            const supportsGrid = CSS.supports('display', 'grid');
            
            this.assert(
                supportsFlexbox,
                'Flexbox support',
                'Browser supports CSS Flexbox'
            );
            
            this.log(`Browser: ${this.getBrowserInfo()}`);
        }

        // Helper method to get browser info
        getBrowserInfo() {
            const ua = navigator.userAgent;
            let browser = 'Unknown';
            
            if (ua.includes('Chrome')) browser = 'Chrome';
            else if (ua.includes('Firefox')) browser = 'Firefox';
            else if (ua.includes('Safari')) browser = 'Safari';
            else if (ua.includes('Edge')) browser = 'Edge';
            else if (ua.includes('Opera')) browser = 'Opera';
            
            return browser;
        }

        // Test assertion helper
        assert(condition, testName, message) {
            this.totalTests++;
            
            if (condition) {
                this.passedTests++;
                this.testResults.push({
                    status: 'PASS',
                    test: testName,
                    message: message
                });
                console.log(`✅ ${testName}: ${message}`);
            } else {
                this.failedTests++;
                this.testResults.push({
                    status: 'FAIL',
                    test: testName,
                    message: message
                });
                console.error(`❌ ${testName}: ${message}`);
            }
        }

        // Logging helper
        log(message) {
            console.log(`📋 ${message}`);
        }

        // Display test results
        displayResults() {
            console.log('\n📊 Test Results Summary:');
            console.log(`Total Tests: ${this.totalTests}`);
            console.log(`Passed: ${this.passedTests} ✅`);
            console.log(`Failed: ${this.failedTests} ❌`);
            console.log(`Success Rate: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
            
            if (this.failedTests > 0) {
                console.log('\n❌ Failed Tests:');
                this.testResults.filter(r => r.status === 'FAIL').forEach(result => {
                    console.log(`  - ${result.test}: ${result.message}`);
                });
            }
            
            // Create visual report
            this.createVisualReport();
        }

        // Create visual test report
        createVisualReport() {
            const reportContainer = document.createElement('div');
            reportContainer.id = 'mobile-table-test-report';
            reportContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 300px;
                max-height: 400px;
                background: white;
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 12px;
                overflow-y: auto;
            `;
            
            const successRate = ((this.passedTests / this.totalTests) * 100).toFixed(1);
            const statusColor = successRate >= 90 ? '#28a745' : successRate >= 70 ? '#ffc107' : '#dc3545';
            
            reportContainer.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4 style="margin: 0; color: #007bff;">Mobile Table Tests</h4>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
                </div>
                <div style="background: ${statusColor}; color: white; padding: 8px; border-radius: 4px; text-align: center; margin-bottom: 10px;">
                    ${successRate}% Success Rate
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>Results:</strong> ${this.passedTests}/${this.totalTests} passed
                </div>
                <div style="max-height: 200px; overflow-y: auto;">
                    ${this.testResults.map(result => `
                        <div style="padding: 4px 0; border-bottom: 1px solid #eee;">
                            <span style="color: ${result.status === 'PASS' ? '#28a745' : '#dc3545'};">
                                ${result.status === 'PASS' ? '✅' : '❌'}
                            </span>
                            <strong>${result.test}</strong><br>
                            <small style="color: #666;">${result.message}</small>
                        </div>
                    `).join('')}
                </div>
                <div style="margin-top: 10px; text-align: center;">
                    <button onclick="window.MobileTableTester.runAllTests()" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                        Run Tests Again
                    </button>
                </div>
            `;
            
            // Remove existing report
            const existingReport = document.getElementById('mobile-table-test-report');
            if (existingReport) {
                existingReport.remove();
            }
            
            document.body.appendChild(reportContainer);
        }

        // Performance test
        testPerformance() {
            const startTime = performance.now();
            
            // Simulate table operations
            const tables = document.querySelectorAll('table.table');
            tables.forEach(table => {
                // Simulate sorting
                const rows = table.querySelectorAll('tbody tr');
                // Simulate search
                const cells = table.querySelectorAll('td');
            });
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            this.assert(
                duration < 1000,
                'Performance test',
                `Operations completed in ${duration.toFixed(2)}ms`
            );
        }
    }

    // Initialize tester
    window.MobileTableTester = new MobileTableTester();

    // Auto-run tests when page loads (only in development)
    if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('🚀 Auto-running mobile table tests...');
                window.MobileTableTester.runAllTests();
            }, 2000);
        });
    }

    // Add keyboard shortcut to run tests (Ctrl+Shift+T)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            window.MobileTableTester.runAllTests();
        }
    });

    console.log('📱 Mobile Table Test Suite loaded. Press Ctrl+Shift+T to run tests.');

})();
