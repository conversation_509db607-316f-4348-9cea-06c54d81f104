{if $_bills}
    {foreach $_bills as $_bill}
        <!-- Modern Plan Card -->
        <div class="modern-plan-card" style="
            background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 24px;
            color: white;
            box-shadow: 0 8px 32px rgba(33, 150, 243, 0.3);
            position: relative;
            overflow: hidden;
        ">
            <!-- Background Pattern -->
            <div style="
                position: absolute;
                top: -50px;
                right: -50px;
                width: 150px;
                height: 150px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                z-index: 1;
            "></div>

            <!-- Header Section -->
            <div style="position: relative; z-index: 2; margin-bottom: 20px;">
                <div class="row" style="margin: 0; align-items: center;">
                    <div class="col-xs-8" style="padding: 0;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            {if $_bill['type'] == 'Hotspot'}
                                <i class="fa fa-wifi" style="font-size: 1.5rem; opacity: 0.9;"></i>
                            {elseif $_bill['type'] == 'PPPOE'}
                                <i class="fa fa-link" style="font-size: 1.5rem; opacity: 0.9;"></i>
                            {elseif $_bill['type'] == 'VPN'}
                                <i class="fa fa-shield" style="font-size: 1.5rem; opacity: 0.9;"></i>
                            {else}
                                <i class="fa fa-globe" style="font-size: 1.5rem; opacity: 0.9;"></i>
                            {/if}
                            <h3 style="margin: 0; font-size: 1.25rem; font-weight: 600;">
                                {if $_bill['routers'] != 'radius'}
                                    {$_bill['namebp']}
                                {else}
                                    {if $_c['radius_plan']==''}Radius Plan{else}{$_c['radius_plan']}{/if}
                                {/if}
                            </h3>
                        </div>
                    </div>
                    <div class="col-xs-4 text-right" style="padding: 0;">
                        {if $_bill['status'] == 'on'}
                            <span style="
                                background: rgba(76, 175, 80, 0.2);
                                color: #4CAF50;
                                padding: 6px 12px;
                                border-radius: 20px;
                                font-size: 0.75rem;
                                font-weight: 600;
                                border: 1px solid rgba(76, 175, 80, 0.3);
                            ">
                                <i class="fa fa-check-circle" style="margin-right: 4px;"></i>
                                {Lang::T('Active')}
                            </span>
                        {else}
                            <span style="
                                background: rgba(244, 67, 54, 0.2);
                                color: #F44336;
                                padding: 6px 12px;
                                border-radius: 20px;
                                font-size: 0.75rem;
                                font-weight: 600;
                                border: 1px solid rgba(244, 67, 54, 0.3);
                            ">
                                <i class="fa fa-times-circle" style="margin-right: 4px;"></i>
                                {Lang::T('Expired')}
                            </span>
                        {/if}
                    </div>
                </div>
            </div>
            <!-- Plan Details Section -->
            <div style="position: relative; z-index: 2;">
                <!-- Period Section -->
                <div style="margin-bottom: 20px;">
                    <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 8px; font-weight: 500;">
                        {Lang::T('Period')}
                    </div>
                    <div style="font-size: 1.125rem; font-weight: 600; line-height: 1.4;">
                        {if $_bill['time'] ne ''}
                            {Lang::dateAndTimeFormat($_bill['recharged_on'],$_bill['recharged_time'])} - {Lang::dateAndTimeFormat($_bill['expiration'],$_bill['time'])}
                        {/if}
                    </div>
                </div>

                <!-- Divider -->
                <div style="
                    height: 1px;
                    background: rgba(255, 255, 255, 0.2);
                    margin: 20px 0;
                "></div>

                <!-- Details Grid -->
                <div class="row" style="margin: 0;">
                    <div class="col-xs-6" style="padding: 0 8px 0 0;">
                        <div style="margin-bottom: 16px;">
                            <div style="font-size: 0.875rem; opacity: 0.8; margin-bottom: 4px;">
                                {Lang::T('Type')}
                            </div>
                            <div style="font-size: 1rem; font-weight: 600;">
                                {if $_bill['prepaid'] eq yes}Prepaid{else}Postpaid{/if}
                            </div>
                        </div>
                        {if $_c['show_bandwidth_plan'] == 'yes'}
                        <div style="margin-bottom: 16px;">
                            <div style="font-size: 0.875rem; opacity: 0.8; margin-bottom: 4px;">
                                {Lang::T('Bandwidth')}
                            </div>
                            <div style="font-size: 1rem; font-weight: 600;">
                                {$_bill['name_bw']}
                            </div>
                        </div>
                        {/if}
                    </div>
                    <div class="col-xs-6" style="padding: 0 0 0 8px;">
                        <div style="margin-bottom: 16px;">
                            <div style="font-size: 0.875rem; opacity: 0.8; margin-bottom: 4px;">
                                {Lang::T('Status')}
                            </div>
                            <div style="font-size: 1rem; font-weight: 600;">
                                {if $_bill['status'] == 'on'}
                                    <i class="fa fa-check-circle" style="color: #4CAF50; margin-right: 4px;"></i>
                                    {Lang::T('Active')}
                                {else}
                                    <i class="fa fa-times-circle" style="color: #F44336; margin-right: 4px;"></i>
                                    {Lang::T('Expired')}
                                {/if}
                            </div>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <div style="font-size: 0.875rem; opacity: 0.8; margin-bottom: 4px;">
                                {Lang::T('Due Date')}
                            </div>
                            <div style="font-size: 1rem; font-weight: 600;">
                                {if $_bill['time'] ne ''}
                                    {Lang::dateAndTimeFormat($_bill['expiration'],$_bill['time'])}
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>

                {* VPN Information Section *}
                {if $_bill['type'] == 'VPN' && $_bill['routers'] == $vpn['routers']}
                    <!-- Divider -->
                    <div style="height: 1px; background: rgba(255, 255, 255, 0.2); margin: 20px 0;"></div>

                    <div style="margin-bottom: 16px;">
                        <div style="font-size: 0.875rem; opacity: 0.8; margin-bottom: 8px;">VPN Information</div>
                        <div class="row" style="margin: 0;">
                            <div class="col-xs-6" style="padding: 0 8px 0 0;">
                                <div style="font-size: 0.75rem; opacity: 0.7; margin-bottom: 2px;">{Lang::T('Public IP')}</div>
                                <div style="font-size: 0.875rem; font-weight: 500;">{$vpn['public_ip']} / {$vpn['port_name']}</div>
                            </div>
                            <div class="col-xs-6" style="padding: 0 0 0 8px;">
                                <div style="font-size: 0.75rem; opacity: 0.7; margin-bottom: 2px;">{Lang::T('Private IP')}</div>
                                <div style="font-size: 0.875rem; font-weight: 500;">{$_user['pppoe_ip']}</div>
                            </div>
                        </div>
                        {foreach $cf as $tcf}
                            {if $tcf['field_name'] == 'Winbox' or $tcf['field_name'] == 'Api' or $tcf['field_name'] == 'Web'}
                                <div style="margin-top: 8px;">
                                    <div style="font-size: 0.75rem; opacity: 0.7; margin-bottom: 2px;">{$tcf['field_name']} Port</div>
                                    <a href="http://{$vpn['public_ip']}:{$tcf['field_value']}" target="_blank"
                                       style="color: white; text-decoration: underline; font-size: 0.875rem; font-weight: 500;">
                                        {$tcf['field_value']}
                                    </a>
                                </div>
                            {/if}
                        {/foreach}
                    </div>
                {/if}

                {* Current Connection Info *}
                {if $nux_ip neq '' || $nux_mac neq ''}
                    <!-- Divider -->
                    <div style="height: 1px; background: rgba(255, 255, 255, 0.2); margin: 20px 0;"></div>

                    <div style="margin-bottom: 16px;">
                        <div style="font-size: 0.875rem; opacity: 0.8; margin-bottom: 8px;">Current Connection</div>
                        <div class="row" style="margin: 0;">
                            {if $nux_ip neq ''}
                            <div class="col-xs-6" style="padding: 0 8px 0 0;">
                                <div style="font-size: 0.75rem; opacity: 0.7; margin-bottom: 2px;">{Lang::T('Current IP')}</div>
                                <div style="font-size: 0.875rem; font-weight: 500;">{$nux_ip}</div>
                            </div>
                            {/if}
                            {if $nux_mac neq ''}
                            <div class="col-xs-6" style="padding: 0 0 0 8px;">
                                <div style="font-size: 0.75rem; opacity: 0.7; margin-bottom: 2px;">{Lang::T('Current MAC')}</div>
                                <div style="font-size: 0.875rem; font-weight: 500;">{$nux_mac}</div>
                            </div>
                            {/if}
                        </div>
                    </div>
                {/if}

                {* Login Status for Hotspot *}
                {if $_bill['type'] == 'Hotspot' && $_bill['status'] == 'on'}
                    <!-- Divider -->
                    <div style="height: 1px; background: rgba(255, 255, 255, 0.2); margin: 20px 0;"></div>

                    <div style="margin-bottom: 16px;">
                        <div style="font-size: 0.875rem; opacity: 0.8; margin-bottom: 8px;">{Lang::T('Login Status')}</div>
                        {if $_bill['routers'] != 'radius' && $_c['hs_auth_method'] != 'hchap'}
                            <div id="login_status_{$_bill['id']}" style="text-align: center;">
                                <img src="{$app_url}/ui/ui/images/loading.gif" style="width: 20px; height: 20px;">
                            </div>
                        {elseif $_c['hs_auth_method'] == 'hchap'}
                            {if $logged == '1'}
                                <a href="http://{$hostname}/status" style="
                                    display: block;
                                    background: rgba(76, 175, 80, 0.2);
                                    color: #4CAF50;
                                    padding: 12px;
                                    border-radius: 12px;
                                    text-decoration: none;
                                    text-align: center;
                                    font-weight: 500;
                                    border: 1px solid rgba(76, 175, 80, 0.3);
                                ">
                                    <i class="fa fa-check-circle" style="margin-right: 8px;"></i>
                                    {Lang::T('You are Online, Check Status')}
                                </a>
                            {else}
                                <a href="{Text::url('home&mikrotik=login')}"
                                   onclick="return ask(this, '{Lang::T('Connect to Internet')}')" style="
                                    display: block;
                                    background: rgba(244, 67, 54, 0.2);
                                    color: #F44336;
                                    padding: 12px;
                                    border-radius: 12px;
                                    text-decoration: none;
                                    text-align: center;
                                    font-weight: 500;
                                    border: 1px solid rgba(244, 67, 54, 0.3);
                                ">
                                    <i class="fa fa-times-circle" style="margin-right: 8px;"></i>
                                    {Lang::T('Not Online, Login now?')}
                                </a>
                            {/if}
                        {/if}
                    </div>
                {/if}

                <!-- Action Buttons Section -->
                <div style="margin-top: 24px;">
                    <div class="row" style="margin: 0; gap: 12px;">
                        {if $_bill['status'] != 'on' && $_bill['prepaid'] != 'yes' && $_c['extend_expired']}
                        <div class="col-xs-4" style="padding: 0;">
                            <a href="{Text::url('home&extend=', $_bill['id'], '&stoken=', App::getToken())}"
                               onclick="return ask(this, '{Text::toHex($_c['extend_confirmation'])}')" style="
                                display: block;
                                background: rgba(255, 193, 7, 0.2);
                                color: #FFC107;
                                padding: 12px;
                                border-radius: 12px;
                                text-decoration: none;
                                text-align: center;
                                font-weight: 500;
                                border: 1px solid rgba(255, 193, 7, 0.3);
                                font-size: 0.875rem;
                            ">
                                <i class="fa fa-clock-o" style="margin-right: 6px;"></i>
                                {Lang::T('Extend')}
                            </a>
                        </div>
                        {/if}

                        <div class="col-xs-4" style="padding: 0;">
                            <a href="{Text::url('home&sync=', $_bill['id'], '&stoken=', App::getToken())}"
                               onclick="return ask(this, '{Lang::T('Sync account if you failed login to internet')}?')"
                               data-toggle="tooltip" data-placement="top"
                               title="{Lang::T('Sync account if you failed login to internet')}" style="
                                display: block;
                                background: rgba(156, 39, 176, 0.2);
                                color: #9C27B0;
                                padding: 12px;
                                border-radius: 12px;
                                text-decoration: none;
                                text-align: center;
                                font-weight: 500;
                                border: 1px solid rgba(156, 39, 176, 0.3);
                                font-size: 0.875rem;
                            ">
                                <i class="fa fa-refresh" style="margin-right: 6px;"></i>
                                {Lang::T('Sync')}
                            </a>
                        </div>

                        <div class="col-xs-4" style="padding: 0;">
                            <a href="{Text::url('home&recharge=', $_bill['id'], '&stoken=', App::getToken())}"
                               onclick="return ask(this, '{Lang::T('Recharge')}?')" style="
                                display: block;
                                background: rgba(76, 175, 80, 0.2);
                                color: #4CAF50;
                                padding: 12px;
                                border-radius: 12px;
                                text-decoration: none;
                                text-align: center;
                                font-weight: 500;
                                border: 1px solid rgba(76, 175, 80, 0.3);
                                font-size: 0.875rem;
                            ">
                                <i class="fa fa-credit-card" style="margin-right: 6px;"></i>
                                {Lang::T('Recharge')}
                            </a>
                        </div>

                        {if $_bill['status'] == 'on' && $_bill['prepaid'] != 'YES'}
                        <div class="col-xs-12" style="padding: 0; margin-top: 12px;">
                            <a href="{Text::url('home&deactivate=', $_bill['id'])}"
                               onclick="return ask(this, '{Lang::T('Deactivate')}?')" style="
                                display: block;
                                background: rgba(244, 67, 54, 0.2);
                                color: #F44336;
                                padding: 12px;
                                border-radius: 12px;
                                text-decoration: none;
                                text-align: center;
                                font-weight: 500;
                                border: 1px solid rgba(244, 67, 54, 0.3);
                                font-size: 0.875rem;
                            ">
                                <i class="fa fa-trash" style="margin-right: 6px;"></i>
                                {Lang::T('Deactivate')}
                            </a>
                        </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    {/foreach}

    {* JavaScript for Login Status *}
    {foreach $_bills as $_bill}
        {if $_bill['type'] == 'Hotspot' && $_bill['status'] == 'on' && $_c['hs_auth_method'] != 'hchap'}
            <script>
                setTimeout(() => {
                    $.ajax({
                        url: "{Text::url('autoload_user/isLogin/')}{$_bill['id']}",
                        cache: false,
                        success: function(msg) {
                            $("#login_status_{$_bill['id']}").html(msg);
                        }
                    });
                }, 2000);
            </script>
        {/if}
    {/foreach}
{/if}

<!-- Mobile Responsive Styles -->
<style>
@media (max-width: 768px) {
    .modern-plan-card {
        margin-bottom: 16px !important;
        padding: 20px !important;
    }

    .modern-plan-card h3 {
        font-size: 1.125rem !important;
    }

    .modern-plan-card .col-xs-6 {
        margin-bottom: 12px;
    }

    .modern-plan-card .row[style*="gap: 12px"] .col-xs-4 {
        width: 100% !important;
        margin-bottom: 8px;
    }
}

@media (max-width: 480px) {
    .modern-plan-card {
        padding: 16px !important;
        border-radius: 16px !important;
    }

    .modern-plan-card h3 {
        font-size: 1rem !important;
    }

    .modern-plan-card div[style*="font-size: 1.125rem"] {
        font-size: 1rem !important;
    }

    .modern-plan-card div[style*="font-size: 1rem"] {
        font-size: 0.875rem !important;
    }
}
</style>