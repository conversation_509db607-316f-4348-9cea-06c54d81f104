<?php

/**
 *  PHP Mikrotik Billing (https://github.com/hotspotbilling/phpnuxbill/)
 *  by https://t.me/ibnux
 **/

header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Expires: <PERSON><PERSON>, 01 Jan 2000 00:00:00 GMT");
header("Pragma: no-cache");

run_hook('customer_logout'); #HOOK
if (session_status() == PHP_SESSION_NONE) session_start();

// Get user info before destroying session
$user_info = '';
if (isset($_SESSION['aid'])) {
    $admin = ORM::for_table('tbl_users')->find_one($_SESSION['aid']);
    if ($admin) {
        $user_info = htmlspecialchars($admin['fullname']) . ' (' . Lang::T($admin['user_type']) . ')';
    }
} elseif (isset($_SESSION['uid'])) {
    $customer = ORM::for_table('tbl_customers')->find_one($_SESSION['uid']);
    if ($customer) {
        $user_info = htmlspecialchars($customer['fullname']) . ' (' . Lang::T('Customer') . ')';
    }
}

Admin::removeCookie();
User::removeCookie();
session_destroy();

$logout_msg = Lang::T('Logout Successful') . '! ';
if ($user_info) {
    $logout_msg .= Lang::T('Thank you') . ', ' . $user_info . '. ' . Lang::T('You have been safely logged out') . '.';
} else {
    $logout_msg .= Lang::T('You have been safely logged out') . '.';
}

_alert($logout_msg, 'warning', "login");
