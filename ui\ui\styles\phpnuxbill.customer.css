/* Modern PHPNuxBill Customer Interface */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    line-height: 1.5;
    font-weight: 400;
    color: #334155;
    min-height: 100vh;
}

/* Modern Header Styling */
.modern-skin-dark .main-header .logo {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: #fff;
    border-radius: 0 0 20px 0;
    box-shadow: 0 4px 20px rgba(0, 191, 165, 0.3);
}

.modern-skin-dark .main-header .navbar {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    border: none;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.modern-skin-dark .main-sidebar .sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    bottom: 0;
    border-right: 1px solid #e2e8f0;
}

.modern-skin-dark .main-sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08);
}

.modern-skin-dark .main-header .navbar>a:focus,
.modern-skin-dark .main-header .navbar>a:active,
.modern-skin-dark .main-header .navbar>a:visited,
.modern-skin-dark .main-header .navbar>a:hover {
    background-color: rgb(28 36 52);
}

/* Modern Sidebar Menu */
.sidebar-menu li>a {
    position: relative;
    background-color: transparent;
    color: #64748b;
    border-radius: 12px;
    margin: 4px 12px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    border: none;
}

.sidebar-menu li>a:hover,
.sidebar-menu li:focus>a {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: #ffffff;
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(0, 191, 165, 0.3);
}

.modern-skin-dark .main-sidebar .sidebar .sidebar-menu li.active a {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: #ffffff;
    border-radius: 12px;
    margin: 4px 12px;
    box-shadow: 0 4px 15px rgba(0, 191, 165, 0.3);
}

.modern-skin-dark .main-sidebar .sidebar .sidebar-menu {
    background-color: transparent;
    padding: 20px 0;
}

.modern-skin-dark .main-sidebar .sidebar .sidebar-menu li .treeview-menu li.active a {
    background-color: transparent !important;
    color: rgb(84, 131, 227);
}

.modern-skin-dark .main-sidebar .sidebar .sidebar-menu li .treeview-menu li>a {
    background-color: transparent !important;
    padding: 10px 5px 5px 15px;
}

.modern-skin-dark .main-sidebar .sidebar .sidebar-menu li .treeview-menu {
    padding-left: 0;
    border-left: 3px solid #10d435;
}

.content-header {
    list-style-type: none;
    padding: 15px;
    background-color: #f6f9fc;

}

@media (max-width: 767px) {
    .content {
        padding: 0 15px !important;
        background-color: #f6f9fc;
    }
}

.content {
    padding: 25px !important;
    background-color: #f6f9fc;

}

.content-wrapper,
.right-side {
    min-height: 100%;
    background-color: #f6f9fc;
    z-index: 800;
}

.main-footer {
    background: rgb(28 36 52);
    padding: 15px;
    color: rgb(100 116 139);
    border-top: 1px solid #d2d6de;
}

.panel-primary {
    border-color: #333;
}

.panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: 0px solid transparent;
    border-radius: 21px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: 0px 4px 30px rgba(221, 224, 255, .54);
}

.panel-primary>.panel-heading {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.panel-primary>.panel-heading {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
}

.box.box-solid.box-primary>.box-header {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.box-body {
    border-radius: 21px;
    padding: 10px;
}

.box.box-solid.box-primary {
    background-color: #fff;
    border: 0px solid transparent;
    border-radius: 21px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: 0px 4px 30px rgba(221, 224, 255, .54);
}

.content .row [class*=col-] .box {
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: 4px 4px 30px rgba(221, 224, 255, .54);
    -moz-box-shadow: 0 1px 1px rgba(0, 0, 0, .1);
    -ms-box-shadow: 0 1px 1px rgba(0, 0, 0, .1);
    -webkit-border-radius: 1px !important;
    -moz-border-radius: 1px !important;
    -ms-border-radius: 1px !important;
    border-radius: 15px !important;
    border-color: rgba(221, 224, 255, .54);
}

.box.box-solid.box-info>.box-header {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.box-header {
    color: inherit;
    display: block;
    padding: 10px;
    position: relative;
    border-color: transparent;
    border-radius: 25px;
}

.box.box-solid.box-default>.box-header {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.box.box-solid.box-success>.box-header {
    color: inherit;
    background: transparent;
    background-color: transparent;
}

.box.box-solid.box-primary>.box-header {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.box.box-solid.box-info>.box-header {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.box.box-solid.box-danger>.box-header {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.box.box-solid.box-warning>.box-header {
    color: inherit;
    background-color: transparent;
    border-color: transparent;
}

.box {
    position: relative;
    border-radius: 15px;
    margin-bottom: 20px;
    width: 100%;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: 0px 4px 30px rgba(221, 224, 255, .54);
}

.modern-skin-dark .main-sidebar .sidebar .sidebar-menu li>a {
    font-weight: bold;
}

.content-header>h1 {
    font-weight: bold;
}

.box-header>.fa,
.box-header>.glyphicon,
.box-header>.ion,
.box-header .box-title {
    font-weight: bold;
}

.main-header .logo .logo-lg {
    font-weight: bold;
}

/* New Customize Interface End Here */

::-moz-selection {
    /* Code for Firefox */
    color: red;
    background: yellow;
}

::selection {
    color: red;
    background: yellow;
}

.content-wrapper {
    margin-top: 50px;
}

@media (max-width: 767px) {
    .content-wrapper {
        margin-top: 100px;
    }
}


.loading {
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-left: 10px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s infinite linear;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.dropdown-menu .dropdown-item {
    margin-bottom: 5px;
}

.dropdown-menu .dropdown-item button {
    margin: 0;
    padding: 10px;
}




.toggle-container {
    cursor: pointer;
}

.toggle-container .toggle-icon {
    font-size: 25px;
    color: rgb(100 116 139);
    transition: color 0.5s ease;
}

@media (max-width: 600px) {

    .toggle-container .toggle-icon {
        font-size: 20px;
        color: rgb(100 116 139);
        transition: color 0.5s ease;
    }
}


/* dark mode styles start here */
.dark-mode {
    background-color: #1a202c;
    color: #cbd5e0;
}

.dark-mode .main-header .logo,
.dark-mode .main-header .navbar,
.dark-mode .main-sidebar,
.dark-mode .main-sidebar .sidebar,
.dark-mode .sidebar-menu li>a {
    background-color: #0e1219;
    color: #cbd5e0;
}

.dark-mode .sidebar-menu li:hover,
.dark-mode .sidebar-menu li:focus {
    color: #10d435;
}

.dark-mode .main-sidebar .sidebar .sidebar-menu li.active a {
    background-color: #2e298e;
}

.dark-mode .content,
.dark-mode .content-header,
.dark-mode .content-wrapper,
.dark-mode .right-side {
    background-color: #0e1219;
}

.dark-mode .main-footer {
    background-color: #1a202c;
    color: #cbd5e0;
}

.dark-mode .panel,
.dark-mode .box {
    background-color: #2d3748;
    border-color: #4a5568;
    box-shadow: none;
}

.dark-mode .panel-heading,
.dark-mode .box-header {
    background-color: transparent;
    color: #cbd5e0;
}

.dark-mode .box-footer,
.dark-mode .panel-footer {
    background-color: #2d3748;
}

.dark-mode .search-container {
    background-color: #2d3748;
    color: #cbd5e0;
}

.dark-mode .searchTerm {
    background-color: #4a5568;
    color: #cbd5e0;
}

.dark-mode .cancelButton {
    background-color: #e53e3e;
}

.dark-mode .notification-top-bar {
    background-color: #742a2a;
}

.dark-mode .bs-callout {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #cbd5e0;
}

.dark-mode .bs-callout h4 {
    color: #cbd5e0;
}

.dark-mode .bg-gray {
    background-color: inherit !important;
}

.dark-mode .breadcrumb {
    padding: 8px 15px;
    margin-bottom: 20px;
    list-style: none;
    background-color: rgba(221, 224, 255, .54);
    border-radius: 4px;
}

.dark-mode .pagination>.disabled>span,
.dark-mode .pagination>.disabled>span:hover,
.dark-mode .pagination>.disabled>span:focus,
.dark-mode .pagination>.disabled>a,
.dark-mode .pagination>.disabled>a:hover,
.dark-mode .pagination>.disabled>a:focus {
    color: inherit;
    background-color: rgba(221, 224, 255, .54);
    border-color: rgba(221, 224, 255, .54);
    cursor: not-allowed;
}

.dark-mode .pagination>.active>a,
.dark-mode .pagination>.active>a:hover,
.dark-mode .pagination>.active>a:focus,
.dark-mode .pagination>.active>span,
.dark-mode .pagination>.active>span:hover,
.dark-mode .pagination>.active>span:focus {
    z-index: 2;
    color: #fff;
    background-color: #435ebe;
    border-color: rgba(221, 224, 255, .54);
    box-shadow: 0 2px 5px rgba(67, 94, 190, .3);
    cursor: default;
}

.dark-mode .pagination>li>a {
    background: inherit;
    color: inherit;
    border: 1px solid;
    border-color: rgba(221, 224, 255, .54);
}

.dark-mode .table {
    background-color: inherit;
    color: #ddd;
    border-color: #444;
}

.dark-mode .table th,
.dark-mode .table td {
    background-color: inherit;
    border-color: inherit;
    color: #ddd;
}

.dark-mode .table th {
    background-color: inherit;
    font-weight: bold;
}

/* Modern Dashboard Cards */
.modern-card {
    background: white;
    border-radius: 20px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.balance-card {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: white;
    text-align: center;
}

.balance-icon {
    font-size: 2.5rem;
    margin-bottom: 12px;
    opacity: 0.9;
}

.balance-label {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 8px;
    font-weight: 500;
}

.balance-amount {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.promo-cta {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.promo-cta:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Quick Actions Styling */
.quick-actions-widget {
    padding: 16px;
}

.quick-action-card {
    display: block;
    background: white;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 12px;
    text-decoration: none;
    color: #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-align: center;
    border: 1px solid #f0f0f0;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: #333;
}

.quick-action-card:focus {
    text-decoration: none;
    color: #333;
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px auto;
}

.quick-action-icon i {
    font-size: 24px;
    color: white;
}

.quick-action-label {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Greeting Section */
.greeting-section {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: white;
    padding: 24px 0;
    margin-bottom: 24px;
    border-radius: 0 0 24px 24px;
}

.greeting-text {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.3;
}

.user-name {
    font-weight: 700;
    color: rgba(255, 255, 255, 0.95);
}

.greeting-time {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 8px 0 0 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .modern-card {
        padding: 20px;
        margin-bottom: 16px;
        border-radius: 16px;
    }

    .balance-amount {
        font-size: 1.5rem;
    }

    .balance-icon {
        font-size: 2rem;
    }

    .quick-actions-widget {
        padding: 12px;
    }

    .quick-action-card {
        padding: 16px;
        margin-bottom: 8px;
        border-radius: 12px;
    }

    .quick-action-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        margin-bottom: 8px;
    }

    .quick-action-icon i {
        font-size: 20px;
    }

    .quick-action-label {
        font-size: 12px;
    }

    .greeting-section {
        padding: 20px 0;
        margin-bottom: 20px;
        border-radius: 0 0 20px 20px;
    }

    .greeting-text {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .modern-card {
        padding: 16px;
        margin-bottom: 12px;
    }

    .balance-amount {
        font-size: 1.25rem;
    }

    .balance-icon {
        font-size: 1.75rem;
    }

    .quick-actions-widget {
        padding: 8px;
    }

    .quick-action-card {
        padding: 12px;
        margin-bottom: 6px;
    }

    .quick-action-icon {
        width: 36px;
        height: 36px;
        margin-bottom: 6px;
    }

    .quick-action-icon i {
        font-size: 18px;
    }

    .quick-action-label {
        font-size: 11px;
    }

    .greeting-section {
        padding: 16px 0;
        margin-bottom: 16px;
        border-radius: 0 0 16px 16px;
    }

    .greeting-text {
        font-size: 1.125rem;
    }
}

.dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: inherit;
}

.dark-mode .table-bordered {
    border: 1px solid #444;
}

.dark-mode .table-hover tbody tr:hover {
    background-color: #555;
    color: #fff;
}

.dark-mode .table-condensed th,
.dark-mode .table-condensed td {
    padding: 8px;
}

.dark-mode .panel>.table:last-child,
.dark-mode .panel>.table-responsive:last-child>.table:last-child {
    border-bottom-right-radius: 21px;
    border-bottom-left-radius: 21px;
}

.dark-mode .panel>.table:last-child>tbody:last-child>tr:last-child,
.dark-mode .panel>.table:last-child>tfoot:last-child>tr:last-child,
.dark-mode .panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,
.dark-mode .panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child {
    border-bottom-right-radius: 21px;
    border-bottom-left-radius: 21px;
}

.dark-mode .panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.dark-mode .panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.dark-mode .panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.dark-mode .panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,
.dark-mode .panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.dark-mode .panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.dark-mode .panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.dark-mode .panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child {
    border-bottom-right-radius: 21px;
}

.dark-mode .help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: inherit;
}

.dark-mode .text-muted {
    color: rgba(221, 224, 255, .54);
}

.dark-mode .form-control {
    display: block;
    width: 100%;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.428571429;
    color: inherit;
    background-color: transparent;
    background-image: none;
    border: 1px solid;
    border-color: rgba(221, 224, 255, .54);
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}

.dark-mode .main-footer {
    border-top: 1px solid transparent;
}

.dark-mode .box.box-solid.box-primary>.box-header {
    color: #fff;
    background-color: inherit;
    border-color: rgba(221, 224, 255, .54);
    border-top-left-radius: 45px;
    border-top-right-radius: 45px;
}

.dark-mode .box-body {
    border-radius: 0px;
    padding: 10px;
}

.dark-mode .box-header {
    display: block;
    padding: 10px;
    position: relative;
    border-color: transparent;
    border-radius: 0px;
}

.dark-mode .nav-stacked>li>a {
    color: inherit;
}

.dark-mode .list-group-item {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: transparent;
    border: 1px solid rgba(221, 224, 255, .54);
}

.dark-mode .panel-footer {
    padding: 10px 15px;
    border-top: 1px rgba(221, 224, 255, .54);
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
}


.dark-mode .content .row [class*=col-] .box {
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: 4px 4px 30px rgba(221, 224, 255, .54);
    -moz-box-shadow: 0 1px 1px rgba(0, 0, 0, .1);
    -ms-box-shadow: 0 1px 1px rgba(0, 0, 0, .1);
    -webkit-border-radius: 1px !important;
    -moz-border-radius: 1px !important;
    -ms-border-radius: 1px !important;
    border-radius: 15px !important;
    border-color: inherit;
    background-color: inherit;
}

/* Dark Mode - Input Fields */
.dark-mode input:not(#filterNavigateMenu),
.dark-mode textarea:not(#filterNavigateMenu),
.dark-mode select:not(#filterNavigateMenu),
.dark-mode .select2-selection:not(#filterNavigateMenu) {
    color: inherit;
    transition: all .5s ease-in-out;
}

.dark-mode input:focus:not(#filterNavigateMenu),
.dark-mode textarea:focus:not(#filterNavigateMenu),
.dark-mode select:focus:not(#filterNavigateMenu),
.dark-mode .select2-selection:focus:not(#filterNavigateMenu) {
    color: #1f201f;
    outline: none;
}

.dark-mode .input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
    margin-bottom: 0;
    color: inherit;
    border-color: rgba(221, 224, 255, .54);
    background-color: inherit;
}

.dark-mode .input-group .input-group-addon {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    color: inherit;
    border-bottom-left-radius: 0;
    border-color: rgba(221, 224, 255, .54);
    background-color: transparent;
}

.dark-mode .input-group .form-control:last-child,
.dark-mode .input-group-addon:last-child,
.dark-mode .input-group-btn:last-child>.btn,
.dark-mode .input-group-btn:last-child>.btn-group>.btn,
.dark-mode .input-group-btn:last-child>.dropdown-toggle,
.dark-mode .input-group-btn:first-child>.btn:not(:first-child),
.dark-mode .input-group-btn:first-child>.btn-group:not(:first-child)>.btn {
    color: inherit;
}

.dark-mode input:not(#filterNavigateMenu),
textarea:not(#filterNavigateMenu),
optgroup:not(#filterNavigateMenu),
select:not(#filterNavigateMenu),
.dark-mode .select2-selection:not(#filterNavigateMenu) {
    -moz-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    -webkit-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;
}

.dark-mode .modern-skin-dark .main-sidebar .sidebar .sidebar-menu li>a {
    font-weight: bold;
}

.dark-mode .content-header>h1 {
    font-weight: bold;
}

.dark-mode .box-header>.fa,
.dark-mode .box-header>.glyphicon,
.dark-mode .box-header>.ion,
.dark-mode .box-header .box-title {
    font-weight: bold;
}

.dark-mode .content-header>h2 {
    font-weight: bold;
}

.dark-mode .main-header .logo .logo-lg {
    font-weight: bold;
}


.dark-mode .modal-content {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
    -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, .125);
    box-shadow: 0 2px 3px rgba(0, 0, 0, .125);
    border: 0;
    background: #1a202c;
}

.dark-mode .modal-header {
    padding: 15px;
    border-bottom: 1px solid rgba(221, 224, 255, .54);
    min-height: 16.428571429px;
    background-color: #1a202c;
    color: inherit;
}


.dark-mode .navbar-nav>.notifications-menu>.dropdown-menu>li .menu>li>a,
.dark-mode .navbar-nav>.messages-menu>.dropdown-menu>li .menu>li>a,
.dark-mode .navbar-nav>.tasks-menu>.dropdown-menu>li .menu>li>a {
    display: block;
    white-space: nowrap;
    border-bottom: 1px solid rgba(221, 224, 255, .54);
    background: #1a202c;
    color: inherit;
}

.dark-mode .navbar-nav>.notifications-menu>.dropdown-menu>li.footer>a,
.dark-mode .navbar-nav>.messages-menu>.dropdown-menu>li.footer>a,
.dark-mode .navbar-nav>.tasks-menu>.dropdown-menu>li.footer>a {
    background: #1a202c !important;
    color: inherit !important;
}

.dark-mode .navbar-nav>.user-menu>.dropdown-menu>.user-footer {
    background-color: #1a202c;
}

.dark-mode .ticket-container {
    background-color: #222020;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #ddd;
    display: grid;
    grid-template-columns: 1fr;
}

/* Fixed Background Colors - Not affected by theme toggle */
/* Header Background - Fixed Teal Color */
.main-header,
.main-header.navbar,
.main-header .navbar,
body .main-header,
html .main-header {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    border-bottom: none !important;
}

/* Desktop Logo - Fixed Teal Color */
.desktop-logo,
.main-header .logo,
body .main-header .logo,
html .main-header .logo {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    color: white !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

/* Sidebar Background - Fixed Dark Color */
.main-sidebar,
.sidebar,
body .main-sidebar,
html .main-sidebar,
body .sidebar,
html .sidebar {
    background: #2c3e50 !important;
    background-color: #2c3e50 !important;
    background-image: none !important;
}

/* Sidebar Menu Items - Fixed Colors */
.main-sidebar .sidebar-menu > li > a,
.sidebar-menu > li > a,
body .main-sidebar .sidebar-menu > li > a,
html .main-sidebar .sidebar-menu > li > a {
    background: transparent !important;
    color: #ecf0f1 !important;
    border: none !important;
}

/* Sidebar Menu Items Hover */
.main-sidebar .sidebar-menu > li > a:hover,
.sidebar-menu > li > a:hover,
body .main-sidebar .sidebar-menu > li > a:hover,
html .main-sidebar .sidebar-menu > li > a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Sidebar Menu Items Active */
.main-sidebar .sidebar-menu > li.active > a,
.sidebar-menu > li.active > a,
body .main-sidebar .sidebar-menu > li.active > a,
html .main-sidebar .sidebar-menu > li.active > a {
    background: #00BFA5 !important;
    color: white !important;
    border-left: 3px solid #ffffff !important;
}

/* Ultra High Specificity Override - Force Fixed Colors */
/* Override all possible theme variations */
body.modern-skin-dark .main-header,
body.modern-skin-dark .main-header .navbar,
body.modern-skin-dark .navbar,
body.modern-skin-dark .navbar-static-top,
html.modern-skin-dark .main-header,
html.modern-skin-dark .navbar,
body.dark-mode .main-header,
body.dark-mode .main-header .navbar,
body.dark-mode .navbar,
body.dark-mode .navbar-static-top,
html.dark-mode .main-header,
html.dark-mode .navbar,
body.light-mode .main-header,
body.light-mode .main-header .navbar,
body.light-mode .navbar,
body.light-mode .navbar-static-top,
html.light-mode .main-header,
html.light-mode .navbar {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Override all possible sidebar variations */
body.modern-skin-dark .main-sidebar,
body.modern-skin-dark .main-sidebar .sidebar,
body.modern-skin-dark .sidebar,
html.modern-skin-dark .main-sidebar,
html.modern-skin-dark .sidebar,
body.dark-mode .main-sidebar,
body.dark-mode .main-sidebar .sidebar,
body.dark-mode .sidebar,
html.dark-mode .main-sidebar,
html.dark-mode .sidebar,
body.light-mode .main-sidebar,
body.light-mode .main-sidebar .sidebar,
body.light-mode .sidebar,
html.light-mode .main-sidebar,
html.light-mode .sidebar {
    background: #2c3e50 !important;
    background-color: #2c3e50 !important;
    background-image: none !important;
}

/* Override all possible logo variations */
body.modern-skin-dark .main-header .logo,
body.modern-skin-dark .desktop-logo,
html.modern-skin-dark .main-header .logo,
html.modern-skin-dark .desktop-logo,
body.dark-mode .main-header .logo,
body.dark-mode .desktop-logo,
html.dark-mode .main-header .logo,
html.dark-mode .desktop-logo,
body.light-mode .main-header .logo,
body.light-mode .desktop-logo,
html.light-mode .main-header .logo,
html.light-mode .desktop-logo {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    color: white !important;
}

/* Override all possible menu item variations */
body.modern-skin-dark .main-sidebar .sidebar-menu > li > a,
body.modern-skin-dark .sidebar-menu > li > a,
html.modern-skin-dark .main-sidebar .sidebar-menu > li > a,
html.modern-skin-dark .sidebar-menu > li > a,
body.dark-mode .main-sidebar .sidebar-menu > li > a,
body.dark-mode .sidebar-menu > li > a,
html.dark-mode .main-sidebar .sidebar-menu > li > a,
html.dark-mode .sidebar-menu > li > a,
body.light-mode .main-sidebar .sidebar-menu > li > a,
body.light-mode .sidebar-menu > li > a,
html.light-mode .main-sidebar .sidebar-menu > li > a,
html.light-mode .sidebar-menu > li > a {
    background: transparent !important;
    color: #ecf0f1 !important;
}

/* Override all possible active menu item variations */
body.modern-skin-dark .main-sidebar .sidebar-menu > li.active > a,
body.modern-skin-dark .sidebar-menu > li.active > a,
html.modern-skin-dark .main-sidebar .sidebar-menu > li.active > a,
html.modern-skin-dark .sidebar-menu > li.active > a,
body.dark-mode .main-sidebar .sidebar-menu > li.active > a,
body.dark-mode .sidebar-menu > li.active > a,
html.dark-mode .main-sidebar .sidebar-menu > li.active > a,
html.dark-mode .sidebar-menu > li.active > a,
body.light-mode .main-sidebar .sidebar-menu > li.active > a,
body.light-mode .sidebar-menu > li.active > a,
html.light-mode .main-sidebar .sidebar-menu > li.active > a,
html.light-mode .sidebar-menu > li.active > a {
    background: #00BFA5 !important;
    color: white !important;
    border-left: 3px solid #ffffff !important;
    gap: 20px;
}

.dark-mode .ticket-label {
    flex: 0 0 150px;
    font-weight: bold;
    color: inherit;
    margin-right: -59px;
}