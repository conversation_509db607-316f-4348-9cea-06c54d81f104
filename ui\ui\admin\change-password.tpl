{include file="sections/header.tpl"}

<div class="row">
    <div class="col-lg-6 col-lg-offset-3 col-md-8 col-md-offset-2 col-sm-10 col-sm-offset-1">
        <div class="modern-card change-password-card" style="
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin-bottom: 30px;
        ">
            <!-- Change Password Header -->
            <div class="change-password-header" style="
                background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                padding: 30px 24px 20px;
                color: white;
                text-align: center;
                position: relative;
            ">
                <div style="
                    position: absolute;
                    top: -50px;
                    right: -50px;
                    width: 150px;
                    height: 150px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                "></div>
                <h3 style="
                    font-size: 1.5rem;
                    font-weight: 600;
                    margin: 0 0 8px 0;
                    position: relative;
                    z-index: 2;
                ">
                    <i class="fa fa-key" style="margin-right: 8px; opacity: 0.9;"></i>
                    {Lang::T('Change Password')}
                </h3>
                <p style="
                    font-size: 0.875rem;
                    opacity: 0.9;
                    margin: 0;
                    position: relative;
                    z-index: 2;
                ">Update your account password securely</p>
            </div>

            <!-- Change Password Form -->
            <div class="change-password-form-container" style="padding: 30px 24px;">
                <form class="modern-change-password-form" method="post" role="form" action="{Text::url('settings/change-password-post')}">
                    <input type="hidden" name="csrf_token" value="{$csrf_token}">

                    <!-- Current Password -->
                    <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                        <label class="modern-form-label" style="
                            display: block;
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: #374151;
                            margin-bottom: 8px;
                        ">
                            <i class="fa fa-lock" style="margin-right: 8px; color: #00BFA5;"></i>
                            {Lang::T('Current Password')}
                        </label>
                        <div class="password-input-container" style="position: relative;">
                            <input type="password"
                                   class="modern-form-input"
                                   id="password"
                                   name="password"
                                   required
                                   style="
                                       width: 100%;
                                       padding: 12px 40px 12px 16px;
                                       border: 2px solid #e5e7eb;
                                       border-radius: 12px;
                                       font-size: 0.875rem;
                                       transition: all 0.3s ease;
                                   "
                                   onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                            <button type="button"
                                    class="password-toggle"
                                    onclick="togglePassword('password')"
                                    style="
                                        position: absolute;
                                        right: 12px;
                                        top: 50%;
                                        transform: translateY(-50%);
                                        background: none;
                                        border: none;
                                        color: #9ca3af;
                                        cursor: pointer;
                                        padding: 4px;
                                    ">
                                <i class="fa fa-eye" id="password-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- New Password -->
                    <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                        <label class="modern-form-label" style="
                            display: block;
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: #374151;
                            margin-bottom: 8px;
                        ">
                            <i class="fa fa-key" style="margin-right: 8px; color: #00BFA5;"></i>
                            {Lang::T('New Password')}
                        </label>
                        <div class="password-input-container" style="position: relative;">
                            <input type="password"
                                   class="modern-form-input"
                                   id="npass"
                                   name="npass"
                                   required
                                   style="
                                       width: 100%;
                                       padding: 12px 40px 12px 16px;
                                       border: 2px solid #e5e7eb;
                                       border-radius: 12px;
                                       font-size: 0.875rem;
                                       transition: all 0.3s ease;
                                   "
                                   onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                            <button type="button"
                                    class="password-toggle"
                                    onclick="togglePassword('npass')"
                                    style="
                                        position: absolute;
                                        right: 12px;
                                        top: 50%;
                                        transform: translateY(-50%);
                                        background: none;
                                        border: none;
                                        color: #9ca3af;
                                        cursor: pointer;
                                        padding: 4px;
                                    ">
                                <i class="fa fa-eye" id="npass-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="password-strength" style="
                            margin-top: 8px;
                            font-size: 0.75rem;
                            display: none;
                        "></div>
                    </div>

                    <!-- Confirm New Password -->
                    <div class="form-group modern-form-group" style="margin-bottom: 24px;">
                        <label class="modern-form-label" style="
                            display: block;
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: #374151;
                            margin-bottom: 8px;
                        ">
                            <i class="fa fa-check-circle" style="margin-right: 8px; color: #00BFA5;"></i>
                            {Lang::T('Confirm New Password')}
                        </label>
                        <div class="password-input-container" style="position: relative;">
                            <input type="password"
                                   class="modern-form-input"
                                   id="cnpass"
                                   name="cnpass"
                                   required
                                   style="
                                       width: 100%;
                                       padding: 12px 40px 12px 16px;
                                       border: 2px solid #e5e7eb;
                                       border-radius: 12px;
                                       font-size: 0.875rem;
                                       transition: all 0.3s ease;
                                   "
                                   onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                            <button type="button"
                                    class="password-toggle"
                                    onclick="togglePassword('cnpass')"
                                    style="
                                        position: absolute;
                                        right: 12px;
                                        top: 50%;
                                        transform: translateY(-50%);
                                        background: none;
                                        border: none;
                                        color: #9ca3af;
                                        cursor: pointer;
                                        padding: 4px;
                                    ">
                                <i class="fa fa-eye" id="cnpass-eye"></i>
                            </button>
                        </div>
                        <div class="password-match" id="password-match" style="
                            margin-top: 8px;
                            font-size: 0.75rem;
                            display: none;
                        "></div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions" style="
                        margin-top: 32px;
                        padding-top: 24px;
                        border-top: 1px solid #e5e7eb;
                    ">
                        <div class="row">
                            <div class="col-md-6 col-sm-6 col-xs-12" style="margin-bottom: 12px;">
                                <button class="modern-btn-primary"
                                        type="submit"
                                        style="
                                            width: 100%;
                                            padding: 14px 24px;
                                            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                                            color: white;
                                            border: none;
                                            border-radius: 12px;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            cursor: pointer;
                                            transition: all 0.3s ease;
                                            box-shadow: 0 4px 12px rgba(0, 191, 165, 0.3);
                                        "
                                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(0, 191, 165, 0.4)'"
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 191, 165, 0.3)'">
                                    <i class="fa fa-save" style="margin-right: 8px;"></i>
                                    {Lang::T('Save Changes')}
                                </button>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12">
                                <a href="{Text::url('dashboard')}"
                                   class="modern-btn-secondary"
                                   style="
                                       display: block;
                                       width: 100%;
                                       padding: 14px 24px;
                                       background: #f8fafc;
                                       color: #6b7280;
                                       border: 2px solid #e5e7eb;
                                       border-radius: 12px;
                                       font-size: 0.875rem;
                                       font-weight: 500;
                                       text-decoration: none;
                                       text-align: center;
                                       cursor: pointer;
                                       transition: all 0.3s ease;
                                   "
                                   onmouseover="this.style.background='#f1f5f9'; this.style.borderColor='#cbd5e1'"
                                   onmouseout="this.style.background='#f8fafc'; this.style.borderColor='#e5e7eb'">
                                    <i class="fa fa-times" style="margin-right: 8px;"></i>
                                    {Lang::T('Cancel')}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const eye = document.getElementById(fieldId + '-eye');

        if (field.type === 'password') {
            field.type = 'text';
            eye.className = 'fa fa-eye-slash';
        } else {
            field.type = 'password';
            eye.className = 'fa fa-eye';
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        const newPassword = document.getElementById('npass');
        const confirmPassword = document.getElementById('cnpass');
        const strengthDiv = document.getElementById('password-strength');
        const matchDiv = document.getElementById('password-match');

        // Password strength checker
        newPassword.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;
            let feedback = [];

            if (password.length >= 8) strength++;
            else feedback.push('At least 8 characters');

            if (/[a-z]/.test(password)) strength++;
            else feedback.push('Lowercase letter');

            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('Uppercase letter');

            if (/[0-9]/.test(password)) strength++;
            else feedback.push('Number');

            if (/[^A-Za-z0-9]/.test(password)) strength++;
            else feedback.push('Special character');

            if (password.length > 0) {
                strengthDiv.style.display = 'block';

                if (strength < 3) {
                    strengthDiv.innerHTML = '<span style="color: #ef4444;">Weak password. Missing: ' + feedback.join(', ') + '</span>';
                } else if (strength < 5) {
                    strengthDiv.innerHTML = '<span style="color: #f59e0b;">Medium password. Missing: ' + feedback.join(', ') + '</span>';
                } else {
                    strengthDiv.innerHTML = '<span style="color: #10b981;">Strong password!</span>';
                }
            } else {
                strengthDiv.style.display = 'none';
            }
        });

        // Password match checker
        function checkPasswordMatch() {
            if (newPassword.value && confirmPassword.value) {
                matchDiv.style.display = 'block';

                if (newPassword.value === confirmPassword.value) {
                    matchDiv.innerHTML = '<span style="color: #10b981;"><i class="fa fa-check"></i> Passwords match</span>';
                    confirmPassword.style.borderColor = '#10b981';
                } else {
                    matchDiv.innerHTML = '<span style="color: #ef4444;"><i class="fa fa-times"></i> Passwords do not match</span>';
                    confirmPassword.style.borderColor = '#ef4444';
                }
            } else {
                matchDiv.style.display = 'none';
                confirmPassword.style.borderColor = '#e5e7eb';
            }
        }

        newPassword.addEventListener('input', checkPasswordMatch);
        confirmPassword.addEventListener('input', checkPasswordMatch);
    });
</script>

{include file="sections/footer.tpl"}
