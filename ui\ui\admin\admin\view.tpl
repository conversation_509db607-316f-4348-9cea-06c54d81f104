{include file="sections/header.tpl"}
<!-- Modern Admin Profile View -->

<div class="row">
    <div class="col-lg-10 col-lg-offset-1 col-md-12">
        <div class="modern-card admin-view-card" style="
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin-bottom: 30px;
        ">
            <!-- Profile Header -->
            <div class="admin-view-header" style="
                background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                padding: 30px 24px 20px;
                color: white;
                text-align: center;
                position: relative;
            ">
                <div style="
                    position: absolute;
                    top: -50px;
                    right: -50px;
                    width: 150px;
                    height: 150px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                "></div>
                <h3 style="
                    font-size: 1.5rem;
                    font-weight: 600;
                    margin: 0 0 8px 0;
                    position: relative;
                    z-index: 2;
                ">
                    <i class="fa fa-user-circle" style="margin-right: 8px; opacity: 0.9;"></i>
                    {$d['fullname']}
                </h3>
                <div style="
                    display: inline-flex;
                    align-items: center;
                    background: rgba(255, 255, 255, 0.2);
                    padding: 6px 12px;
                    border-radius: 20px;
                    font-size: 0.75rem;
                    font-weight: 500;
                    position: relative;
                    z-index: 2;
                ">
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: {if $d['status'] == 'Active'}#10b981{else}#ef4444{/if};
                        border-radius: 50%;
                        margin-right: 6px;
                    "></div>
                    {$d['user_type']} - {$d['status']}
                </div>
            </div>

            <!-- Profile Content -->
            <div class="admin-view-content" style="padding: 30px 24px;">
                <div class="row">
                    <!-- Main Profile Information -->
                    <div class="{if $d['user_type'] == 'Sales'}col-md-6{else}col-md-8 col-md-offset-2{/if}">
                        <div class="profile-info-section" style="
                            background: #f8fafc;
                            border-radius: 16px;
                            padding: 24px;
                            margin-bottom: 24px;
                            border: 1px solid #e5e7eb;
                        ">
                            <!-- Profile Photo Section -->
                            <div class="admin-photo-display" style="text-align: center; margin-bottom: 24px;">
                                <div class="admin-photo-container" style="
                                    display: inline-block;
                                    position: relative;
                                    margin-bottom: 16px;
                                ">
                                    <a href="{$app_url}/{$UPLOAD_PATH}{$d['photo']}" target="foto" style="text-decoration: none;">
                                        <img src="{$app_url}/{$UPLOAD_PATH}{$d['photo']}.thumb.jpg"
                                            class="admin-view-photo"
                                            alt="Admin Photo"
                                            data-avatar="true"
                                            data-avatar-name="{$d['fullname']}"
                                            data-avatar-type="{$d['user_type']}"
                                            data-avatar-size="profile"
                                            style="
                                                width: 120px;
                                                height: 120px;
                                                border-radius: 50%;
                                                border: 4px solid #00BFA5;
                                                box-shadow: 0 8px 24px rgba(0, 191, 165, 0.3);
                                                cursor: pointer;
                                                transition: all 0.3s ease;
                                                object-fit: cover;
                                            "
                                            onmouseover="this.style.transform='scale(1.05)'"
                                            onmouseout="this.style.transform='scale(1)'">
                                    </a>
                                    <div class="photo-status-badge" style="
                                        position: absolute;
                                        bottom: 8px;
                                        right: 8px;
                                        width: 24px;
                                        height: 24px;
                                        background: {if $d['status'] == 'Active'}#10b981{else}#ef4444{/if};
                                        border: 3px solid white;
                                        border-radius: 50%;
                                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                                    "></div>
                                </div>
                            </div>
                            <!-- Profile Information List -->
                            <div class="profile-info-list">
                                <!-- Username -->
                                <div class="info-item" style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    padding: 12px 0;
                                    border-bottom: 1px solid #e5e7eb;
                                ">
                                    <div class="info-label" style="
                                        display: flex;
                                        align-items: center;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                    ">
                                        <i class="fa fa-user" style="margin-right: 8px; color: #00BFA5; width: 16px;"></i>
                                        {Lang::T('Username')}
                                    </div>
                                    <div class="info-value" style="
                                        font-size: 0.875rem;
                                        color: #6b7280;
                                        font-weight: 500;
                                    ">
                                        {$d['username']}
                                    </div>
                                </div>

                                <!-- Phone Number -->
                                <div class="info-item" style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    padding: 12px 0;
                                    border-bottom: 1px solid #e5e7eb;
                                ">
                                    <div class="info-label" style="
                                        display: flex;
                                        align-items: center;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                    ">
                                        <i class="fa fa-phone" style="margin-right: 8px; color: #00BFA5; width: 16px;"></i>
                                        {Lang::T('Phone Number')}
                                    </div>
                                    <div class="info-value" style="
                                        font-size: 0.875rem;
                                        color: #6b7280;
                                        font-weight: 500;
                                    ">
                                        {if $d['phone']}
                                            <a href="tel:{$d['phone']}" style="color: #00BFA5; text-decoration: none;">
                                                {$d['phone']}
                                            </a>
                                        {else}
                                            <span style="color: #9ca3af; font-style: italic;">Not provided</span>
                                        {/if}
                                    </div>
                                </div>

                                <!-- Email -->
                                <div class="info-item" style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    padding: 12px 0;
                                    border-bottom: 1px solid #e5e7eb;
                                ">
                                    <div class="info-label" style="
                                        display: flex;
                                        align-items: center;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                    ">
                                        <i class="fa fa-envelope" style="margin-right: 8px; color: #00BFA5; width: 16px;"></i>
                                        {Lang::T('Email')}
                                    </div>
                                    <div class="info-value" style="
                                        font-size: 0.875rem;
                                        color: #6b7280;
                                        font-weight: 500;
                                    ">
                                        {if $d['email']}
                                            <a href="mailto:{$d['email']}" style="color: #00BFA5; text-decoration: none;">
                                                {$d['email']}
                                            </a>
                                        {else}
                                            <span style="color: #9ca3af; font-style: italic;">Not provided</span>
                                        {/if}
                                    </div>
                                </div>

                                <!-- City -->
                                <div class="info-item" style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    padding: 12px 0;
                                    border-bottom: 1px solid #e5e7eb;
                                ">
                                    <div class="info-label" style="
                                        display: flex;
                                        align-items: center;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                    ">
                                        <i class="fa fa-map-marker" style="margin-right: 8px; color: #00BFA5; width: 16px;"></i>
                                        {Lang::T('City')}
                                    </div>
                                    <div class="info-value" style="
                                        font-size: 0.875rem;
                                        color: #6b7280;
                                        font-weight: 500;
                                    ">
                                        {if $d['city']}{$d['city']}{else}<span style="color: #9ca3af; font-style: italic;">Not provided</span>{/if}
                                    </div>
                                </div>

                                <!-- Sub District -->
                                <div class="info-item" style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    padding: 12px 0;
                                    border-bottom: 1px solid #e5e7eb;
                                ">
                                    <div class="info-label" style="
                                        display: flex;
                                        align-items: center;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                    ">
                                        <i class="fa fa-building" style="margin-right: 8px; color: #00BFA5; width: 16px;"></i>
                                        {Lang::T('Sub District')}
                                    </div>
                                    <div class="info-value" style="
                                        font-size: 0.875rem;
                                        color: #6b7280;
                                        font-weight: 500;
                                    ">
                                        {if $d['subdistrict']}{$d['subdistrict']}{else}<span style="color: #9ca3af; font-style: italic;">Not provided</span>{/if}
                                    </div>
                                </div>

                                <!-- Ward -->
                                <div class="info-item" style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    padding: 12px 0;
                                    border-bottom: 1px solid #e5e7eb;
                                ">
                                    <div class="info-label" style="
                                        display: flex;
                                        align-items: center;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                    ">
                                        <i class="fa fa-home" style="margin-right: 8px; color: #00BFA5; width: 16px;"></i>
                                        {Lang::T('Ward')}
                                    </div>
                                    <div class="info-value" style="
                                        font-size: 0.875rem;
                                        color: #6b7280;
                                        font-weight: 500;
                                    ">
                                        {if $d['ward']}{$d['ward']}{else}<span style="color: #9ca3af; font-style: italic;">Not provided</span>{/if}
                                    </div>
                                </div>

                                <!-- User Type -->
                                <div class="info-item" style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    padding: 12px 0;
                                ">
                                    <div class="info-label" style="
                                        display: flex;
                                        align-items: center;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                    ">
                                        <i class="fa fa-users" style="margin-right: 8px; color: #00BFA5; width: 16px;"></i>
                                        {Lang::T('User Type')}
                                    </div>
                                    <div class="info-value" style="
                                        font-size: 0.875rem;
                                        font-weight: 500;
                                    ">
                                        <span style="
                                            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                                            color: white;
                                            padding: 4px 12px;
                                            border-radius: 12px;
                                            font-size: 0.75rem;
                                            font-weight: 600;
                                        ">
                                            {$d['user_type']}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Agent Information (for Sales users) -->
                    {if $d['user_type'] == 'Sales' && $d['root'] neq ''}
                        <div class="col-md-6">
                            <div class="agent-info-section" style="
                                background: #f0fdfa;
                                border-radius: 16px;
                                padding: 24px;
                                margin-bottom: 24px;
                                border: 1px solid #a7f3d0;
                            ">
                                <h4 style="
                                    font-size: 1.125rem;
                                    font-weight: 600;
                                    color: #059669;
                                    margin: 0 0 20px 0;
                                    padding-bottom: 12px;
                                    border-bottom: 2px solid #10b981;
                                    display: flex;
                                    align-items: center;
                                ">
                                    <i class="fa fa-user-tie" style="margin-right: 8px;"></i>
                                    Agent - {$agent['fullname']}
                                </h4>

                                <div class="agent-info-list">
                                    <!-- Agent Phone -->
                                    <div class="info-item" style="
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;
                                        padding: 12px 0;
                                        border-bottom: 1px solid #a7f3d0;
                                    ">
                                        <div class="info-label" style="
                                            display: flex;
                                            align-items: center;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            color: #059669;
                                        ">
                                            <i class="fa fa-phone" style="margin-right: 8px; width: 16px;"></i>
                                            {Lang::T('Phone Number')}
                                        </div>
                                        <div class="info-value" style="
                                            font-size: 0.875rem;
                                            color: #047857;
                                            font-weight: 500;
                                        ">
                                            <a href="tel:{$agent['phone']}" style="color: #059669; text-decoration: none;">
                                                {$agent['phone']}
                                            </a>
                                        </div>
                                    </div>

                                    <!-- Agent Email -->
                                    <div class="info-item" style="
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;
                                        padding: 12px 0;
                                        border-bottom: 1px solid #a7f3d0;
                                    ">
                                        <div class="info-label" style="
                                            display: flex;
                                            align-items: center;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            color: #059669;
                                        ">
                                            <i class="fa fa-envelope" style="margin-right: 8px; width: 16px;"></i>
                                            {Lang::T('Email')}
                                        </div>
                                        <div class="info-value" style="
                                            font-size: 0.875rem;
                                            color: #047857;
                                            font-weight: 500;
                                        ">
                                            <a href="mailto:{$agent['email']}" style="color: #059669; text-decoration: none;">
                                                {$agent['email']}
                                            </a>
                                        </div>
                                    </div>

                                    <!-- Agent City -->
                                    <div class="info-item" style="
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;
                                        padding: 12px 0;
                                        border-bottom: 1px solid #a7f3d0;
                                    ">
                                        <div class="info-label" style="
                                            display: flex;
                                            align-items: center;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            color: #059669;
                                        ">
                                            <i class="fa fa-map-marker" style="margin-right: 8px; width: 16px;"></i>
                                            {Lang::T('City')}
                                        </div>
                                        <div class="info-value" style="
                                            font-size: 0.875rem;
                                            color: #047857;
                                            font-weight: 500;
                                        ">
                                            {$agent['city']}
                                        </div>
                                    </div>

                                    <!-- Agent Sub District -->
                                    <div class="info-item" style="
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;
                                        padding: 12px 0;
                                        border-bottom: 1px solid #a7f3d0;
                                    ">
                                        <div class="info-label" style="
                                            display: flex;
                                            align-items: center;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            color: #059669;
                                        ">
                                            <i class="fa fa-building" style="margin-right: 8px; width: 16px;"></i>
                                            {Lang::T('Sub District')}
                                        </div>
                                        <div class="info-value" style="
                                            font-size: 0.875rem;
                                            color: #047857;
                                            font-weight: 500;
                                        ">
                                            {$agent['subdistrict']}
                                        </div>
                                    </div>

                                    <!-- Agent Ward -->
                                    <div class="info-item" style="
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;
                                        padding: 12px 0;
                                    ">
                                        <div class="info-label" style="
                                            display: flex;
                                            align-items: center;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            color: #059669;
                                        ">
                                            <i class="fa fa-home" style="margin-right: 8px; width: 16px;"></i>
                                            {Lang::T('Ward')}
                                        </div>
                                        <div class="info-value" style="
                                            font-size: 0.875rem;
                                            color: #047857;
                                            font-weight: 500;
                                        ">
                                            {$agent['ward']}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {/if}
                </div>
                <!-- Action Buttons -->
                <div class="profile-actions" style="
                    margin-top: 32px;
                    padding-top: 24px;
                    border-top: 1px solid #e5e7eb;
                    text-align: center;
                ">
                    <div class="row">
                        <div class="col-md-6 col-md-offset-3">
                            <a href="{Text::url('settings/users-edit/', $d['id'])}"
                               class="modern-btn-primary"
                               style="
                                   display: block;
                                   width: 100%;
                                   padding: 14px 24px;
                                   background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                                   color: white;
                                   border: none;
                                   border-radius: 12px;
                                   font-size: 0.875rem;
                                   font-weight: 600;
                                   text-decoration: none;
                                   cursor: pointer;
                                   transition: all 0.3s ease;
                                   box-shadow: 0 4px 12px rgba(0, 191, 165, 0.3);
                                   margin-bottom: 12px;
                               "
                               onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(0, 191, 165, 0.4)'"
                               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 191, 165, 0.3)'">
                                <i class="fa fa-edit" style="margin-right: 8px;"></i>
                                {Lang::T('Edit')}
                            </a>
                            <div style="
                                font-size: 0.875rem;
                                color: #6b7280;
                            ">
                                Or <a href="{Text::url('settings/users')}"
                                      style="
                                          color: #00BFA5;
                                          text-decoration: none;
                                          font-weight: 500;
                                      "
                                      onmouseover="this.style.textDecoration='underline'"
                                      onmouseout="this.style.textDecoration='none'">
                                    {Lang::T('Cancel')}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="sections/footer.tpl"}