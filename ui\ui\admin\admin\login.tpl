<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>{Lang::T('Login')} - {$_c['CompanyName']}</title>
    <link rel="shortcut icon" href="{$app_url}/ui/ui/images/logo.png" type="image/x-icon" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/bootstrap.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/modern-AdminLTE.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 50%, #26A69A 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        .login-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 420px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00BFA5, #4DB6AC, #26A69A);
            border-radius: 24px 24px 0 0;
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00BFA5, #4DB6AC);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(0, 191, 165, 0.3);
        }

        .login-logo i {
            font-size: 2rem;
            color: white;
        }

        .login-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            letter-spacing: -0.025em;
        }

        .login-subtitle {
            color: #64748b;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input-group {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 500;
            color: #1e293b;
            background: #ffffff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #00BFA5;
            box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
            transform: translateY(-1px);
        }

        .form-input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }

        .form-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .form-input:focus + .form-icon {
            color: #00BFA5;
        }

        .login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #00BFA5, #4DB6AC);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 24px;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 191, 165, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 12px;
            color: #64748b;
            text-decoration: none;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #f1f5f9;
            color: #00BFA5;
            text-decoration: none;
        }

        .back-link i {
            margin-right: 8px;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 16px;
            margin-bottom: 24px;
            font-weight: 500;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }

        .alert-success {
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            color: #059669;
            border-left: 4px solid #059669;
        }

        /* Loading Animation */
        .login-btn.loading {
            pointer-events: none;
        }

        .login-btn.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .login-card {
                padding: 24px;
                margin: 10px;
                border-radius: 20px;
            }

            .login-title {
                font-size: 1.5rem;
            }

            .form-input {
                padding: 14px 18px 14px 45px;
            }

            .login-btn {
                padding: 14px;
            }
        }

        /* Enhanced Alert Styles */
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 24px;
            padding: 16px 20px;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.15);
            animation: slideInDown 0.3s ease-out;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border-left: 4px solid #ef4444;
            color: #dc2626;
        }

        .alert-content {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .alert-content i {
            font-size: 1.2rem;
            margin-right: 12px;
            color: #ef4444;
        }

        .alert-message {
            font-weight: 500;
            font-size: 0.95rem;
        }

        .alert-suggestion {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid rgba(239, 68, 68, 0.2);
        }

        .alert-suggestion small {
            color: #7f1d1d;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
        }

        .alert-suggestion i {
            margin-right: 6px;
            font-size: 0.9rem;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fa fa-shield"></i>
                </div>
                <h1 class="login-title">Admin Portal</h1>
                <p class="login-subtitle">{Lang::T('Enter Admin Area')} - {$_c['CompanyName']}</p>
            </div>

            {if isset($login_error)}
                <div class="alert alert-danger" id="loginError">
                    <div class="alert-content">
                        <i class="fa {if $login_error_type == 'username_not_found'}fa-user-times{elseif $login_error_type == 'password_incorrect'}fa-lock{else}fa-exclamation-triangle{/if}"></i>
                        <span class="alert-message">{$login_error}</span>
                    </div>
                    {if $login_error_type == 'username_not_found'}
                        <div class="alert-suggestion">
                            <small><i class="fa fa-info-circle"></i> Please check your username spelling or contact administrator if you need assistance.</small>
                        </div>
                    {elseif $login_error_type == 'password_incorrect'}
                        <div class="alert-suggestion">
                            <small><i class="fa fa-info-circle"></i> Please check your password. Make sure Caps Lock is off.</small>
                        </div>
                    {/if}
                </div>
            {elseif isset($notify)}
                <div class="alert alert-danger">
                    {$notify}
                </div>
            {/if}

            <form action="{Text::url('admin/post')}" method="post" id="adminLoginForm">
                <input type="hidden" name="csrf_token" value="{$csrf_token}">

                <div class="form-group">
                    <label class="form-label">{Lang::T('Username')}</label>
                    <div class="form-input-group">
                        <input type="text"
                               required
                               class="form-input"
                               name="username"
                               placeholder="{Lang::T('Username')}"
                               autocomplete="username"
                               value="{if isset($username_value)}{$username_value}{/if}">
                        <i class="fa fa-user form-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">{Lang::T('Password')}</label>
                    <div class="form-input-group">
                        <input type="password"
                               required
                               class="form-input"
                               name="password"
                               placeholder="{Lang::T('Password')}"
                               autocomplete="current-password">
                        <i class="fa fa-lock form-icon"></i>
                    </div>
                </div>

                <button type="submit" class="login-btn" id="loginButton">
                    <span id="loginText">{Lang::T('Login')}</span>
                </button>

                <a href="{Text::url('login')}" class="back-link">
                    <i class="fa fa-arrow-left"></i>
                    {Lang::T('Go Back')}
                </a>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('adminLoginForm');
            const loginButton = document.getElementById('loginButton');
            const loginText = document.getElementById('loginText');

            form.addEventListener('submit', function(e) {
                loginButton.classList.add('loading');
                loginText.style.opacity = '0';
                loginButton.disabled = true;
            });

            // Handle error alerts
            const loginError = document.getElementById('loginError');
            if (loginError) {
                // Auto-dismiss alert after 10 seconds
                setTimeout(() => {
                    loginError.style.opacity = '0';
                    loginError.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        loginError.style.display = 'none';
                    }, 300);
                }, 10000);

                // Focus appropriate field based on error type
                const errorType = '{if isset($login_error_type)}{$login_error_type}{/if}';
                if (errorType === 'password_incorrect') {
                    // Focus password field if password was incorrect
                    const passwordInput = form.querySelector('input[type="password"]');
                    if (passwordInput) {
                        passwordInput.focus();
                        passwordInput.select();
                    }
                } else {
                    // Focus username field for other errors
                    const usernameInput = form.querySelector('input[name="username"]');
                    if (usernameInput) {
                        usernameInput.focus();
                        if (errorType === 'username_not_found') {
                            usernameInput.select();
                        }
                    }
                }
            } else {
                // Auto-focus first input if no error
                const firstInput = form.querySelector('input[type="text"]');
                if (firstInput) {
                    firstInput.focus();
                }
            }

            // Handle form validation
            const inputs = form.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() === '') {
                        this.style.borderColor = '#ef4444';
                    } else {
                        this.style.borderColor = '#e2e8f0';
                    }
                });

                input.addEventListener('input', function() {
                    if (this.style.borderColor === 'rgb(239, 68, 68)') {
                        this.style.borderColor = '#e2e8f0';
                    }
                });
            });
        });
    </script>
</body>

</html>
