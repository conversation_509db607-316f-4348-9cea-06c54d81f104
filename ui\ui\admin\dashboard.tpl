{include file="sections/header.tpl"}

{function showWidget pos=0}
    {foreach $widgets as $w}
        {if $w['position'] == $pos}
            {$w['content']}
        {/if}
    {/foreach}
{/function}

{assign dtipe value="dashboard_`$tipeUser`"}

{assign rows explode(".", $_c[$dtipe])}
{assign pos 1}
{foreach $rows as $cols}
    {if $cols == 12}
        <div class="row dashboard-row">
            <div class="col-md-12 col-sm-12 col-xs-12">
                {showWidget widgets=$widgets pos=$pos}
            </div>
        </div>
        {assign pos value=$pos+1}
    {else}
        {assign colss explode(",", $cols)}
        <div class="row dashboard-row">
            {foreach $colss as $c}
                {if $c == 6}
                    <div class="col-md-{$c} col-sm-6 col-xs-6 dashboard-card-col">
                        {showWidget widgets=$widgets pos=$pos}
                    </div>
                {elseif $c == 4}
                    <div class="col-md-{$c} col-sm-6 col-xs-6 dashboard-card-col">
                        {showWidget widgets=$widgets pos=$pos}
                    </div>
                {elseif $c == 3}
                    <div class="col-md-{$c} col-sm-6 col-xs-6 dashboard-card-col">
                        {showWidget widgets=$widgets pos=$pos}
                    </div>
                {else}
                    <div class="col-md-{$c} col-sm-12 col-xs-12 dashboard-card-col">
                        {showWidget widgets=$widgets pos=$pos}
                    </div>
                {/if}
                {assign pos value=$pos+1}
            {/foreach}
        </div>
    {/if}
{/foreach}

{if $_c['new_version_notify'] != 'disable'}
    <script>
        window.addEventListener('DOMContentLoaded', function() {
            $.getJSON("./version.json?" + Math.random(), function(data) {
                var localVersion = data.version;
                $('#version').html('Version: ' + localVersion);
                $.getJSON(
                    "https://raw.githubusercontent.com/hotspotbilling/phpnuxbill/master/version.json?" +
                    Math
                    .random(),
                    function(data) {
                        var latestVersion = data.version;
                        if (localVersion !== latestVersion) {
                            $('#version').html('Latest Version: ' + latestVersion);
                            if (getCookie(latestVersion) != 'done') {
                                Swal.fire({
                                    icon: 'info',
                                    title: "New Version Available\nVersion: " + latestVersion,
                                    toast: true,
                                    position: 'bottom-right',
                                    showConfirmButton: true,
                                    showCloseButton: true,
                                    timer: 30000,
                                    confirmButtonText: '<a href="{Text::url('community')}#latestVersion" style="color: white;">Update Now</a>',
                                    timerProgressBar: true,
                                    didOpen: (toast) => {
                                        toast.addEventListener('mouseenter', Swal.stopTimer)
                                        toast.addEventListener('mouseleave', Swal
                                            .resumeTimer)
                                    }
                                });
                                setCookie(latestVersion, 'done', 7);
                            }
                        }
                    });
            });

        });
    </script>
{/if}

<!-- Enhanced Mobile 2-Column Layout CSS -->
<style>
/* ===== MOBILE 2-COLUMN DASHBOARD LAYOUT ===== */

/* Ensure consistent 2-column layout on mobile for all dashboard cards */
@media (max-width: 768px) {
    /* Force 2-column layout for dashboard cards */
    .dashboard-row {
        display: flex !important;
        flex-wrap: wrap !important;
        margin: 0 -8px !important;
    }

    .dashboard-card-col {
        padding: 0 8px !important;
        margin-bottom: 16px !important;
        box-sizing: border-box !important;
    }

    /* Force 2-column layout for col-md-3 and col-md-4 cards */
    .dashboard-row .col-md-3,
    .dashboard-row .col-md-4 {
        width: 50% !important;
        float: none !important;
        flex: 0 0 50% !important;
        max-width: 50% !important;
    }

    /* Keep col-md-6 as single column for better readability */
    .dashboard-row .col-md-6 {
        width: 100% !important;
        float: none !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    /* Ensure col-md-12 stays full width */
    .dashboard-row .col-md-12 {
        width: 100% !important;
        float: none !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    /* Touch optimization for mobile cards */
    .modern-card,
    .panel,
    .box {
        min-height: 120px !important;
        border-radius: 16px !important;
        margin-bottom: 16px !important;
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    }

    .modern-card:hover,
    .panel:hover,
    .box:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    }

    /* Optimize card content for mobile */
    .modern-card .stat-value,
    .panel-body h3,
    .box-body h3 {
        font-size: 1.75rem !important;
        line-height: 1.2 !important;
    }

    .modern-card .stat-label,
    .panel-title,
    .box-title {
        font-size: 0.875rem !important;
        line-height: 1.3 !important;
    }

    /* Improve spacing for mobile */
    .content {
        padding: 20px 15px !important;
    }

    .content-header {
        padding: 15px !important;
        margin-bottom: 20px !important;
    }

    .content-header h1 {
        font-size: 1.5rem !important;
        margin: 0 !important;
    }
}

/* Extra small mobile devices (≤480px) - Single column */
@media (max-width: 480px) {
    .dashboard-row .col-md-3,
    .dashboard-row .col-md-4 {
        width: 100% !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    .dashboard-card-col {
        padding: 0 15px !important;
    }

    .dashboard-row {
        margin: 0 -15px !important;
    }

    .modern-card,
    .panel,
    .box {
        min-height: 100px !important;
    }

    .content {
        padding: 15px 10px !important;
    }
}

/* Landscape mobile orientation optimization */
@media (max-width: 768px) and (orientation: landscape) {
    .dashboard-row {
        margin-bottom: 12px;
    }

    .modern-card,
    .panel,
    .box {
        min-height: 100px !important;
    }

    .content {
        padding: 15px 10px !important;
    }
}

/* Ensure proper Bootstrap grid behavior */
.dashboard-row [class*="col-"] {
    position: relative;
    min-height: 1px;
}

/* Touch target optimization */
@media (max-width: 768px) {
    .modern-card,
    .panel,
    .box,
    .dashboard-card-footer,
    .btn {
        min-height: 48px;
        touch-action: manipulation;
    }

    /* Improve button spacing on mobile */
    .btn-group .btn {
        padding: 8px 12px !important;
        font-size: 0.875rem !important;
        margin: 2px !important;
    }

    /* Better mobile typography */
    .modern-card h3,
    .panel-title,
    .box-title {
        font-size: 1.125rem !important;
        line-height: 1.3 !important;
        margin-bottom: 8px !important;
    }

    /* Optimize chart containers for mobile */
    .chart-container,
    canvas {
        max-height: 250px !important;
    }

    /* Improve table responsiveness */
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        font-size: 12px;
    }
}
</style>

{include file="sections/footer.tpl"}