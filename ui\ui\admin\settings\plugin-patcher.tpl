{include file="sections/header.tpl"}

<div class="row">
    <div class="col-sm-12">
        <div class="panel panel-hovered mb20 panel-primary">
            <div class="panel-heading">
                <div class="btn-group pull-right">
                    <a class="btn btn-primary btn-xs" title="Scan Plugins" href="{Text::url('')}pluginpatcher/scan">
                        <span class="glyphicon glyphicon-search" aria-hidden="true"></span> Scan
                    </a>
                    <a class="btn btn-success btn-xs" title="Patch All" href="{Text::url('')}pluginpatcher/patch" 
                       onclick="return confirm('This will patch all problematic plugins. Continue?')">
                        <span class="glyphicon glyphicon-wrench" aria-hidden="true"></span> Patch All
                    </a>
                    <a class="btn btn-warning btn-xs" title="Cleanup Backups" href="{Text::url('')}pluginpatcher/cleanup" 
                       onclick="return confirm('This will remove backup files older than 7 days. Continue?')">
                        <span class="glyphicon glyphicon-trash" aria-hidden="true"></span> Cleanup
                    </a>
                </div>
                Plugin Compatibility Patcher
            </div>
            <div class="panel-body">
                
                {if isset($compatibility_info)}
                <div class="row">
                    <div class="col-md-6">
                        <h4>System Information</h4>
                        <table class="table table-condensed">
                            <tr><td>Operating System</td><td><span class="label label-info">{$compatibility_info.php_os}</span></td></tr>
                            <tr><td>PHP Version</td><td><span class="label label-info">{$compatibility_info.php_version}</span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h4>Function Availability</h4>
                        <table class="table table-condensed">
                            {foreach $compatibility_info.functions_available as $func => $available}
                            <tr>
                                <td>{$func}()</td>
                                <td>
                                    {if $available}
                                        <span class="label label-success">Available</span>
                                    {else}
                                        <span class="label label-danger">Disabled</span>
                                    {/if}
                                </td>
                            </tr>
                            {/foreach}
                        </table>
                    </div>
                </div>
                
                {if !empty($compatibility_info.disabled_functions)}
                <div class="alert alert-warning">
                    <strong>Disabled Functions:</strong> {implode(', ', $compatibility_info.disabled_functions)}
                </div>
                {/if}
                {/if}
                
                {if isset($scan_result)}
                <div class="alert alert-info">
                    <h4>Scan Results</h4>
                    {if empty($plugins_needing_patch)}
                        <p>All plugins are compatible! No patching needed.</p>
                    {else}
                        <p>Found {count($plugins_needing_patch)} plugin(s) that need compatibility patching:</p>
                        <ul>
                            {foreach $plugins_needing_patch as $plugin}
                            <li>
                                <strong>{$plugin}</strong>
                                <a href="{Text::url('')}pluginpatcher/patch/{$plugin}" class="btn btn-xs btn-success" 
                                   onclick="return confirm('Patch this plugin?')">Patch</a>
                                <a href="{Text::url('')}pluginpatcher/info/{$plugin}" class="btn btn-xs btn-info">Info</a>
                            </li>
                            {/foreach}
                        </ul>
                    {/if}
                </div>
                {/if}
                
                {if isset($plugin_info)}
                <div class="alert alert-info">
                    <h4>Plugin Information: {$plugin_name}</h4>
                    <p><strong>Needs Patching:</strong> 
                        {if $needs_patch}
                            <span class="label label-warning">Yes</span>
                            <a href="{Text::url('')}pluginpatcher/patch/{$plugin_name}" class="btn btn-xs btn-success" 
                               onclick="return confirm('Patch this plugin?')">Patch Now</a>
                        {else}
                            <span class="label label-success">No</span>
                        {/if}
                    </p>
                    
                    {if !empty($backup_files)}
                    <p><strong>Backup Files:</strong> {count($backup_files)} available
                        <a href="{Text::url('')}pluginpatcher/restore/{$plugin_name}" class="btn btn-xs btn-warning" 
                           onclick="return confirm('Restore from backup?')">Restore</a>
                    </p>
                    {/if}
                    
                    <details>
                        <summary>View Plugin Code</summary>
                        <pre style="max-height: 400px; overflow-y: auto; background: #f5f5f5; padding: 10px; margin-top: 10px;">{$plugin_content|escape}</pre>
                    </details>
                </div>
                {/if}
                
                {if isset($plugin_list)}
                <h4>Installed Plugins</h4>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Plugin Name</th>
                                <th>Status</th>
                                <th>Size</th>
                                <th>Modified</th>
                                <th>Backups</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach $plugin_list as $plugin}
                            <tr>
                                <td>{$plugin.name}</td>
                                <td>
                                    {if $plugin.needs_patch}
                                        <span class="label label-warning">Needs Patch</span>
                                    {else}
                                        <span class="label label-success">Compatible</span>
                                    {/if}
                                </td>
                                <td>{($plugin.size/1024)|number_format:1} KB</td>
                                <td>{$plugin.modified|date_format:'Y-m-d H:i:s'}</td>
                                <td>
                                    {if $plugin.has_backup}
                                        <span class="label label-info">{$plugin.backup_count}</span>
                                    {else}
                                        <span class="label label-default">0</span>
                                    {/if}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-xs">
                                        <a href="{Text::url('')}pluginpatcher/info/{$plugin.name}" class="btn btn-info" title="Info">
                                            <i class="glyphicon glyphicon-info-sign"></i>
                                        </a>
                                        {if $plugin.needs_patch}
                                        <a href="{Text::url('')}pluginpatcher/patch/{$plugin.name}" class="btn btn-success" title="Patch"
                                           onclick="return confirm('Patch this plugin?')">
                                            <i class="glyphicon glyphicon-wrench"></i>
                                        </a>
                                        {/if}
                                        {if $plugin.has_backup}
                                        <a href="{Text::url('')}pluginpatcher/restore/{$plugin.name}" class="btn btn-warning" title="Restore"
                                           onclick="return confirm('Restore from backup?')">
                                            <i class="glyphicon glyphicon-repeat"></i>
                                        </a>
                                        {/if}
                                    </div>
                                </td>
                            </tr>
                            {/foreach}
                        </tbody>
                    </table>
                </div>
                {/if}
                
            </div>
        </div>
    </div>
</div>

<div class="panel panel-default">
    <div class="panel-heading">
        <h4>About Plugin Compatibility Patcher</h4>
    </div>
    <div class="panel-body">
        <p>This tool helps fix compatibility issues with plugins that use system-specific functions:</p>
        <ul>
            <li><strong>sys_getloadavg()</strong> - Not available on Windows systems</li>
            <li><strong>shell_exec()</strong> - Often disabled on shared hosting for security</li>
            <li><strong>exec(), system(), passthru()</strong> - May be disabled on some servers</li>
        </ul>
        <p>The patcher automatically replaces problematic function calls with safe alternatives that work across different environments.</p>
        
        <div class="alert alert-warning">
            <strong>Note:</strong> Always backup your plugins before patching. The patcher creates automatic backups, but manual backups are recommended for important plugins.
        </div>
    </div>
</div>

{include file="sections/footer.tpl"}
