/**
 * PHPNuxBill Mobile Table Horizontal Scrolling Enhancement
 * 
 * This script enhances mobile table scrolling by:
 * 1. Adding scroll indicators
 * 2. Improving touch scrolling behavior
 * 3. Managing scroll hints and visual feedback
 */

(function() {
    'use strict';

    // Initialize mobile table scrolling when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileTableScroll);
    } else {
        initMobileTableScroll();
    }

    function initMobileTableScroll() {
        // Only apply on mobile devices
        if (window.innerWidth <= 768) {
            const tableContainers = document.querySelectorAll('.table-responsive');
            tableContainers.forEach(initTableContainer);
        }

        // Re-initialize on window resize
        window.addEventListener('resize', debounce(function() {
            if (window.innerWidth <= 768) {
                const tableContainers = document.querySelectorAll('.table-responsive');
                tableContainers.forEach(initTableContainer);
            }
        }, 250));
    }

    function initTableContainer(container) {
        const table = container.querySelector('table');
        if (!table) return;

        // Check if table needs horizontal scrolling
        updateScrollState(container);

        // Add scroll event listeners
        container.addEventListener('scroll', function() {
            updateScrollIndicators(container);
            markAsInteracted(container);
        });

        // Add touch event listeners for better mobile experience
        addTouchScrolling(container);

        // Initial scroll indicator update
        updateScrollIndicators(container);
    }

    function updateScrollState(container) {
        const table = container.querySelector('table');
        if (!table) return;

        // Check if table is wider than container
        const containerWidth = container.clientWidth;
        const tableWidth = table.scrollWidth;
        
        if (tableWidth > containerWidth) {
            container.classList.add('has-scroll');
        } else {
            container.classList.remove('has-scroll');
        }
    }

    function updateScrollIndicators(container) {
        const scrollLeft = container.scrollLeft;
        const scrollWidth = container.scrollWidth;
        const clientWidth = container.clientWidth;
        const maxScroll = scrollWidth - clientWidth;

        // Update scroll position classes
        if (scrollLeft > 10) {
            container.classList.add('scrolled-left');
        } else {
            container.classList.remove('scrolled-left');
        }

        if (scrollLeft >= maxScroll - 10) {
            container.classList.add('scrolled-right');
        } else {
            container.classList.remove('scrolled-right');
        }
    }

    function markAsInteracted(container) {
        container.classList.add('interacted');
    }

    function addTouchScrolling(container) {
        let isScrolling = false;
        let startX = 0;
        let scrollLeftStart = 0;

        // Touch start
        container.addEventListener('touchstart', function(e) {
            if (e.touches.length === 1) {
                isScrolling = true;
                startX = e.touches[0].pageX;
                scrollLeftStart = container.scrollLeft;
                
                // Add momentum class for smooth scrolling
                container.style.scrollBehavior = 'auto';
            }
        }, { passive: true });

        // Touch move
        container.addEventListener('touchmove', function(e) {
            if (!isScrolling || e.touches.length !== 1) return;

            const currentX = e.touches[0].pageX;
            const deltaX = startX - currentX;
            
            // Scroll the container
            container.scrollLeft = scrollLeftStart + deltaX;
            
            // Update indicators
            updateScrollIndicators(container);
        }, { passive: true });

        // Touch end
        container.addEventListener('touchend', function(e) {
            if (isScrolling) {
                isScrolling = false;
                
                // Re-enable smooth scrolling
                container.style.scrollBehavior = 'smooth';
                
                // Mark as interacted
                markAsInteracted(container);
            }
        }, { passive: true });

        // Mouse events for desktop testing
        let isMouseDown = false;
        let mouseStartX = 0;
        let mouseScrollStart = 0;

        container.addEventListener('mousedown', function(e) {
            isMouseDown = true;
            mouseStartX = e.pageX;
            mouseScrollStart = container.scrollLeft;
            container.style.cursor = 'grabbing';
            e.preventDefault();
        });

        document.addEventListener('mousemove', function(e) {
            if (!isMouseDown) return;
            
            const deltaX = mouseStartX - e.pageX;
            container.scrollLeft = mouseScrollStart + deltaX;
            updateScrollIndicators(container);
        });

        document.addEventListener('mouseup', function() {
            if (isMouseDown) {
                isMouseDown = false;
                container.style.cursor = 'grab';
                markAsInteracted(container);
            }
        });
    }

    // Utility function for debouncing
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Enhanced scroll behavior for DataTables
    function enhanceDataTables() {
        // Wait for DataTables to initialize
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $(document).on('init.dt', function(e, settings) {
                const api = new $.fn.dataTable.Api(settings);
                const container = $(api.table().container()).find('.table-responsive')[0];
                
                if (container && window.innerWidth <= 768) {
                    // Re-initialize scrolling for DataTable
                    setTimeout(() => {
                        initTableContainer(container);
                    }, 100);
                }
            });

            // Handle DataTable redraws
            $(document).on('draw.dt', function(e, settings) {
                const api = new $.fn.dataTable.Api(settings);
                const container = $(api.table().container()).find('.table-responsive')[0];
                
                if (container && window.innerWidth <= 768) {
                    updateScrollState(container);
                    updateScrollIndicators(container);
                }
            });
        }
    }

    // Initialize DataTables enhancements
    enhanceDataTables();

    // Add CSS for improved scrolling cursor
    const style = document.createElement('style');
    style.textContent = `
        @media (max-width: 768px) {
            .table-responsive {
                cursor: grab;
            }
            
            .table-responsive:active {
                cursor: grabbing;
            }
            
            /* Improve scrollbar visibility on mobile */
            .table-responsive::-webkit-scrollbar {
                height: 6px !important;
            }
            
            .table-responsive::-webkit-scrollbar-thumb {
                background: rgba(0, 191, 165, 0.4) !important;
                border-radius: 3px !important;
            }
            
            .table-responsive::-webkit-scrollbar-thumb:active {
                background: rgba(0, 191, 165, 0.6) !important;
            }
            
            /* Smooth momentum scrolling */
            .table-responsive {
                -webkit-overflow-scrolling: touch !important;
                scroll-behavior: smooth !important;
            }
            
            /* Visual feedback for scrollable tables */
            .table-responsive.has-scroll {
                border: 1px solid rgba(0, 191, 165, 0.2) !important;
                border-radius: 8px !important;
            }
            
            /* Highlight scrollable area */
            .table-responsive.has-scroll:not(.interacted) {
                animation: scrollHint 2s ease-in-out infinite;
            }
            
            @keyframes scrollHint {
                0%, 100% { 
                    border-color: rgba(0, 191, 165, 0.2);
                }
                50% { 
                    border-color: rgba(0, 191, 165, 0.4);
                }
            }
        }
    `;
    document.head.appendChild(style);

    // Export functions for external use
    window.PHPNuxBillTableScroll = {
        init: initMobileTableScroll,
        initContainer: initTableContainer,
        updateScrollState: updateScrollState
    };

})();
