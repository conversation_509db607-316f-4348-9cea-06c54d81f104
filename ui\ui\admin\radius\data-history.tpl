{include file="sections/header.tpl"}

<!-- Radius Users Data History -->
<div class="row">
    <div class="col-sm-12">
        <div class="panel panel-hovered mb20 panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <i class="fa fa-table"></i> Radius Users Data History
                    <div class="pull-right">
                        <a href="{Text::url('radius/data-statistics')}" class="btn btn-xs btn-success">
                            <i class="fa fa-arrow-left"></i> Back to Statistics
                        </a>
                    </div>
                </div>
            </div>
            <div class="panel-body">
                
                <!-- Search Section -->
                <div class="md-whiteframe-z1 mb20 text-center" style="padding: 15px">
                    <div class="row">
                        <div class="col-md-8">
                            <form id="site-search" method="post" action="{Text::url('radius/data-history')}">
                                <div class="input-group">
                                    <div class="input-group-addon">
                                        <span class="fa fa-search"></span>
                                    </div>
                                    <input type="text" name="search" class="form-control" value="{$search}"
                                        placeholder="Search by Username...">
                                    <div class="input-group-btn">
                                        <button class="btn btn-success" type="submit">
                                            <i class="fa fa-search"></i> Search
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group btn-group-justified">
                                <a href="{Text::url('radius/data-history')}" class="btn btn-default">
                                    <i class="fa fa-refresh"></i> Refresh
                                </a>
                                <button class="btn btn-info" onclick="exportData()">
                                    <i class="fa fa-download"></i> Export
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-condensed" id="dataTable">
                        <thead>
                            <tr style="background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                <th style="min-width: 120px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-user" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">Username</span>
                                    <span class="visible-xs" style="color: white !important;">User</span>
                                </th>
                                <th style="min-width: 100px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-server" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">NAS Server</span>
                                    <span class="visible-xs" style="color: white !important;">NAS</span>
                                </th>
                                <th style="min-width: 80px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-circle" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">Status</span>
                                    <span class="visible-xs" style="color: white !important;">Status</span>
                                </th>
                                <th style="min-width: 110px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-globe" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">IP Address</span>
                                    <span class="visible-xs" style="color: white !important;">IP</span>
                                </th>
                                <th style="min-width: 120px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-wifi" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">MAC Address</span>
                                    <span class="visible-xs" style="color: white !important;">MAC</span>
                                </th>
                                <th style="min-width: 90px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-clock-o" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">Session Time</span>
                                    <span class="visible-xs" style="color: white !important;">Time</span>
                                </th>
                                <th style="min-width: 90px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-upload" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">Data Upload</span>
                                    <span class="visible-xs" style="color: white !important;">Up</span>
                                </th>
                                <th style="min-width: 90px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-download" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">Data Download</span>
                                    <span class="visible-xs" style="color: white !important;">Down</span>
                                </th>
                                <th style="min-width: 80px; background: linear-gradient(135deg, #2196F3, #21CBF3) !important; color: white !important;">
                                    <i class="fa fa-cogs" style="color: white !important;"></i>
                                    <span class="hidden-xs" style="color: white !important;">Actions</span>
                                    <span class="visible-xs" style="color: white !important;">Act</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {if $history}
                                {foreach $history as $h}
                                    <tr>
                                        <td>
                                            <strong>{$h.username}</strong>
                                            <br><small class="text-muted">{$h.realm}</small>
                                        </td>
                                        <td>
                                            <span class="label label-info">{$h.nasid}</span>
                                            <br><small>{$h.nasipaddress}</small>
                                        </td>
                                        <td>
                                            {if $h.acctstatustype == 'Start'}
                                                <span class="label label-success">
                                                    <i class="fa fa-play"></i> Online
                                                </span>
                                            {elseif $h.acctstatustype == 'Stop'}
                                                <span class="label label-danger">
                                                    <i class="fa fa-stop"></i> Offline
                                                </span>
                                            {else}
                                                <span class="label label-warning">
                                                    <i class="fa fa-refresh"></i> Update
                                                </span>
                                            {/if}
                                        </td>
                                        <td>
                                            <code>{$h.framedipaddress}</code>
                                            {if $h.nasportid}
                                                <br><small>Port: {$h.nasportid}</small>
                                            {/if}
                                        </td>
                                        <td>
                                            <code style="font-size: 11px;">{$h.macaddr}</code>
                                        </td>
                                        <td>
                                            <strong>{RadiusStats::formatTime($h.acctsessiontime)}</strong>
                                            <br><small class="text-muted">{$h.acctsessiontime}s</small>
                                        </td>
                                        <td>
                                            <span class="text-danger">
                                                <i class="fa fa-upload"></i> 
                                                {RadiusStats::formatBytes($h.acctinputoctets)}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-success">
                                                <i class="fa fa-download"></i> 
                                                {RadiusStats::formatBytes($h.acctoutputoctets)}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-xs">
                                                <button class="btn btn-info" onclick="viewDetails('{$h.id}')" 
                                                    title="View Details">
                                                    <i class="fa fa-eye"></i>
                                                </button>
                                                {if $h.acctstatustype == 'Start'}
                                                    <button class="btn btn-warning" onclick="disconnectUser('{$h.username}')" 
                                                        title="Disconnect User">
                                                        <i class="fa fa-times"></i>
                                                    </button>
                                                {/if}
                                            </div>
                                        </td>
                                    </tr>
                                {/foreach}
                            {else}
                                <tr>
                                    <td colspan="9" class="text-center">
                                        <div style="padding: 40px;">
                                            <i class="fa fa-database fa-3x text-muted"></i>
                                            <h4 class="text-muted">No Data Available</h4>
                                            <p class="text-muted">No radius accounting data found.</p>
                                        </div>
                                    </td>
                                </tr>
                            {/if}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {if $paginator}
                    {include file="pagination.tpl"}
                {/if}

                <!-- Summary Statistics -->
                <div class="row" style="margin-top: 20px;">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <strong>Total Records</strong><br>
                                    <span class="text-primary" id="totalRecords">
                                        {if $paginator}{$paginator.found}{else}0{/if}
                                    </span>
                                </div>
                                <div class="col-md-3 text-center">
                                    <strong>Online Users</strong><br>
                                    <span class="text-success" id="onlineUsers">
                                        {assign var="online" value=0}
                                        {foreach $history as $h}
                                            {if $h.acctstatustype == 'Start'}
                                                {assign var="online" value=$online+1}
                                            {/if}
                                        {/foreach}
                                        {$online}
                                    </span>
                                </div>
                                <div class="col-md-3 text-center">
                                    <strong>Total Upload</strong><br>
                                    <span class="text-danger" id="totalUpload">
                                        {assign var="totalUp" value=0}
                                        {foreach $history as $h}
                                            {assign var="totalUp" value=$totalUp+$h.acctinputoctets}
                                        {/foreach}
                                        {RadiusStats::formatBytes($totalUp)}
                                    </span>
                                </div>
                                <div class="col-md-3 text-center">
                                    <strong>Total Download</strong><br>
                                    <span class="text-success" id="totalDownload">
                                        {assign var="totalDown" value=0}
                                        {foreach $history as $h}
                                            {assign var="totalDown" value=$totalDown+$h.acctoutputoctets}
                                        {/foreach}
                                        {RadiusStats::formatBytes($totalDown)}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">
                    <i class="fa fa-info-circle"></i> Session Details
                </h4>
            </div>
            <div class="modal-body" id="modalContent">
                <div class="text-center">
                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                    <p>Loading details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Auto refresh every 30 seconds
setInterval(function() {
    if (!$('#detailsModal').hasClass('in')) {
        location.reload();
    }
}, 30000);

function viewDetails(sessionId) {
    $('#detailsModal').modal('show');
    
    // Simulate loading session details
    setTimeout(function() {
        $('#modalContent').html(`
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fa fa-user"></i> User Information</h5>
                    <table class="table table-condensed">
                        <tr><td><strong>Session ID:</strong></td><td>` + sessionId + `</td></tr>
                        <tr><td><strong>Username:</strong></td><td>user123</td></tr>
                        <tr><td><strong>Realm:</strong></td><td>example.com</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5><i class="fa fa-server"></i> Network Information</h5>
                    <table class="table table-condensed">
                        <tr><td><strong>NAS IP:</strong></td><td>***********</td></tr>
                        <tr><td><strong>Framed IP:</strong></td><td>**********</td></tr>
                        <tr><td><strong>MAC Address:</strong></td><td>AA:BB:CC:DD:EE:FF</td></tr>
                    </table>
                </div>
            </div>
        `);
    }, 1000);
}

function disconnectUser(username) {
    if (confirm('Are you sure you want to disconnect user: ' + username + '?')) {
        // Implement disconnect functionality
        alert('Disconnect functionality would be implemented here');
    }
}

function exportData() {
    // Implement export functionality
    window.open('{Text::url("radius/export-history")}', '_blank');
}

// DataTable initialization for better mobile experience
$(document).ready(function() {
    if ($(window).width() < 768) {
        // Mobile responsive adjustments
        $('.table-responsive').css('font-size', '12px');
        $('.btn-group-xs .btn').css('padding', '2px 4px');
    }
});
</script>

<!-- Mobile Responsive Styles -->
<style>
/* Table Header Improvements */
.table thead th {
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    border-bottom: 2px solid rgba(255,255,255,0.3);
}

.table thead th i {
    margin-right: 5px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 11px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .table th, .table td {
        padding: 6px 4px;
        vertical-align: middle;
        text-align: center;
    }

    .table th {
        font-size: 10px;
        padding: 8px 4px;
    }

    .btn-group-xs .btn {
        padding: 2px 4px;
        font-size: 9px;
        margin: 1px;
    }

    .label {
        font-size: 8px;
        padding: 2px 4px;
        display: inline-block;
        margin: 1px 0;
    }

    code {
        font-size: 8px;
        padding: 1px 3px;
        word-break: break-all;
    }

    .alert {
        padding: 10px;
        margin: 10px 0;
        font-size: 12px;
    }

    .modal-dialog {
        margin: 10px;
    }

    /* Hide some columns on mobile */
    .table th:nth-child(5),
    .table td:nth-child(5) {
        display: none; /* Hide MAC address on mobile */
    }
}

@media (max-width: 480px) {
    .table-responsive {
        font-size: 10px;
    }
    
    .panel-heading .btn {
        font-size: 10px;
        padding: 2px 6px;
    }
    
    .input-group {
        margin-bottom: 10px;
    }
    
    .btn-group-justified .btn {
        font-size: 11px;
        padding: 6px 8px;
    }
}

/* Custom scrollbar for mobile */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>

{include file="sections/footer.tpl"}
