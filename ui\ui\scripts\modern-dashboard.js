/**
 * Modern Dashboard JavaScript
 * PHPNuxBill Modern UI Enhancement
 */

document.addEventListener('DOMContentLoaded', function() {

    // Initialize modern features
    initializeModernFeatures();

    // Initialize greeting
    updateGreeting();

    // Initialize animations
    initializeAnimations();

    // Initialize responsive features
    initializeResponsive();

});

/**
 * Initialize modern UI features
 */
function initializeModernFeatures() {

    // Add loading states to buttons
    addLoadingStates();

    // Add hover effects
    addHoverEffects();

    // Initialize tooltips
    initializeTooltips();

    // Add smooth scrolling
    addSmoothScrolling();
}

/**
 * Update greeting based on time and language
 */
function updateGreeting() {
    const now = new Date();
    const hour = now.getHours();
    const greetingElement = document.getElementById('greeting-time');
    const timeElement = document.getElementById('current-time');

    // Get language from global variable or detect from page
    const currentLanguage = window.phpnuxbillLanguage || 'english';
    const dateLocale = currentLanguage === 'indonesia' ? 'id-ID' : 'en-US';

    // Language-aware greeting messages
    const greetingMessages = {
        indonesia: {
            morning: 'Selamat Pagi',
            afternoon: 'Selamat Siang',
            evening: 'Selamat Sore',
            night: 'Selamat Malam'
        },
        english: {
            morning: 'Good Morning',
            afternoon: 'Good Afternoon',
            evening: 'Good Evening',
            night: 'Good Night'
        }
    };

    let greetingKey;
    if (hour < 12) {
        greetingKey = 'morning';
    } else if (hour < 15) {
        greetingKey = 'afternoon';
    } else if (hour < 18) {
        greetingKey = 'evening';
    } else {
        greetingKey = 'night';
    }

    const greeting = greetingMessages[currentLanguage] ?
        greetingMessages[currentLanguage][greetingKey] :
        greetingMessages.english[greetingKey];

    if (greetingElement) {
        greetingElement.textContent = greeting;
    }

    if (timeElement) {
        timeElement.textContent = now.toLocaleDateString(dateLocale, {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
}

/**
 * Initialize animations
 */
function initializeAnimations() {

    // Fade in cards on load
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animate quick action buttons
    const quickActions = document.querySelectorAll('.quick-action-btn');
    quickActions.forEach((btn, index) => {
        btn.style.opacity = '0';
        btn.style.transform = 'scale(0.8)';

        setTimeout(() => {
            btn.style.transition = 'all 0.4s ease';
            btn.style.opacity = '1';
            btn.style.transform = 'scale(1)';
        }, 300 + (index * 50));
    });
}

/**
 * Add loading states to buttons
 */
function addLoadingStates() {
    const buttons = document.querySelectorAll('a[href], button');

    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Skip if it's a hash link or external link
            const href = this.getAttribute('href');
            if (!href || href.startsWith('#') || href.startsWith('http')) {
                return;
            }

            // Add loading state
            this.classList.add('loading');
            this.style.pointerEvents = 'none';

            // Remove loading state after navigation
            setTimeout(() => {
                this.classList.remove('loading');
                this.style.pointerEvents = 'auto';
            }, 2000);
        });
    });
}

/**
 * Add hover effects
 */
function addHoverEffects() {

    // Service items hover effect
    const serviceItems = document.querySelectorAll('.service-item');
    serviceItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Card hover effects
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 12px 40px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.08)';
        });
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');

    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'modern-tooltip';
            tooltip.textContent = this.getAttribute('data-tooltip');
            tooltip.style.cssText = `
                position: absolute;
                background: #1e293b;
                color: white;
                padding: 8px 12px;
                border-radius: 8px;
                font-size: 0.75rem;
                z-index: 1000;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            document.body.appendChild(tooltip);

            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

            setTimeout(() => tooltip.style.opacity = '1', 10);

            this._tooltip = tooltip;
        });

        element.addEventListener('mouseleave', function() {
            if (this._tooltip) {
                this._tooltip.remove();
                this._tooltip = null;
            }
        });
    });
}

/**
 * Add smooth scrolling
 */
function addSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            const target = document.querySelector(href);

            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Initialize responsive features
 */
function initializeResponsive() {

    // Handle mobile navigation
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.main-sidebar');

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            document.body.classList.toggle('sidebar-open');
        });
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        // Adjust layout for mobile
        if (window.innerWidth < 768) {
            document.body.classList.add('mobile-view');
        } else {
            document.body.classList.remove('mobile-view');
        }
    });

    // Initial check
    if (window.innerWidth < 768) {
        document.body.classList.add('mobile-view');
    }
}

/**
 * Utility function to format numbers
 */
function formatNumber(num) {
    const currentLanguage = window.phpnuxbillLanguage || 'english';
    const numberLocale = currentLanguage === 'indonesia' ? 'id-ID' : 'en-US';
    return new Intl.NumberFormat(numberLocale).format(num);
}

/**
 * Utility function to format currency
 */
function formatCurrency(amount, currency = 'IDR') {
    const currentLanguage = window.phpnuxbillLanguage || 'english';
    const currencyLocale = currentLanguage === 'indonesia' ? 'id-ID' : 'en-US';
    return new Intl.NumberFormat(currencyLocale, {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = 'modern-notification ' + type;

    const iconClass = type === 'success' ? 'check' : type === 'error' ? 'times' : 'info';
    notification.innerHTML =
        '<div class="notification-content">' +
            '<i class="fa fa-' + iconClass + '-circle"></i>' +
            '<span>' + message + '</span>' +
        '</div>';

    const bgColor = type === 'success' ? '#4CAF50' : type === 'error' ? '#F44336' : '#2196F3';
    notification.style.cssText =
        'position: fixed;' +
        'top: 20px;' +
        'right: 20px;' +
        'background: ' + bgColor + ';' +
        'color: white;' +
        'padding: 16px 20px;' +
        'border-radius: 12px;' +
        'box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);' +
        'z-index: 10000;' +
        'transform: translateX(100%);' +
        'transition: transform 0.3s ease;';

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);

    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

/**
 * Mobile Header Functions
 */
function toggleMobileUserMenu() {
    // This function is now handled by Bootstrap dropdown
    // Keeping for backward compatibility
}

function toggleMobileNotifications() {
    const dropdown = document.getElementById('mobileNotificationsDropdown');
    if (dropdown) {
        dropdown.style.display = dropdown.style.display === 'flex' ? 'none' : 'flex';
    }
}

/**
 * Enhanced Search Functions
 */
function initializeSearch() {
    const openSearchBtn = document.getElementById('openSearch');
    const openSearchDesktopBtn = document.getElementById('openSearchDesktop');
    const closeSearchBtn = document.getElementById('closeSearch');
    const searchOverlay = document.getElementById('searchOverlay');
    const searchInput = document.getElementById('searchTerm');

    // Open search overlay
    if (openSearchBtn) {
        openSearchBtn.addEventListener('click', function() {
            if (searchOverlay) {
                searchOverlay.style.display = 'flex';
                if (searchInput) searchInput.focus();
            }
        });
    }

    if (openSearchDesktopBtn) {
        openSearchDesktopBtn.addEventListener('click', function() {
            if (searchOverlay) {
                searchOverlay.style.display = 'flex';
                if (searchInput) searchInput.focus();
            }
        });
    }

    // Close search overlay
    if (closeSearchBtn) {
        closeSearchBtn.addEventListener('click', function() {
            if (searchOverlay) {
                searchOverlay.style.display = 'none';
            }
        });
    }

    // Close on overlay click
    if (searchOverlay) {
        searchOverlay.addEventListener('click', function(e) {
            if (e.target === searchOverlay) {
                searchOverlay.style.display = 'none';
            }
        });
    }

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && searchOverlay && searchOverlay.style.display === 'flex') {
            searchOverlay.style.display = 'none';
        }
    });
}

/**
 * Mobile dropdown close on outside click
 */
function initializeMobileDropdowns() {
    document.addEventListener('click', function(e) {
        const notificationsDropdown = document.getElementById('mobileNotificationsDropdown');

        // Close notifications dropdown if clicking outside
        if (notificationsDropdown && notificationsDropdown.style.display === 'flex') {
            if (!e.target.closest('.mobile-notifications-btn') && !e.target.closest('.mobile-notifications-dropdown')) {
                notificationsDropdown.style.display = 'none';
            }
        }
    });

    // Initialize Bootstrap dropdowns for mobile user menus
    if (typeof $ !== 'undefined') {
        $('.mobile-user-menu .dropdown-toggle').dropdown();
    }
}

/**
 * Header responsive adjustments
 */
function initializeHeaderResponsive() {
    function adjustHeader() {
        const mobileHeader = document.querySelector('.mobile-header');
        const desktopElements = document.querySelectorAll('.desktop-logo, .desktop-nav, .desktop-sidebar-toggle');

        if (window.innerWidth <= 768) {
            // Mobile view
            if (mobileHeader) mobileHeader.style.display = 'flex';
            desktopElements.forEach(el => {
                if (el) el.style.display = 'none';
            });
        } else {
            // Desktop view
            if (mobileHeader) mobileHeader.style.display = 'none';
            desktopElements.forEach(el => {
                if (el) el.style.display = '';
            });
        }
    }

    window.addEventListener('resize', adjustHeader);
    adjustHeader(); // Initial check
}

// Export functions for global use
window.ModernDashboard = {
    updateGreeting,
    showNotification,
    formatNumber,
    formatCurrency
};

// Export mobile functions globally
window.toggleMobileUserMenu = toggleMobileUserMenu;
window.toggleMobileNotifications = toggleMobileNotifications;

// Initialize all features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
    initializeMobileDropdowns();
    initializeHeaderResponsive();
    initializeProfilePage();
});

/**
 * Initialize profile page features
 */
function initializeProfilePage() {
    // File input display functionality
    const fileInput = document.getElementById('photo-input');
    const fileDisplay = document.querySelector('.file-input-display');

    if (fileInput && fileDisplay) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

                fileDisplay.innerHTML =
                    '<i class="fa fa-check-circle" style="font-size: 1.5rem; color: #00BFA5; margin-bottom: 8px;"></i>' +
                    '<div style="font-size: 0.875rem; color: #00BFA5; font-weight: 600;">' +
                        fileName +
                    '</div>' +
                    '<div style="font-size: 0.75rem; color: #6b7280; margin-top: 4px;">' +
                        fileSize + ' MB' +
                    '</div>';
                fileDisplay.style.borderColor = '#00BFA5';
                fileDisplay.style.background = '#f0fdfa';
            }
        });

        // Reset display on click
        fileDisplay.addEventListener('click', function() {
            if (fileInput.files.length === 0) {
                fileDisplay.innerHTML =
                    '<i class="fa fa-cloud-upload" style="font-size: 1.5rem; color: #6b7280; margin-bottom: 8px;"></i>' +
                    '<div style="font-size: 0.875rem; color: #6b7280;">' +
                        'Click to upload new photo' +
                    '</div>';
                fileDisplay.style.borderColor = '#d1d5db';
                fileDisplay.style.background = '#f8fafc';
            }
        });
    }

    // Form validation
    const form = document.querySelector('.modern-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const fullname = document.getElementById('fullname');
            const email = document.getElementById('email');

            let isValid = true;
            let errorMessage = '';

            // Validate full name
            if (fullname && fullname.value.trim().length < 2) {
                isValid = false;
                errorMessage += 'Full name must be at least 2 characters long.\n';
                fullname.style.borderColor = '#ef4444';
            }

            // Validate email format
            if (email && email.value.trim() && !email.readOnly) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email.value.trim())) {
                    isValid = false;
                    errorMessage += 'Please enter a valid email address.\n';
                    email.style.borderColor = '#ef4444';
                }
            }

            if (!isValid) {
                e.preventDefault();
                alert(errorMessage);
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin" style="margin-right: 8px;"></i>Saving...';
                submitBtn.style.opacity = '0.7';
            }
        });
    }

    // Reset border color on input focus
    const inputs = document.querySelectorAll('.modern-form-input, .modern-form-textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = '#00BFA5';
        });

        input.addEventListener('blur', function() {
            if (this.style.borderColor === 'rgb(239, 68, 68)') {
                // Keep error color if validation failed
                return;
            }
            this.style.borderColor = '#e5e7eb';
        });
    });
}
