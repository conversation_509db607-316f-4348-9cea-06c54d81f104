/**
 * Sidebar Minimize Enhancement
 * Handles tooltip and submenu functionality for minimized sidebar
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeSidebarMinimize();
});

/**
 * Initialize sidebar minimize functionality
 */
function initializeSidebarMinimize() {
    // Only initialize on desktop
    if (window.innerWidth <= 768) {
        return;
    }

    // Wait for sidebar to be ready
    setTimeout(() => {
        setupSidebarMinimize();
    }, 100);

    // Listen for sidebar toggle events
    document.addEventListener('click', function(e) {
        if (e.target.closest('.sidebar-toggle')) {
            setTimeout(() => {
                setupSidebarMinimize();
            }, 350); // Wait for transition to complete
        }
    });

    // Close submenu when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.sidebar-minimize-submenu') &&
            !e.target.closest('.main-sidebar .sidebar-menu > li.treeview > a')) {
            closeAllSubmenus();
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            closeAllSubmenus();
            removeAllTooltips();
        } else {
            setTimeout(() => {
                setupSidebarMinimize();
            }, 100);
        }
    });
}

/**
 * Setup sidebar minimize functionality
 */
function setupSidebarMinimize() {
    // Check if sidebar is minimized
    if (!isSidebarMinimized()) {
        closeAllSubmenus();
        removeAllTooltips();
        enableAdminLTETooltips();
        return;
    }

    // Disable AdminLTE tooltips for sidebar
    disableAdminLTETooltips();

    // Clean up any existing tooltips
    removeAllTooltips();
    removeAdminLTETooltips();

    const sidebarMenuItems = document.querySelectorAll('.main-sidebar .sidebar-menu > li > a');

    sidebarMenuItems.forEach(item => {
        const listItem = item.parentElement;
        const isTreeview = listItem.classList.contains('treeview');

        // Remove existing event listeners
        item.removeEventListener('mouseenter', item._tooltipMouseEnter);
        item.removeEventListener('mouseleave', item._tooltipMouseLeave);
        item.removeEventListener('click', item._submenuClick);

        // Disable any data-toggle tooltip
        item.removeAttribute('data-toggle');
        item.removeAttribute('data-placement');
        item.removeAttribute('title');
        item.removeAttribute('data-original-title');

        // Add tooltip functionality
        addTooltipFunctionality(item);

        // Add submenu functionality for treeview items
        if (isTreeview) {
            addSubmenuFunctionality(item, listItem);
        }
    });
}

/**
 * Add tooltip functionality to menu item
 */
function addTooltipFunctionality(item) {
    const spanElement = item.querySelector('span');
    if (!spanElement) return;

    const tooltipText = spanElement.textContent.trim();

    const mouseEnterHandler = function() {
        // Clean up any existing tooltips first
        removeAllTooltips();
        removeAdminLTETooltips();

        // Don't show tooltip if submenu is open
        if (this.parentElement.querySelector('.sidebar-minimize-submenu.show')) {
            return;
        }

        showTooltip(this, tooltipText);
    };

    const mouseLeaveHandler = function() {
        hideTooltip(this);
        // Also clean up any AdminLTE tooltips that might appear
        setTimeout(() => {
            removeAdminLTETooltips();
        }, 50);
    };

    item.addEventListener('mouseenter', mouseEnterHandler);
    item.addEventListener('mouseleave', mouseLeaveHandler);

    // Store handlers for removal
    item._tooltipMouseEnter = mouseEnterHandler;
    item._tooltipMouseLeave = mouseLeaveHandler;
}

/**
 * Add submenu functionality to treeview item
 */
function addSubmenuFunctionality(item, listItem) {
    const treeviewMenu = listItem.querySelector('.treeview-menu');
    if (!treeviewMenu) return;

    const clickHandler = function(e) {
        e.preventDefault();

        // Close other submenus
        closeAllSubmenus();

        // Hide tooltip
        hideTooltip(this);

        // Show submenu
        showSubmenu(this, listItem, treeviewMenu);
    };

    // Prevent default link behavior for treeview items
    item.addEventListener('click', clickHandler);

    // Store handler for removal
    item._submenuClick = clickHandler;
}

/**
 * Show tooltip
 */
function showTooltip(element, text) {
    // Remove existing tooltip and any AdminLTE tooltips
    hideTooltip(element);
    removeAdminLTETooltips();

    const tooltip = document.createElement('div');
    tooltip.className = 'sidebar-minimize-tooltip';
    tooltip.textContent = text;

    element.appendChild(tooltip);

    // Trigger animation
    setTimeout(() => {
        tooltip.classList.add('show');
    }, 10);

    element._tooltip = tooltip;
}

/**
 * Hide tooltip
 */
function hideTooltip(element) {
    if (element._tooltip) {
        element._tooltip.remove();
        element._tooltip = null;
    }
}

/**
 * Show submenu
 */
function showSubmenu(triggerElement, listItem, treeviewMenu) {
    const spanElement = triggerElement.querySelector('span');
    const menuTitle = spanElement ? spanElement.textContent.trim() : 'Menu';
    
    // Create submenu container
    const submenu = document.createElement('div');
    submenu.className = 'sidebar-minimize-submenu';
    submenu.setAttribute('data-menu-item', listItem.querySelector('a span')?.textContent || 'menu');
    
    // Create header
    const header = document.createElement('div');
    header.className = 'sidebar-minimize-submenu-header';
    header.textContent = menuTitle;
    submenu.appendChild(header);
    
    // Create items container
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'sidebar-minimize-submenu-items';
    
    // Get submenu items
    const submenuItems = treeviewMenu.querySelectorAll('li > a');
    submenuItems.forEach(submenuItem => {
        const item = document.createElement('a');
        item.className = 'sidebar-minimize-submenu-item';
        item.href = submenuItem.href;
        
        // Check if item is active
        if (submenuItem.parentElement.classList.contains('active')) {
            item.classList.add('active');
        }
        
        // Get icon and text
        const icon = submenuItem.querySelector('i');
        const text = submenuItem.textContent.trim();
        
        if (icon) {
            const iconClone = icon.cloneNode(true);
            item.appendChild(iconClone);
        }
        
        const textNode = document.createTextNode(text);
        item.appendChild(textNode);
        
        // Add click handler
        item.addEventListener('click', function() {
            closeAllSubmenus();
        });
        
        itemsContainer.appendChild(item);
    });
    
    submenu.appendChild(itemsContainer);
    
    // Position submenu relative to the clicked icon (viewport coordinates)
    const rect = triggerElement.getBoundingClientRect();
    const sidebarRect = document.querySelector('.main-sidebar').getBoundingClientRect();

    // Calculate horizontal position (to the right of sidebar with proper spacing)
    const leftPosition = sidebarRect.right + 5; // 5px gap from sidebar right edge

    // Calculate vertical position (aligned with the top of the clicked icon)
    const topPosition = rect.top;

    // Set positioning to fixed to position relative to viewport
    submenu.style.position = 'fixed';
    submenu.style.left = leftPosition + 'px';
    submenu.style.top = topPosition + 'px';

    // Add to DOM after positioning
    document.body.appendChild(submenu);

    // Trigger animation
    setTimeout(() => {
        submenu.classList.add('show');
    }, 10);

    // Adjust position if submenu goes beyond viewport
    setTimeout(() => {
        const updatedSubmenuRect = submenu.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        const headerHeight = document.querySelector('.main-header')?.offsetHeight || 50;

        let adjustedTop = topPosition;
        let adjustedLeft = leftPosition;
        let positionChanged = false;

        // Vertical adjustment - prevent submenu from going below viewport
        if (updatedSubmenuRect.bottom > viewportHeight - 20) {
            const overflow = updatedSubmenuRect.bottom - viewportHeight + 20;
            adjustedTop = Math.max(headerHeight + 10, topPosition - overflow);
            positionChanged = true;
        }

        // Vertical adjustment - prevent submenu from going above header
        if (updatedSubmenuRect.top < headerHeight + 10) {
            adjustedTop = headerHeight + 10;
            positionChanged = true;
        }

        // Horizontal adjustment - if submenu goes beyond right edge
        if (updatedSubmenuRect.right > viewportWidth - 20) {
            adjustedLeft = Math.max(sidebarRect.right + 5, viewportWidth - updatedSubmenuRect.width - 20);
            positionChanged = true;
        }

        // Apply adjustments if needed
        if (positionChanged) {
            submenu.style.top = adjustedTop + 'px';
            submenu.style.left = adjustedLeft + 'px';
        }

        // Update arrow position to point to the icon center
        updateArrowPosition(submenu, rect, adjustedTop, topPosition);
    }, 50);
}

/**
 * Update arrow position to point to the clicked icon
 */
function updateArrowPosition(submenu, iconRect, submenuTop, originalTop) {
    // Calculate the center of the icon in viewport coordinates
    const iconCenterY = iconRect.top + (iconRect.height / 2);

    // Calculate where the arrow should point relative to the submenu
    const submenuRect = submenu.getBoundingClientRect();
    const arrowTargetY = iconCenterY - submenuRect.top;

    // Constrain arrow position within submenu bounds (with padding)
    const minArrowY = 15;
    const maxArrowY = submenu.offsetHeight - 25;
    const arrowY = Math.max(minArrowY, Math.min(arrowTargetY, maxArrowY));

    // Update CSS custom property for arrow position
    submenu.style.setProperty('--arrow-top', arrowY + 'px');
}

/**
 * Close all submenus
 */
function closeAllSubmenus() {
    const openSubmenus = document.querySelectorAll('.sidebar-minimize-submenu');
    openSubmenus.forEach(submenu => {
        submenu.classList.remove('show');
        setTimeout(() => {
            if (submenu.parentElement) {
                submenu.remove();
            }
        }, 300);
    });
}

/**
 * Remove all tooltips
 */
function removeAllTooltips() {
    const tooltips = document.querySelectorAll('.sidebar-minimize-tooltip');
    tooltips.forEach(tooltip => {
        tooltip.remove();
    });
}

/**
 * Check if sidebar is minimized
 */
function isSidebarMinimized() {
    return document.body.classList.contains('sidebar-collapse') &&
           document.body.classList.contains('sidebar-mini');
}

/**
 * Disable AdminLTE tooltips for sidebar
 */
function disableAdminLTETooltips() {
    // Disable Bootstrap tooltips on sidebar items
    const sidebarItems = document.querySelectorAll('.main-sidebar .sidebar-menu > li > a');
    sidebarItems.forEach(item => {
        // Remove tooltip attributes
        item.removeAttribute('data-toggle');
        item.removeAttribute('data-placement');
        item.removeAttribute('title');
        item.removeAttribute('data-original-title');

        // Destroy any existing Bootstrap tooltip
        if (typeof $ !== 'undefined' && $(item).tooltip) {
            $(item).tooltip('destroy');
        }
    });
}

/**
 * Enable AdminLTE tooltips for sidebar (when not minimized)
 */
function enableAdminLTETooltips() {
    // Re-enable Bootstrap tooltips if needed
    if (typeof $ !== 'undefined') {
        $('[data-toggle="tooltip"]').tooltip();
    }
}

/**
 * Remove all AdminLTE/Bootstrap tooltips from DOM
 */
function removeAdminLTETooltips() {
    // Remove Bootstrap tooltip elements
    const bootstrapTooltips = document.querySelectorAll('.tooltip, .tooltip-inner, [role="tooltip"]');
    bootstrapTooltips.forEach(tooltip => {
        if (tooltip.parentElement) {
            tooltip.remove();
        }
    });

    // Remove any tooltip that might be attached to body
    const bodyTooltips = document.querySelectorAll('body > .tooltip');
    bodyTooltips.forEach(tooltip => {
        tooltip.remove();
    });
}

// Export functions for external use
window.SidebarMinimize = {
    init: initializeSidebarMinimize,
    closeAllSubmenus: closeAllSubmenus,
    removeAllTooltips: removeAllTooltips,
    removeAdminLTETooltips: removeAdminLTETooltips
};
