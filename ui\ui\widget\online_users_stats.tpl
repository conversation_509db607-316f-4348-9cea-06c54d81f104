<!-- Online Users Statistics Widget -->
{if isset($error_message)}
    <!-- Error State -->
    <div class="modern-card" style="background: white; border-radius: 16px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); overflow: hidden; margin-bottom: 24px;">
        <div style="padding: 40px 24px; text-align: center;">
            <i class="fa fa-exclamation-triangle" style="font-size: 3rem; color: #dc3545; margin-bottom: 16px;"></i>
            <h5 style="color: #dc3545; margin-bottom: 8px; font-weight: 500;">
                {Lang::T('Error')}
            </h5>
            <p style="color: #6c757d; margin: 0; font-size: 0.875rem;">
                {$error_message}
            </p>
        </div>
    </div>
{else}
    <!-- Online Users Cards -->
    <div class="row online-users-stats" style="margin: 0;">
        <!-- Hotspot Online Users -->
        <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0 12px 24px 0;">
            <div class="modern-card online-users-card" style="
                background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
                color: white;
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(33, 150, 243, 0.3);
                overflow: hidden;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            " onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 30px rgba(33, 150, 243, 0.4)'"
               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 20px rgba(33, 150, 243, 0.3)'">

                <!-- Card Content -->
                <div class="online-card-content" style="padding: 32px 24px;">
                    <div class="row" style="margin: 0; align-items: center;">
                        <div class="col-xs-8" style="padding: 0;">
                            <div class="online-card-label" style="font-size: 1rem; opacity: 0.9; margin-bottom: 12px; font-weight: 500;">
                                {Lang::T('Hotspot_Online_Users')}
                            </div>
                            <div class="online-card-value" style="font-size: 2.5rem; font-weight: 700; line-height: 1;">
                                {$hotspot_online}
                            </div>
                        </div>
                        <div class="col-xs-4 text-right" style="padding: 0;">
                            <i class="fa fa-wifi online-card-icon" style="font-size: 3rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <a href="{Text::url('reports/hotspot')}" class="online-card-footer" style="
                    display: block;
                    background: rgba(255, 255, 255, 0.1);
                    padding: 16px 24px;
                    border-top: 1px solid rgba(255, 255, 255, 0.1);
                    color: white;
                    text-decoration: none;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
                   onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                        <i class="fa fa-wifi" style="font-size: 0.875rem; opacity: 0.9;"></i>
                        <span style="font-size: 0.875rem; font-weight: 500; opacity: 0.9;">
                            {Lang::T('Hotspot Reports')}
                        </span>
                        <i class="fa fa-arrow-right" style="font-size: 0.75rem; opacity: 0.8;"></i>
                    </div>
                </a>
            </div>
        </div>

        <!-- PPPoE Online Users -->
        <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0 0 24px 12px;">
            <div class="modern-card online-users-card" style="
                background: linear-gradient(135deg, #9C27B0 0%, #E91E63 100%);
                color: white;
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(156, 39, 176, 0.3);
                overflow: hidden;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            " onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 30px rgba(156, 39, 176, 0.4)'"
               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 20px rgba(156, 39, 176, 0.3)'">

                <!-- Card Content -->
                <div class="online-card-content" style="padding: 32px 24px;">
                    <div class="row" style="margin: 0; align-items: center;">
                        <div class="col-xs-8" style="padding: 0;">
                            <div class="online-card-label" style="font-size: 1rem; opacity: 0.9; margin-bottom: 12px; font-weight: 500;">
                                {Lang::T('PPPoE_Online_Users')}
                            </div>
                            <div class="online-card-value" style="font-size: 2.5rem; font-weight: 700; line-height: 1;">
                                {$pppoe_online}
                            </div>
                        </div>
                        <div class="col-xs-4 text-right" style="padding: 0;">
                            <i class="fa fa-network-wired online-card-icon" style="font-size: 3rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <a href="{Text::url('reports/pppoe')}" class="online-card-footer" style="
                    display: block;
                    background: rgba(255, 255, 255, 0.1);
                    padding: 16px 24px;
                    border-top: 1px solid rgba(255, 255, 255, 0.1);
                    color: white;
                    text-decoration: none;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
                   onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                        <i class="fa fa-link" style="font-size: 0.875rem; opacity: 0.9;"></i>
                        <span style="font-size: 0.875rem; font-weight: 500; opacity: 0.9;">
                            {Lang::T('PPPoE Reports')}
                        </span>
                        <i class="fa fa-arrow-right" style="font-size: 0.75rem; opacity: 0.8;"></i>
                    </div>
                </a>
            </div>
        </div>
    </div>
{/if}

<!-- Mobile Responsive Adjustments -->
<style>
@media (max-width: 768px) {
    /* Online Users Stats - 2 Column Grid on Mobile */
    .online-users-stats .online-users-card {
        margin-bottom: 16px !important;
        min-height: 160px !important;
    }

    .online-users-stats .col-xs-6 {
        padding-left: 8px !important;
        padding-right: 8px !important;
        margin-bottom: 16px;
    }

    .online-card-content {
        padding: 24px 20px !important;
    }

    .online-card-label {
        font-size: 0.875rem !important;
        margin-bottom: 8px !important;
    }

    .online-card-value {
        font-size: 2rem !important;
    }

    .online-card-icon {
        font-size: 2.5rem !important;
    }

    .online-card-footer {
        padding: 12px 20px !important;
    }

    .online-card-footer span {
        font-size: 0.8rem !important;
    }

    .online-card-footer i {
        font-size: 0.75rem !important;
    }
}

@media (max-width: 480px) {
    /* Extra Small Mobile - Maintain 2 columns but smaller */
    .online-users-stats .online-users-card {
        min-height: 150px !important;
    }

    .online-users-stats .col-xs-6 {
        padding-left: 6px !important;
        padding-right: 6px !important;
    }

    .online-card-content {
        padding: 20px 16px !important;
    }

    .online-card-label {
        font-size: 0.75rem !important;
    }

    .online-card-value {
        font-size: 1.75rem !important;
    }

    .online-card-icon {
        font-size: 2rem !important;
    }

    .online-card-footer {
        padding: 10px 16px !important;
    }

    .online-card-footer span {
        font-size: 0.75rem !important;
    }

    .online-card-footer i {
        font-size: 0.7rem !important;
    }
}

/* Footer hover effects */
.online-card-footer:hover {
    text-decoration: none !important;
}

.online-card-footer:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5) !important;
    outline-offset: 2px !important;
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .online-card-footer:active {
        background: rgba(255, 255, 255, 0.25) !important;
        transform: scale(0.95) !important;
        transition: all 0.1s ease !important;
    }
}
</style>
