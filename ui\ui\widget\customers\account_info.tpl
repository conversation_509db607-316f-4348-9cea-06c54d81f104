<!-- Balance Card -->
<div class="row">
    {if $_c['enable_balance'] == 'yes'}
    <div class="col-md-12">
        <div class="modern-card balance-card">
            <div class="balance-icon">
                <i class="fa fa-wallet"></i>
            </div>
            <div class="balance-label">Saldo</div>
            <div class="balance-amount">{Lang::moneyFormat($_user['balance'])}</div>
            {if $_user['auto_renewal'] == 1}
                <a href="{Text::url('home&renewal=0')}" class="promo-cta" style="font-size: 0.75rem; padding: 4px 12px;"
                    onclick="return ask(this, '{Lang::T('Disable auto renewal?')}')">{Lang::T('Auto Renewal On')}</a>
            {else}
                <a href="{Text::url('home&renewal=1')}" class="promo-cta" style="font-size: 0.75rem; padding: 4px 12px;"
                    onclick="return ask(this, '{Lang::T('Enable auto renewal?')}')">{Lang::T('Auto Renewal Off')}</a>
            {/if}
        </div>
    </div>
    {/if}

</div>



<!-- Modern Account Information Card -->
<div class="modern-account-card" style="
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    border-radius: 20px;
    padding: 24px;
    margin-top: 24px;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 191, 165, 0.3);
    position: relative;
    overflow: hidden;
">
    <!-- Background Pattern -->
    <div style="
        position: absolute;
        top: -50px;
        right: -50px;
        width: 150px;
        height: 150px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        z-index: 1;
    "></div>

    <!-- Header Section -->
    <div style="position: relative; z-index: 2; margin-bottom: 20px;">
        <div style="display: flex; align-items: center; gap: 12px;">
            <i class="fa fa-user-circle" style="font-size: 1.5rem; opacity: 0.9;"></i>
            <h3 style="margin: 0; font-size: 1.25rem; font-weight: 600;">
                {Lang::T('Your Account Information')}
            </h3>
        </div>
    </div>

    <!-- Account Details Section -->
    <div style="position: relative; z-index: 2;">
        <!-- Username Section -->
        <div style="margin-bottom: 20px;">
            <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 8px; font-weight: 500;">
                {Lang::T('Usernames')}
            </div>
            <div style="
                background: rgba(255, 255, 255, 0.15);
                padding: 12px 16px;
                border-radius: 12px;
                font-size: 1rem;
                font-weight: 600;
                border: 1px solid rgba(255, 255, 255, 0.2);
            ">
                {$_user['username']}
            </div>
        </div>

        <!-- Password Section -->
        <div style="margin-bottom: 20px;">
            <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 8px; font-weight: 500;">
                {Lang::T('Password')}
            </div>
            <div style="position: relative;">
                <input type="password" value="{$_user['password']}" style="
                    width: 100%;
                    background: rgba(255, 255, 255, 0.15);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 12px 16px;
                    font-size: 1rem;
                    font-weight: 600;
                    color: white;
                    outline: none;
                " onmouseleave="this.type = 'password'"
                   onmouseenter="this.type = 'text'"
                   onclick="this.select()"
                   placeholder="••••••••">
            </div>
        </div>

        <!-- Service Type Section -->
        <div style="margin-bottom: 20px;">
            <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 8px; font-weight: 500;">
                {Lang::T('Service Type')}
            </div>
            <div style="
                background: rgba(255, 255, 255, 0.15);
                padding: 12px 16px;
                border-radius: 12px;
                font-size: 1rem;
                font-weight: 600;
                border: 1px solid rgba(255, 255, 255, 0.2);
                display: flex;
                align-items: center;
                gap: 8px;
            ">
                {if $_user.service_type == 'Hotspot'}
                    <i class="fa fa-wifi" style="opacity: 0.8;"></i>
                    Hotspot
                {elseif $_user.service_type == 'PPPoE'}
                    <i class="fa fa-link" style="opacity: 0.8;"></i>
                    PPPoE
                {elseif $_user.service_type == 'VPN'}
                    <i class="fa fa-shield" style="opacity: 0.8;"></i>
                    VPN
                {elseif $_user.service_type == 'Others' || $_user.service_type == null}
                    <i class="fa fa-globe" style="opacity: 0.8;"></i>
                    Others
                {/if}
            </div>
        </div>
    </div>

    {* Additional Billing Section *}
    {if $abills && count($abills)>0}
        <!-- Divider -->
        <div style="
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
            margin: 20px 0;
            position: relative;
            z-index: 2;
        "></div>

        <div style="position: relative; z-index: 2;">
            <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 16px; font-weight: 500;">
                {Lang::T('Additional Billing')}
            </div>

            {assign var="total" value=0}
            {foreach $abills as $k => $v}
                <div style="
                    background: rgba(255, 255, 255, 0.1);
                    padding: 12px 16px;
                    border-radius: 12px;
                    margin-bottom: 8px;
                    border: 1px solid rgba(255, 255, 255, 0.15);
                ">
                    <div class="row" style="margin: 0; align-items: center;">
                        <div class="col-xs-6" style="padding: 0;">
                            <div style="font-size: 0.875rem; font-weight: 500;">
                                {str_replace(' Bill', '', $k)}
                            </div>
                        </div>
                        <div class="col-xs-6 text-right" style="padding: 0;">
                            <div style="font-size: 0.875rem; font-weight: 600;">
                                {if strpos($v, ':') === false}
                                    {Lang::moneyFormat($v)}
                                    <sup style="font-size: 0.75rem; opacity: 0.8;" title="recurring">∞</sup>
                                    {assign var="total" value=$v+$total}
                                {else}
                                    {assign var="exp" value=explode(':',$v)}
                                    {Lang::moneyFormat($exp[0])}
                                    <sup style="font-size: 0.75rem; opacity: 0.8;" title="{$exp[1]} more times">
                                        {if $exp[1]==0}{Lang::T('paid off')}{else}{$exp[1]}x{/if}
                                    </sup>
                                    {if $exp[1]>0}
                                        {assign var="total" value=$exp[0]+$total}
                                    {/if}
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            {/foreach}

            <!-- Total Section -->
            <div style="
                background: rgba(255, 255, 255, 0.2);
                padding: 16px;
                border-radius: 12px;
                margin-top: 12px;
                border: 1px solid rgba(255, 255, 255, 0.25);
            ">
                <div class="row" style="margin: 0; align-items: center;">
                    <div class="col-xs-6" style="padding: 0;">
                        <div style="font-size: 1rem; font-weight: 600;">
                            {Lang::T('Total')}
                        </div>
                    </div>
                    <div class="col-xs-6 text-right" style="padding: 0;">
                        <div style="font-size: 1.125rem; font-weight: 700;">
                            {if $total==0}
                                {ucwords(Lang::T('paid off'))}
                            {else}
                                {Lang::moneyFormat($total)}
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/if}
</div>

<!-- Mobile Responsive Styles -->
<style>
@media (max-width: 768px) {
    .modern-account-card {
        margin-top: 16px !important;
        padding: 20px !important;
    }

    .modern-account-card h3 {
        font-size: 1.125rem !important;
    }

    .modern-account-card input {
        font-size: 0.875rem !important;
    }
}

@media (max-width: 480px) {
    .modern-account-card {
        padding: 16px !important;
        border-radius: 16px !important;
    }

    .modern-account-card h3 {
        font-size: 1rem !important;
    }

    .modern-account-card div[style*="font-size: 1rem"] {
        font-size: 0.875rem !important;
    }

    .modern-account-card div[style*="font-size: 1.125rem"] {
        font-size: 1rem !important;
    }

    .modern-account-card input {
        font-size: 0.875rem !important;
        padding: 10px 12px !important;
    }
}
</style>
