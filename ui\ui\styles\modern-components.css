/* Modern Components for PHPNuxBill Dashboard */

/* Modern Card Components */
.modern-card {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* Balance Card */
.balance-card {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: white;
    padding: 24px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.balance-amount {
    font-size: 2rem;
    font-weight: 700;
    margin: 8px 0;
}

.balance-label {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 500;
}

.balance-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2rem;
    opacity: 0.3;
}



/* Quick Action Buttons */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin: 24px 0;
}

.quick-action-btn {
    background: #ffffff;
    border: none;
    border-radius: 16px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    text-decoration: none;
    color: #334155;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    color: #334155;
    text-decoration: none;
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 12px;
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.quick-action-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
}

/* Admin Quick Actions Grid */
.admin-quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin: 0;
}

.admin-quick-action-item {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    text-decoration: none;
    color: #334155;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
}

.admin-quick-action-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    color: #334155;
    text-decoration: none;
    border-color: #cbd5e1;
}

.admin-quick-action-icon {
    width: 56px;
    height: 56px;
    margin: 0 auto 12px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-quick-action-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #475569;
    text-align: center;
    line-height: 1.3;
}

/* Promotional Banner */
.promo-banner {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    border-radius: 20px;
    padding: 24px;
    color: white;
    position: relative;
    overflow: hidden;
    margin: 24px 0;
}

.promo-banner::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.promo-content {
    position: relative;
    z-index: 2;
}

.promo-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.promo-subtitle {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 16px;
}

.promo-cta {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.promo-cta:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
}

/* Service Grid */
.service-grid {
    margin: 24px 0;
}

.service-section {
    margin-bottom: 24px;
}

.service-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
    padding-left: 4px;
}

.service-items {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
}

@media (max-width: 768px) {
    .service-items {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .service-items {
        grid-template-columns: repeat(2, 1fr);
    }
}

.service-item {
    background: #ffffff;
    border-radius: 16px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    text-decoration: none;
    color: #334155;
}

.service-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    color: #334155;
    text-decoration: none;
}

.service-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.service-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
}

/* Provider specific colors */
.telkomsel { background: linear-gradient(135deg, #e60012 0%, #ff4444 100%); color: white; }
.indosat { background: linear-gradient(135deg, #ffcc00 0%, #ffd700 100%); color: #333; }
.xl { background: linear-gradient(135deg, #0066cc 0%, #0080ff 100%); color: white; }
.three { background: linear-gradient(135deg, #ff6600 0%, #ff8833 100%); color: white; }

/* Greeting Section */
.greeting-section {
    padding: 24px 0;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 24px;
}

.greeting-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.user-name {
    color: #00BFA5;
}

.greeting-time {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 4px;
}

/* Modern Header Styles */
.modern-header {
    position: relative;
    z-index: 1001;
}

/* Fixed Background Colors - Not affected by theme toggle */
/* Header Background - Fixed Teal Color */
.main-header,
.main-header.navbar,
.main-header .navbar,
body .main-header,
html .main-header {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    border-bottom: none !important;
}

/* Desktop Logo - Fixed Teal Color */
.desktop-logo,
.main-header .logo,
body .main-header .logo,
html .main-header .logo {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    color: white !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

/* Sidebar Background - Fixed Dark Color */
.main-sidebar,
.sidebar,
body .main-sidebar,
html .main-sidebar,
body .sidebar,
html .sidebar {
    background: #2c3e50 !important;
    background-color: #2c3e50 !important;
    background-image: none !important;
}

/* Sidebar Menu Items - Fixed Colors */
.main-sidebar .sidebar-menu > li > a,
.sidebar-menu > li > a,
body .main-sidebar .sidebar-menu > li > a,
html .main-sidebar .sidebar-menu > li > a {
    background: transparent !important;
    color: #ecf0f1 !important;
    border: none !important;
}

/* Sidebar Menu Items Hover */
.main-sidebar .sidebar-menu > li > a:hover,
.sidebar-menu > li > a:hover,
body .main-sidebar .sidebar-menu > li > a:hover,
html .main-sidebar .sidebar-menu > li > a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Sidebar Menu Items Active */
.main-sidebar .sidebar-menu > li.active > a,
.sidebar-menu > li.active > a,
body .main-sidebar .sidebar-menu > li.active > a,
html .main-sidebar .sidebar-menu > li.active > a {
    background: #00BFA5 !important;
    color: white !important;
    border-left: 3px solid #ffffff !important;
}

/* Sidebar Submenu */
.main-sidebar .sidebar-menu .treeview-menu,
.sidebar-menu .treeview-menu,
body .main-sidebar .sidebar-menu .treeview-menu,
html .main-sidebar .sidebar-menu .treeview-menu {
    background: #34495e !important;
}

.main-sidebar .sidebar-menu .treeview-menu > li > a,
.sidebar-menu .treeview-menu > li > a,
body .main-sidebar .sidebar-menu .treeview-menu > li > a,
html .main-sidebar .sidebar-menu .treeview-menu > li > a {
    background: transparent !important;
    color: #bdc3c7 !important;
}

.main-sidebar .sidebar-menu .treeview-menu > li > a:hover,
.sidebar-menu .treeview-menu > li > a:hover,
body .main-sidebar .sidebar-menu .treeview-menu > li > a:hover,
html .main-sidebar .sidebar-menu .treeview-menu > li > a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Desktop Header Elements */
.desktop-logo {
    background: #00BFA5 !important;
    color: white !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

/* Navbar and Header Elements - Fixed Colors */
.navbar,
.navbar-static-top,
body .navbar,
html .navbar,
body .navbar-static-top,
html .navbar-static-top {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Navbar Brand */
.navbar-brand,
body .navbar-brand,
html .navbar-brand {
    background: #00BFA5 !important;
    color: white !important;
}

/* Header User Menu */
.navbar-nav > .user-menu > .dropdown-toggle,
.navbar-nav .user-menu .dropdown-toggle,
body .navbar-nav > .user-menu > .dropdown-toggle,
html .navbar-nav > .user-menu > .dropdown-toggle {
    background: transparent !important;
    color: white !important;
}

/* Header Icons and Buttons */
.navbar-nav > li > a,
.navbar-nav li a,
body .navbar-nav > li > a,
html .navbar-nav > li > a {
    color: white !important;
    background: transparent !important;
}

.navbar-nav > li > a:hover,
.navbar-nav li a:hover,
body .navbar-nav > li > a:hover,
html .navbar-nav > li > a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Sidebar Toggle Button */
.sidebar-toggle,
.navbar-nav .sidebar-toggle,
body .sidebar-toggle,
html .sidebar-toggle {
    background: transparent !important;
    color: white !important;
    border: none !important;
}

.sidebar-toggle:hover,
.navbar-nav .sidebar-toggle:hover,
body .sidebar-toggle:hover,
html .sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Override Theme Colors - Force Fixed Colors */
/* Light Theme Override */
body.light-mode .main-header,
body.light-mode .navbar,
body.light-mode .navbar-static-top,
html.light-mode .main-header,
html.light-mode .navbar {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
}

body.light-mode .main-sidebar,
body.light-mode .sidebar,
html.light-mode .main-sidebar,
html.light-mode .sidebar {
    background: #2c3e50 !important;
    background-color: #2c3e50 !important;
}

/* Dark Theme Override */
body.dark-mode .main-header,
body.dark-mode .navbar,
body.dark-mode .navbar-static-top,
html.dark-mode .main-header,
html.dark-mode .navbar {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
}

body.dark-mode .main-sidebar,
body.dark-mode .sidebar,
html.dark-mode .main-sidebar,
html.dark-mode .sidebar {
    background: #2c3e50 !important;
    background-color: #2c3e50 !important;
}

/* Additional Header Elements */
.content-header,
body .content-header,
html .content-header {
    background: transparent !important;
}

/* Search Box in Header */
.navbar-form,
.navbar-form .form-control,
body .navbar-form,
html .navbar-form {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

.navbar-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Ultra High Specificity Override - Force Fixed Colors */
/* Override all possible theme variations */
body.modern-skin-dark .main-header,
body.modern-skin-dark .main-header .navbar,
body.modern-skin-dark .navbar,
body.modern-skin-dark .navbar-static-top,
html.modern-skin-dark .main-header,
html.modern-skin-dark .navbar,
body.dark-mode .main-header,
body.dark-mode .main-header .navbar,
body.dark-mode .navbar,
body.dark-mode .navbar-static-top,
html.dark-mode .main-header,
html.dark-mode .navbar,
body.light-mode .main-header,
body.light-mode .main-header .navbar,
body.light-mode .navbar,
body.light-mode .navbar-static-top,
html.light-mode .main-header,
html.light-mode .navbar {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Override all possible sidebar variations */
body.modern-skin-dark .main-sidebar,
body.modern-skin-dark .main-sidebar .sidebar,
body.modern-skin-dark .sidebar,
html.modern-skin-dark .main-sidebar,
html.modern-skin-dark .sidebar,
body.dark-mode .main-sidebar,
body.dark-mode .main-sidebar .sidebar,
body.dark-mode .sidebar,
html.dark-mode .main-sidebar,
html.dark-mode .sidebar,
body.light-mode .main-sidebar,
body.light-mode .main-sidebar .sidebar,
body.light-mode .sidebar,
html.light-mode .main-sidebar,
html.light-mode .sidebar {
    background: #2c3e50 !important;
    background-color: #2c3e50 !important;
    background-image: none !important;
}

/* Override all possible logo variations */
body.modern-skin-dark .main-header .logo,
body.modern-skin-dark .desktop-logo,
html.modern-skin-dark .main-header .logo,
html.modern-skin-dark .desktop-logo,
body.dark-mode .main-header .logo,
body.dark-mode .desktop-logo,
html.dark-mode .main-header .logo,
html.dark-mode .desktop-logo,
body.light-mode .main-header .logo,
body.light-mode .desktop-logo,
html.light-mode .main-header .logo,
html.light-mode .desktop-logo {
    background: #00BFA5 !important;
    background-color: #00BFA5 !important;
    background-image: none !important;
    color: white !important;
}

/* Override all possible menu item variations */
body.modern-skin-dark .main-sidebar .sidebar-menu > li > a,
body.modern-skin-dark .sidebar-menu > li > a,
html.modern-skin-dark .main-sidebar .sidebar-menu > li > a,
html.modern-skin-dark .sidebar-menu > li > a,
body.dark-mode .main-sidebar .sidebar-menu > li > a,
body.dark-mode .sidebar-menu > li > a,
html.dark-mode .main-sidebar .sidebar-menu > li > a,
html.dark-mode .sidebar-menu > li > a,
body.light-mode .main-sidebar .sidebar-menu > li > a,
body.light-mode .sidebar-menu > li > a,
html.light-mode .main-sidebar .sidebar-menu > li > a,
html.light-mode .sidebar-menu > li > a {
    background: transparent !important;
    color: #ecf0f1 !important;
}

/* Override all possible active menu item variations */
body.modern-skin-dark .main-sidebar .sidebar-menu > li.active > a,
body.modern-skin-dark .sidebar-menu > li.active > a,
html.modern-skin-dark .main-sidebar .sidebar-menu > li.active > a,
html.modern-skin-dark .sidebar-menu > li.active > a,
body.dark-mode .main-sidebar .sidebar-menu > li.active > a,
body.dark-mode .sidebar-menu > li.active > a,
html.dark-mode .main-sidebar .sidebar-menu > li.active > a,
html.dark-mode .sidebar-menu > li.active > a,
body.light-mode .main-sidebar .sidebar-menu > li.active > a,
body.light-mode .sidebar-menu > li.active > a,
html.light-mode .main-sidebar .sidebar-menu > li.active > a,
html.light-mode .sidebar-menu > li.active > a {
    background: #00BFA5 !important;
    color: white !important;
    border-left: 3px solid #ffffff !important;
}

.desktop-nav {
    display: flex;
    align-items: center;
}

.desktop-sidebar-toggle {
    background: transparent;
    border: none;
    color: #64748b;
    padding: 15px;
    transition: all 0.3s ease;
}

.desktop-sidebar-toggle:hover {
    background: rgba(100, 116, 139, 0.1);
    color: #1e293b;
}

/* Mobile Header */
.mobile-header {
    display: none;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: white;
    position: relative;
    z-index: 1002;
}

.mobile-logo {
    text-decoration: none;
    color: white;
}

.mobile-logo-text {
    font-size: 1.125rem;
    font-weight: 600;
}

.mobile-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.mobile-balance {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.mobile-search-btn,
.mobile-notifications-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
}

.mobile-search-btn:hover,
.mobile-notifications-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.mobile-notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ff4444;
    color: white;
    font-size: 0.625rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.mobile-user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-user-avatar:hover {
    border-color: white;
    transform: scale(1.1);
}

/* Mobile User Dropdown Menu */
.mobile-user-menu.dropdown {
    position: relative;
}

.mobile-user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    min-width: 280px;
    z-index: 1000;
    border: 1px solid #e2e8f0;
    margin-top: 8px;
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-user-menu.dropdown.open .mobile-user-dropdown-menu,
.mobile-user-menu.dropdown .dropdown-menu.show {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

/* Mobile Dropdowns (for notifications) */
.mobile-notifications-dropdown {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding-top: 60px;
}

.mobile-notifications-dropdown .mobile-notifications-header {
    background: #ffffff;
    border-radius: 20px 20px 0 0;
    padding: 24px;
    width: 100%;
    max-width: 400px;
    margin: 0 16px;
}

.mobile-user-dropdown-menu .mobile-user-info {
    display: flex;
    align-items: center;
    gap: 16px;
    border-bottom: 1px solid #e2e8f0;
    padding: 20px;
    margin: 0;
    border-radius: 12px 12px 0 0;
}

.mobile-user-avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #00BFA5;
}

.mobile-user-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.mobile-user-contact,
.mobile-user-role {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 2px;
}

.mobile-user-balance {
    font-size: 0.875rem;
    color: #00BFA5;
    font-weight: 600;
}

.mobile-user-dropdown-menu .mobile-user-actions {
    background: #ffffff;
    padding: 0 20px 20px;
    border-radius: 0 0 12px 12px;
    width: 100%;
    margin: 0;
}

.mobile-user-action {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    text-decoration: none;
    color: #64748b;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.mobile-user-action:hover {
    color: #1e293b;
    text-decoration: none;
}

.mobile-user-action.logout {
    color: #dc2626;
    border-bottom: none;
}

.mobile-user-action.logout:hover {
    color: #b91c1c;
}

/* Enhanced Search Overlay */
.modern-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.modern-search-container {
    background: #ffffff;
    border-radius: 20px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.modern-search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modern-search-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.modern-search-close {
    background: #f1f5f9;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modern-search-close:hover {
    background: #e2e8f0;
    color: #1e293b;
}

.modern-search-input-container {
    position: relative;
    margin-bottom: 20px;
}

.modern-search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
}

.modern-search-input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.modern-search-input:focus {
    outline: none;
    border-color: #00BFA5;
    box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
}

.modern-search-results {
    max-height: 300px;
    overflow-y: auto;
}

/* Sidebar Minimize/Collapse Styles */
.sidebar-mini.sidebar-collapse .main-sidebar {
    width: 60px !important;
    transition: width 0.3s ease;
}

.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a {
    padding: 12px 8px !important;
    text-align: center;
    border-radius: 8px !important;
    margin: 4px 8px !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a > span {
    display: none !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a > i {
    font-size: 1.2rem !important;
    margin-right: 0 !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > .treeview-menu {
    display: none !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li.treeview > a > .pull-right-container {
    display: none !important;
}

.sidebar-mini.sidebar-collapse .content-wrapper {
    margin-left: 60px !important;
    transition: margin-left 0.3s ease;
}

.sidebar-mini.sidebar-collapse .main-header .navbar {
    margin-left: 60px !important;
    transition: margin-left 0.3s ease;
}

.sidebar-mini.sidebar-collapse .main-header .logo {
    width: 60px !important;
    transition: width 0.3s ease;
}

.sidebar-mini.sidebar-collapse .main-header .logo .logo-lg {
    display: none !important;
}

.sidebar-mini.sidebar-collapse .main-header .logo .logo-mini {
    display: block !important;
    text-align: center;
    font-size: 1.2rem;
    padding: 15px 0;
}

/* Enhanced hover effects for minimized sidebar */
.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a:hover {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%) !important;
    color: white !important;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 191, 165, 0.4);
}

.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li.active > a {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%) !important;
    color: white !important;
    border-left: none !important;
    box-shadow: 0 4px 15px rgba(0, 191, 165, 0.4);
}

/* Enhanced Sidebar Minimize with Tooltip and Submenu Support */

/* Disable AdminLTE's built-in tooltip for minimized sidebar */
.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a > span {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Disable any AdminLTE tooltip elements globally when sidebar is minimized */
.sidebar-mini.sidebar-collapse .tooltip,
.sidebar-mini.sidebar-collapse .tooltip-inner,
.sidebar-mini.sidebar-collapse [role="tooltip"],
.sidebar-mini.sidebar-collapse .main-sidebar .tooltip,
.sidebar-mini.sidebar-collapse .main-sidebar [data-toggle="tooltip"],
.sidebar-mini.sidebar-collapse .main-sidebar .tooltip-inner {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* Prevent AdminLTE from showing text on hover */
.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a:hover > span,
.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a:focus > span {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Override any AdminLTE tooltip positioning */
.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a::after,
.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a::before {
    content: none !important;
    display: none !important;
}

/* Tooltip for minimized sidebar */
.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a {
    position: relative;
    overflow: visible !important;
}

.sidebar-minimize-tooltip {
    position: absolute;
    left: 65px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.sidebar-minimize-tooltip::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-right-color: #1e293b;
}

.sidebar-minimize-tooltip.show {
    opacity: 1;
    transform: translateY(-50%) translateX(5px);
}

/* Submenu dropdown for minimized sidebar */
.sidebar-minimize-submenu {
    position: fixed;
    left: 65px;
    top: 0;
    min-width: 220px;
    max-width: 280px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-15px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    --arrow-top: 20px;
}

.sidebar-minimize-submenu.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0) scale(1);
}

.sidebar-minimize-submenu::before {
    content: '';
    position: absolute;
    left: -9px;
    top: var(--arrow-top, 20px);
    border: 9px solid transparent;
    border-right-color: white;
    z-index: 1051;
    filter: drop-shadow(-2px 0 4px rgba(0, 0, 0, 0.1));
}

.sidebar-minimize-submenu::after {
    content: '';
    position: absolute;
    left: -10px;
    top: calc(var(--arrow-top, 20px) - 1px);
    border: 10px solid transparent;
    border-right-color: #e2e8f0;
    z-index: 1050;
}

.sidebar-minimize-submenu-header {
    padding: 16px 20px 12px;
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-minimize-submenu-items {
    padding: 8px 0;
    max-height: 300px;
    overflow-y: auto;
}

.sidebar-minimize-submenu-item {
    display: block;
    padding: 12px 20px;
    color: #1e293b;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.sidebar-minimize-submenu-item:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #00BFA5;
    text-decoration: none;
    padding-left: 24px;
}

.sidebar-minimize-submenu-item.active {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: white;
}

.sidebar-minimize-submenu-item.active:hover {
    background: linear-gradient(135deg, #00ACC1 0%, #26A69A 100%);
    color: white;
    padding-left: 24px;
}

.sidebar-minimize-submenu-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    font-size: 0.875rem;
}

/* Dark mode support */
.dark-mode .sidebar-minimize-submenu {
    background: #2d3748;
    border-color: #4a5568;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3), 0 4px 15px rgba(0, 0, 0, 0.2);
}

.dark-mode .sidebar-minimize-submenu::before {
    border-right-color: #2d3748;
}

.dark-mode .sidebar-minimize-submenu::after {
    border-right-color: #4a5568;
}

.dark-mode .sidebar-minimize-submenu-item {
    color: #e2e8f0;
}

.dark-mode .sidebar-minimize-submenu-item:hover {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    color: #00BFA5;
}

/* Responsive - hide on mobile */
@media (max-width: 768px) {
    .sidebar-minimize-tooltip,
    .sidebar-minimize-submenu {
        display: none !important;
    }
}

/* ===== MODERN PROFILE PICTURE PLACEHOLDERS ===== */

/* Base avatar container */
.modern-avatar {
    position: relative;
    display: inline-block;
    overflow: hidden;
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 191, 165, 0.3);
    transition: all 0.3s ease;
}

.modern-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 191, 165, 0.4);
}

/* Avatar sizes */
.modern-avatar.size-xs {
    width: 24px;
    height: 24px;
    font-size: 10px;
    line-height: 20px;
}

.modern-avatar.size-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    line-height: 28px;
}

.modern-avatar.size-md {
    width: 48px;
    height: 48px;
    font-size: 16px;
    line-height: 44px;
}

.modern-avatar.size-lg {
    width: 64px;
    height: 64px;
    font-size: 20px;
    line-height: 60px;
}

.modern-avatar.size-xl {
    width: 96px;
    height: 96px;
    font-size: 28px;
    line-height: 92px;
}

.modern-avatar.size-xxl {
    width: 128px;
    height: 128px;
    font-size: 36px;
    line-height: 124px;
}

.modern-avatar.size-profile {
    width: 200px;
    height: 200px;
    font-size: 48px;
    line-height: 196px;
}

/* Avatar with image */
.modern-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* Initials display */
.modern-avatar .avatar-initials {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Generic icon fallback */
.modern-avatar .avatar-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 60%;
    opacity: 0.9;
}

/* User type specific colors */
.modern-avatar.user-admin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.modern-avatar.user-admin:hover {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.modern-avatar.user-superadmin {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
}

.modern-avatar.user-superadmin:hover {
    box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
}

.modern-avatar.user-agent {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.modern-avatar.user-agent:hover {
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.modern-avatar.user-report {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    box-shadow: 0 4px 12px rgba(67, 233, 123, 0.3);
}

.modern-avatar.user-report:hover {
    box-shadow: 0 6px 20px rgba(67, 233, 123, 0.4);
}

.modern-avatar.user-customer {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    box-shadow: 0 4px 12px rgba(0, 191, 165, 0.3);
}

.modern-avatar.user-customer:hover {
    box-shadow: 0 6px 20px rgba(0, 191, 165, 0.4);
}

/* Dark mode support */
.dark-mode .modern-avatar {
    border-color: rgba(255, 255, 255, 0.1);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .modern-avatar.size-profile {
        width: 120px;
        height: 120px;
        font-size: 32px;
        line-height: 116px;
    }
}

/* Legacy avatar compatibility */
.user-image,
.mobile-user-avatar,
.mobile-user-avatar-large,
.profile-user-img,
.img-circle {
    border-radius: 50% !important;
}

/* Responsive Layout Adjustments */
@media (max-width: 768px) {
    /* Hide sidebar on mobile */
    .main-sidebar,
    .desktop-sidebar {
        display: none !important;
    }

    /* Hide desktop elements */
    .desktop-logo,
    .desktop-nav,
    .desktop-sidebar-toggle {
        display: none !important;
    }

    /* Show mobile header */
    .mobile-header {
        display: flex !important;
    }

    /* Adjust content wrapper for mobile */
    .content-wrapper {
        margin-left: 0 !important;
        padding-bottom: 80px !important;
        margin-top: 60px !important;
    }

    /* Adjust main header for mobile */
    .main-header .navbar {
        margin-left: 0 !important;
        display: none !important;
    }

    /* Hide desktop footer on mobile */
    .desktop-footer {
        display: none !important;
    }

    /* Show bottom navigation on mobile */
    .bottom-nav {
        display: block !important;
    }

    /* Mobile dropdown adjustments */
    .mobile-user-dropdown-menu {
        position: fixed !important;
        top: 60px !important;
        right: 16px !important;
        left: auto !important;
        max-width: 300px !important;
        width: calc(100vw - 32px) !important;
    }

    /* Ensure dropdown is above other elements */
    .mobile-user-menu.dropdown .dropdown-menu.show {
        z-index: 1050 !important;
    }
}

@media (min-width: 769px) {
    /* Hide bottom navigation on desktop */
    .bottom-nav {
        display: none !important;
    }

    /* Show desktop elements */
    .desktop-sidebar,
    .desktop-footer {
        display: block !important;
    }

    /* Normal content padding for desktop */
    .content-wrapper {
        padding-bottom: 20px !important;
    }
}

/* ===== MOBILE DASHBOARD GRID LAYOUT ===== */

/* Mobile Dashboard Grid Improvements */
@media (max-width: 768px) {
    /* Dashboard Stats Cards - 2 Column Grid on Mobile */
    .admin-dashboard-stats .dashboard-stat-card {
        margin-bottom: 16px;
        min-height: 140px !important;
    }

    .admin-dashboard-stats .dashboard-stat-card > div:first-child {
        padding: 20px !important;
    }

    .admin-dashboard-stats .stat-label {
        font-size: 0.75rem !important;
        margin-bottom: 6px !important;
    }

    .admin-dashboard-stats .stat-value {
        font-size: 1.75rem !important;
        line-height: 1.2;
    }

    .admin-dashboard-stats .stat-icon {
        font-size: 2rem !important;
    }

    /* Ensure proper spacing between rows */
    .admin-dashboard-stats .col-xs-6 {
        padding-left: 8px;
        padding-right: 8px;
        margin-bottom: 16px;
    }

    /* Quick Actions Grid - 2 Columns on Mobile */
    .admin-quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .admin-quick-action-item {
        min-height: 100px;
        padding: 16px;
    }

    .admin-quick-action-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }

    .admin-quick-action-label {
        font-size: 0.8rem;
    }

    /* System Status Cards - Stack on Mobile */
    .admin-system-status-section .dashboard-system-card,
    .admin-system-status-section .dashboard-activity-card {
        margin-bottom: 16px;
        padding: 20px !important;
    }

    .admin-system-status-section .col-xs-12 {
        margin-bottom: 16px;
    }

    .system-status-item {
        padding: 12px !important;
    }

    .activity-list {
        max-height: 150px !important;
    }

    .activity-item {
        padding: 6px 0 !important;
    }

    /* Customer Dashboard Adjustments */
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    /* Mobile-specific positioning for quick actions */
    .quick-actions-widget {
        order: 2 !important; /* Position after announcement */
    }

    /* Quick actions mobile optimization */
    .quick-actions {
        margin: 16px 0;
        padding: 0 8px;
    }

    .quick-action-btn {
        padding: 16px;
        min-height: 100px;
    }

    .quick-action-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .quick-action-label {
        font-size: 0.8rem;
        margin-top: 8px;
    }

    /* Touch-friendly improvements for mobile */
    .quick-action-btn {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        touch-action: manipulation;
        user-select: none;
    }

    .quick-action-btn:active {
        transform: translateY(1px);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    }

    .balance-amount {
        font-size: 1.75rem;
    }

    .greeting-text {
        font-size: 1.25rem;
    }

    /* Mobile specific adjustments for admin */
    .admin-more-content {
        margin: 0 16px;
        border-radius: 20px;
    }

    .admin-more-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }
}

/* Extra Small Mobile Devices */
@media (max-width: 480px) {
    /* Ensure widget ordering is maintained on very small screens */
    .announcement-widget {
        order: 1 !important;
        margin-bottom: 12px;
    }

    .quick-actions-widget {
        order: 2 !important;
        margin-bottom: 12px;
    }

    /* Quick actions extra small mobile optimization */
    .quick-actions {
        gap: 8px;
        margin: 12px 0;
    }

    .quick-action-btn {
        padding: 12px;
        min-height: 90px;
    }

    .quick-action-icon {
        width: 36px;
        height: 36px;
        font-size: 1.1rem;
    }

    .quick-action-label {
        font-size: 0.75rem;
        margin-top: 6px;
    }
    /* Dashboard Stats - Maintain 2 columns but smaller */
    .admin-dashboard-stats .dashboard-stat-card {
        min-height: 130px !important;
    }

    .admin-dashboard-stats .dashboard-stat-card > div:first-child {
        padding: 16px !important;
    }

    .admin-dashboard-stats .stat-label {
        font-size: 0.7rem !important;
    }

    .admin-dashboard-stats .stat-value {
        font-size: 1.5rem !important;
    }

    .admin-dashboard-stats .stat-icon {
        font-size: 1.75rem !important;
    }

    .admin-dashboard-stats .col-xs-6 {
        padding-left: 6px;
        padding-right: 6px;
    }

    /* Quick Actions - Maintain 2 columns */
    .admin-quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .admin-quick-action-item {
        min-height: 90px;
        padding: 12px;
    }

    .admin-quick-action-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
        margin-bottom: 8px;
    }

    .admin-quick-action-label {
        font-size: 0.75rem;
        line-height: 1.2;
    }

    /* System Status Cards */
    .admin-system-status-section .dashboard-system-card,
    .admin-system-status-section .dashboard-activity-card {
        padding: 16px !important;
    }

    .system-status-item {
        padding: 8px !important;
    }

    .system-status-item div:first-child {
        font-size: 1.75rem !important;
    }

    .system-status-item div:last-child {
        font-size: 0.75rem !important;
    }

    .activity-list {
        max-height: 120px !important;
    }
}

/* ===== UNIVERSAL MOBILE DASHBOARD GRID SYSTEM ===== */

/* Apply mobile grid layout to all dashboard widgets */
@media (max-width: 768px) {
    /* General Dashboard Widget Grid Rules - Only for smaller columns */
    .row .col-md-3,
    .row .col-md-4 {
        /* Convert desktop multi-column layouts to 2-column mobile grid */
        width: 50% !important;
        float: left !important;
        padding-left: 8px !important;
        padding-right: 8px !important;
        margin-bottom: 16px !important;
    }

    /* Keep col-md-6 as single column on mobile for better readability */
    .row .col-md-6 {
        width: 100% !important;
        float: none !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        margin-bottom: 16px !important;
    }

    /* Full width widgets on mobile */
    .row .col-md-12 {
        width: 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        margin-bottom: 16px !important;
    }

    /* Dashboard widget cards mobile optimization */
    .modern-card {
        margin-bottom: 16px !important;
        border-radius: 16px !important;
    }

    /* Mobile widget positioning - Quick Actions after Announcement */
    .announcement-widget {
        order: -2 !important; /* Ensure announcement comes first */
        margin-bottom: 16px !important;
    }

    .quick-actions-widget {
        order: -1 !important; /* Quick actions comes after announcement */
        margin-top: 0 !important;
        margin-bottom: 16px !important;
    }

    /* For better mobile layout, ensure widgets stack properly */
    .col-md-12 {
        margin-bottom: 16px;
    }
}

/* Desktop layout - ensure 2-column layout is preserved */
@media (min-width: 769px) {
    .row .col-md-6 {
        width: 50% !important;
        float: left !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    /* Ensure proper clearfix for 2-column layout */
    .row::after {
        content: "";
        display: table;
        clear: both;
    }

    /* Reset widget ordering on desktop */
    .announcement-widget,
    .quick-actions-widget {
        order: unset !important;
    }

    /* ===== MOBILE TABLE RESPONSIVE STYLES ===== */

    /* Enhanced table responsive container */
    .table-responsive {
        border: none !important;
        margin-bottom: 0 !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        -webkit-overflow-scrolling: touch !important;
        -ms-overflow-style: -ms-autohiding-scrollbar !important;
        position: relative !important;
        /* Improved touch scrolling */
        scroll-behavior: smooth !important;
        scrollbar-width: thin !important;
        scrollbar-color: rgba(0, 191, 165, 0.3) transparent !important;
    }

    /* Custom scrollbar for webkit browsers */
    .table-responsive::-webkit-scrollbar {
        height: 8px !important;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05) !important;
        border-radius: 4px !important;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: rgba(0, 191, 165, 0.3) !important;
        border-radius: 4px !important;
        transition: background 0.3s ease !important;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 191, 165, 0.5) !important;
    }

    /* Touch-friendly cursor for mobile table scrolling */
    .table-responsive {
        cursor: grab !important;
    }

    .table-responsive:active {
        cursor: grabbing !important;
    }

    /* Mobile table wrapper with improved scroll indicators */
    .table-responsive::before,
    .table-responsive::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 15px;
        z-index: 1;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    /* Left scroll indicator - shows when content is scrolled */
    .table-responsive::before {
        left: 0;
        background: linear-gradient(to right, rgba(248,249,250,0.9), transparent);
        opacity: 0;
    }

    /* Right scroll indicator - shows when more content available */
    .table-responsive::after {
        right: 0;
        background: linear-gradient(to left, rgba(248,249,250,0.9), transparent);
        opacity: 0;
    }

    /* Show indicators based on scroll position */
    .table-responsive.has-scroll::after {
        opacity: 1;
    }

    .table-responsive.scrolled-left::before {
        opacity: 1;
    }

    .table-responsive.scrolled-right::after {
        opacity: 0;
    }

    /* Mobile table styles - optimized for horizontal scrolling */
    .table-responsive table {
        min-width: 800px !important; /* Minimum width to trigger horizontal scroll */
        margin-bottom: 0 !important;
        border-collapse: separate !important;
        border-spacing: 0 !important;
        white-space: nowrap !important;
        /* Ensure table scrolls naturally */
        position: relative !important;
        width: auto !important;
    }

    /* Table header mobile optimization - sticky top only for vertical scrolling */
    .table-responsive thead th {
        position: sticky !important;
        top: 0 !important;
        background: #f8f9fa !important;
        z-index: 10 !important;
        border-bottom: 2px solid #dee2e6 !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        padding: 8px 6px !important;
        text-align: center !important;
        vertical-align: middle !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        /* Ensure headers scroll horizontally with table content */
        left: auto !important;
        right: auto !important;
    }

    /* REMOVED: First column sticky behavior for natural horizontal scrolling */
    /* All columns now scroll together naturally */
    .table-responsive th:first-child,
    .table-responsive td:first-child {
        /* Remove sticky positioning for natural scrolling */
        position: static !important;
        background: inherit !important;
        border-right: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }

    /* Header first column - no special positioning */
    .table-responsive thead th:first-child {
        background: #f8f9fa !important;
        position: static !important;
    }

    /* Table body mobile optimization */
    .table-responsive tbody td {
        font-size: 11px !important;
        padding: 6px 4px !important;
        vertical-align: middle !important;
        border-bottom: 1px solid #dee2e6 !important;
        min-width: 80px !important; /* Minimum width for readability */
        max-width: 200px !important; /* Increased max width */
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        /* Ensure cells don't interfere with horizontal scrolling */
        position: static !important;
        white-space: nowrap !important;
    }

    /* Action buttons mobile optimization */
    .table-responsive .btn {
        font-size: 10px !important;
        padding: 2px 6px !important;
        margin: 1px !important;
        border-radius: 3px !important;
    }

    /* Checkbox and form elements mobile */
    .table-responsive input[type="checkbox"] {
        transform: scale(0.8) !important;
        margin: 0 !important;
    }

    /* Status badges mobile */
    .table-responsive .badge,
    .table-responsive .label {
        font-size: 9px !important;
        padding: 2px 4px !important;
        border-radius: 3px !important;
    }

    /* Mobile table row hover effect */
    .table-responsive tbody tr:hover {
        background-color: #f8f9fa !important;
        transform: scale(1.01) !important;
        transition: all 0.2s ease !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    }

    /* Specific table optimizations */

    /* Customer table mobile */
    #customerTable {
        min-width: 1200px !important;
    }

    #customerTable th:nth-child(1),
    #customerTable td:nth-child(1) {
        width: 40px !important;
        min-width: 40px !important;
    }

    #customerTable th:nth-child(2),
    #customerTable td:nth-child(2) {
        width: 100px !important;
        min-width: 100px !important;
    }

    /* Voucher table mobile */
    #datatable {
        min-width: 1000px !important;
    }

    /* Router table mobile */
    .table-responsive table.table-condensed {
        min-width: 900px !important;
    }

    /* Reports table mobile */
    .table-responsive table.table-striped {
        min-width: 800px !important;
    }

    /* Mobile scroll hint - only show when table has horizontal scroll */
    .table-responsive.has-scroll:not(.interacted)::after {
        content: '← Swipe to scroll →' !important;
        position: absolute !important;
        bottom: -25px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        font-size: 11px !important;
        color: #00BFA5 !important;
        background: rgba(255,255,255,0.9) !important;
        padding: 4px 8px !important;
        border-radius: 12px !important;
        border: 1px solid rgba(0, 191, 165, 0.2) !important;
        width: auto !important;
        height: auto !important;
        opacity: 0.8 !important;
        animation: fadeInOut 3s infinite !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    }

    @keyframes fadeInOut {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.8; }
    }

    /* Hide scroll hint after first interaction */
    .table-responsive.interacted::after {
        display: none !important;
    }

    /* Mobile table loading state */
    .table-responsive.loading {
        opacity: 0.6 !important;
        pointer-events: none !important;
    }

    .table-responsive.loading::before {
        content: 'Loading...' !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: rgba(255,255,255,0.9) !important;
        padding: 10px 20px !important;
        border-radius: 5px !important;
        font-size: 14px !important;
        z-index: 100 !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2) !important;
    }

    /* DataTable mobile optimizations */
    .dataTables_wrapper {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        margin-bottom: 10px !important;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        font-size: 12px !important;
        padding: 4px 8px !important;
    }

    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        font-size: 11px !important;
        margin-top: 10px !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 4px 8px !important;
        margin: 0 2px !important;
        font-size: 11px !important;
    }

    /* Mobile table search and filter optimization */
    .table-search-mobile {
        position: sticky !important;
        top: 0 !important;
        background: #fff !important;
        z-index: 20 !important;
        padding: 10px !important;
        border-bottom: 1px solid #dee2e6 !important;
        margin-bottom: 0 !important;
    }

    .table-search-mobile input {
        width: 100% !important;
        font-size: 14px !important;
        padding: 8px 12px !important;
        border: 1px solid #ced4da !important;
        border-radius: 5px !important;
    }

    /* Mobile table pagination */
    .pagination-mobile {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        padding: 10px !important;
        background: #f8f9fa !important;
        border-top: 1px solid #dee2e6 !important;
    }

    .pagination-mobile .btn {
        margin: 0 5px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
    }

    /* Mobile table export buttons */
    .table-export-mobile {
        position: sticky !important;
        top: 60px !important;
        background: #fff !important;
        z-index: 19 !important;
        padding: 5px 10px !important;
        border-bottom: 1px solid #dee2e6 !important;
        text-align: center !important;
    }

    .table-export-mobile .btn {
        font-size: 10px !important;
        padding: 4px 8px !important;
        margin: 2px !important;
    }

    /* Mobile table bulk actions */
    .table-bulk-actions-mobile {
        position: fixed !important;
        bottom: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        background: #007bff !important;
        color: white !important;
        padding: 10px 20px !important;
        border-radius: 25px !important;
        box-shadow: 0 4px 15px rgba(0,123,255,0.3) !important;
        z-index: 1000 !important;
        display: none !important;
        font-size: 12px !important;
    }

    .table-bulk-actions-mobile.show {
        display: block !important;
        animation: slideUp 0.3s ease !important;
    }

    @keyframes slideUp {
        from {
            transform: translateX(-50%) translateY(100px);
            opacity: 0;
        }
        to {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
    }

    /* Mobile table row selection indicator */
    .table-responsive tbody tr.selected {
        background-color: #e3f2fd !important;
        border-left: 4px solid #2196f3 !important;
    }

    .table-responsive tbody tr.selected td {
        background-color: #e3f2fd !important;
    }

    /* Mobile table column sorting */
    .table-responsive th.sortable {
        cursor: pointer !important;
        user-select: none !important;
        position: relative !important;
    }

    .table-responsive th.sortable:hover {
        background-color: #e9ecef !important;
    }

    .table-responsive th.sortable::after {
        content: '↕' !important;
        position: absolute !important;
        right: 4px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        font-size: 10px !important;
        opacity: 0.5 !important;
    }

    .table-responsive th.sortable.asc::after {
        content: '↑' !important;
        opacity: 1 !important;
        color: #007bff !important;
    }

    .table-responsive th.sortable.desc::after {
        content: '↓' !important;
        opacity: 1 !important;
        color: #007bff !important;
    }

    /* Ensure proper spacing for dashboard rows */
    .row {
        margin-left: -8px !important;
        margin-right: -8px !important;
    }

    /* Dashboard content padding adjustments */
    .content {
        padding: 20px 15px !important;
    }

    /* Widget specific mobile adjustments */
    .panel,
    .box {
        margin-bottom: 16px !important;
        border-radius: 16px !important;
    }

    .panel-body,
    .box-body {
        padding: 20px !important;
    }

    /* Chart and graph containers */
    .chart-container,
    canvas {
        max-height: 250px !important;
    }

    /* Table responsive improvements */
    .table-responsive {
        border: none !important;
        margin-bottom: 0 !important;
    }

    .table {
        font-size: 0.875rem !important;
    }

    .table th,
    .table td {
        padding: 8px 6px !important;
        white-space: nowrap;
    }
}

/* Extra small mobile devices - maintain 2-column layout */
@media (max-width: 480px) {
    /* Maintain 2-column grid but with tighter spacing */
    .row .col-md-3,
    .row .col-md-4,
    .row .col-md-6 {
        padding-left: 6px !important;
        padding-right: 6px !important;
    }

    .row {
        margin-left: -6px !important;
        margin-right: -6px !important;
    }

    .content {
        padding: 15px 10px !important;
    }

    .modern-card {
        border-radius: 12px !important;
    }

    .panel-body,
    .box-body {
        padding: 16px !important;
    }

    /* Smaller text for very small screens */
    .table {
        font-size: 0.75rem !important;
    }

    .table th,
    .table td {
        padding: 6px 4px !important;
    }
}

/* Large mobile devices and small tablets */
@media (min-width: 481px) and (max-width: 768px) {
    /* Slightly larger spacing for larger mobile screens */
    .row .col-md-3,
    .row .col-md-4,
    .row .col-md-6 {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    .row {
        margin-left: -10px !important;
        margin-right: -10px !important;
    }
}

/* ===== DASHBOARD SPECIFIC MOBILE IMPROVEMENTS ===== */

/* Dashboard row spacing */
.dashboard-row {
    margin-bottom: 16px;
}

.dashboard-row:last-child {
    margin-bottom: 0;
}

/* Mobile dashboard header improvements */
@media (max-width: 768px) {
    .content-header {
        padding: 15px !important;
        margin-bottom: 20px !important;
    }

    .content-header h1 {
        font-size: 1.5rem !important;
        margin: 0 !important;
    }

    /* Dashboard widget animations on mobile */
    .modern-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    }

    .modern-card:hover {
        transform: translateY(-2px) !important;
    }

    /* Improve touch targets on mobile */
    .modern-card,
    .panel,
    .box {
        min-height: 120px;
        cursor: pointer;
    }

    /* Better mobile typography */
    .modern-card h3,
    .panel-title,
    .box-title {
        font-size: 1.125rem !important;
        line-height: 1.3 !important;
    }

    /* Mobile dashboard stats improvements */
    .info-box,
    .small-box {
        border-radius: 16px !important;
        margin-bottom: 16px !important;
    }

    .info-box-content,
    .small-box .inner {
        padding: 20px !important;
    }

    .info-box-number,
    .small-box h3 {
        font-size: 1.75rem !important;
    }

    .info-box-text,
    .small-box p {
        font-size: 0.875rem !important;
    }

    /* Mobile chart containers */
    .chart-responsive {
        height: 200px !important;
        max-height: 200px !important;
    }

    /* Mobile table improvements */
    .table-responsive {
        border-radius: 12px !important;
        overflow: hidden !important;
    }

    /* Mobile button improvements */
    .btn {
        padding: 10px 16px !important;
        font-size: 0.875rem !important;
        border-radius: 8px !important;
    }

    .btn-group .btn {
        padding: 8px 12px !important;
        font-size: 0.75rem !important;
    }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .dashboard-row {
        margin-bottom: 12px;
    }

    .modern-card {
        min-height: 100px;
    }

    .content {
        padding: 15px 10px !important;
    }
}

/* ===== DASHBOARD CARD FOOTER STYLES ===== */

/* Dashboard card footer base styles */
.dashboard-card-footer {
    display: block !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: none !important;
    outline: none !important;
}

.dashboard-card-footer:hover,
.dashboard-card-footer:focus {
    text-decoration: none !important;
    outline: none !important;
}

.dashboard-card-footer:active {
    transform: scale(0.98) !important;
}

/* Online card footer styles */
.online-card-footer {
    display: block !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: none !important;
    outline: none !important;
}

.online-card-footer:hover,
.online-card-footer:focus {
    text-decoration: none !important;
    outline: none !important;
}

.online-card-footer:active {
    transform: scale(0.98) !important;
}

/* Mobile responsive footer adjustments */
@media (max-width: 768px) {
    /* Dashboard card footers on mobile */
    .dashboard-card-footer {
        padding: 10px 20px !important;
        font-size: 0.8rem !important;
    }

    .dashboard-card-footer span {
        font-size: 0.8rem !important;
    }

    .dashboard-card-footer i {
        font-size: 0.75rem !important;
    }

    /* Online card footers on mobile */
    .online-card-footer {
        padding: 12px 20px !important;
    }

    .online-card-footer span {
        font-size: 0.8rem !important;
    }

    .online-card-footer i {
        font-size: 0.75rem !important;
    }

    /* Adjust card structure for footers on mobile */
    .admin-dashboard-stats .dashboard-stat-card {
        min-height: 140px !important;
    }

    .online-users-stats .online-users-card {
        min-height: 160px !important;
    }

    /* Footer hover effects on mobile */
    .dashboard-card-footer:hover,
    .online-card-footer:hover {
        transform: none !important;
    }

    .dashboard-card-footer:active,
    .online-card-footer:active {
        transform: scale(0.95) !important;
    }
}

/* Extra small mobile adjustments */
@media (max-width: 480px) {
    .dashboard-card-footer {
        padding: 8px 16px !important;
    }

    .dashboard-card-footer span {
        font-size: 0.75rem !important;
    }

    .dashboard-card-footer i {
        font-size: 0.7rem !important;
    }

    .online-card-footer {
        padding: 10px 16px !important;
    }

    .online-card-footer span {
        font-size: 0.75rem !important;
    }

    .online-card-footer i {
        font-size: 0.7rem !important;
    }

    /* Reduce card heights on very small screens */
    .admin-dashboard-stats .dashboard-stat-card {
        min-height: 130px !important;
    }

    .online-users-stats .online-users-card {
        min-height: 150px !important;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .dashboard-card-footer,
    .online-card-footer {
        /* Remove hover effects on touch devices */
        transition: background-color 0.2s ease !important;
    }

    .dashboard-card-footer:hover,
    .online-card-footer:hover {
        transform: none !important;
    }

    .dashboard-card-footer:active,
    .online-card-footer:active {
        transform: scale(0.95) !important;
        transition: transform 0.1s ease !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .dashboard-card-footer {
        border: 2px solid currentColor !important;
    }

    .online-card-footer {
        border: 2px solid currentColor !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .dashboard-card-footer,
    .online-card-footer {
        transition: none !important;
    }

    .dashboard-card-footer:hover,
    .online-card-footer:hover {
        transform: none !important;
    }

    .dashboard-card-footer:active,
    .online-card-footer:active {
        transform: none !important;
    }
}

/* ===== MODERN PROFILE PAGE STYLES ===== */

/* Profile card responsive design */
.profile-card {
    transition: all 0.3s ease;
}

.profile-card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12) !important;
}

/* Modern form styles */
.modern-form-group {
    margin-bottom: 24px;
}

.modern-form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.modern-form-input,
.modern-form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.modern-form-input:focus,
.modern-form-textarea:focus {
    outline: none;
    border-color: #00BFA5;
    box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
}

.modern-form-input[readonly] {
    background: #f9fafb;
    color: #6b7280;
}

/* File input styling */
.modern-file-input {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-input-display {
    background: #f8fafc;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-input-display:hover {
    border-color: #00BFA5;
    background: #f0fdfa;
}

/* Button styles */
.modern-btn-primary {
    background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 191, 165, 0.3);
}

.modern-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 191, 165, 0.4);
}

.modern-btn-secondary {
    background: #f8fafc;
    color: #6b7280;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modern-btn-secondary:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    text-decoration: none;
}

/* Profile photo styling */
.profile-photo {
    transition: all 0.3s ease;
}

.profile-photo:hover {
    transform: scale(1.05);
}

/* Face detect option */
.face-detect-option {
    background: #f0fdfa;
    border: 1px solid #a7f3d0;
    border-radius: 12px;
    padding: 12px;
}

.modern-checkbox {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: #059669;
    cursor: pointer;
    margin: 0;
}

/* Input group styling */
.modern-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

/* Form actions */
.form-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

/* Mobile responsive styles for profile page */
@media (max-width: 768px) {
    .profile-card {
        margin: 16px;
        border-radius: 16px !important;
    }

    .profile-header {
        padding: 24px 20px 16px !important;
    }

    .profile-header h3 {
        font-size: 1.25rem !important;
    }

    .profile-form-container {
        padding: 24px 20px !important;
    }

    .profile-photo {
        width: 100px !important;
        height: 100px !important;
    }

    .modern-form-group {
        margin-bottom: 20px;
    }

    .modern-form-label {
        font-size: 0.8rem;
    }

    .modern-form-input,
    .modern-form-textarea {
        padding: 10px 14px;
        font-size: 0.8rem;
    }

    .file-input-display {
        padding: 12px;
    }

    .face-detect-option {
        margin-top: 12px;
        padding: 10px;
    }

    .modern-checkbox {
        font-size: 0.7rem;
    }

    .modern-input-group {
        flex-direction: column;
        gap: 12px;
    }

    .modern-input-group input {
        flex: none !important;
        width: 100% !important;
    }

    .modern-btn-secondary {
        width: 100% !important;
        text-align: center;
    }

    .form-actions {
        margin-top: 24px;
        padding-top: 20px;
    }

    .modern-btn-primary,
    .modern-btn-secondary {
        padding: 12px 20px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .profile-card {
        margin: 12px;
        border-radius: 12px !important;
    }

    .profile-header {
        padding: 20px 16px 12px !important;
    }

    .profile-header h3 {
        font-size: 1.125rem !important;
    }

    .profile-form-container {
        padding: 20px 16px !important;
    }

    .profile-photo {
        width: 80px !important;
        height: 80px !important;
    }

    .modern-form-group {
        margin-bottom: 16px;
    }

    .modern-form-input,
    .modern-form-textarea {
        padding: 8px 12px;
        font-size: 0.75rem;
    }

    .file-input-display {
        padding: 10px;
    }

    .file-input-display i {
        font-size: 1.25rem !important;
    }

    .file-input-display div {
        font-size: 0.75rem !important;
    }

    .face-detect-option {
        padding: 8px;
    }

    .modern-checkbox {
        font-size: 0.65rem;
    }

    .form-actions {
        margin-top: 20px;
        padding-top: 16px;
    }

    .modern-btn-primary,
    .modern-btn-secondary {
        padding: 10px 16px;
        font-size: 0.75rem;
    }
}

/* Custom fields styling */
.custom-fields-section .modern-form-group {
    margin-bottom: 20px;
}

.custom-fields-section .modern-form-label {
    color: #374151;
    font-weight: 600;
}

.custom-fields-section .modern-form-input {
    border-color: #e5e7eb;
}

.custom-fields-section .modern-form-input:focus {
    border-color: #00BFA5;
    box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
}

/* ===== ADMIN VIEW PAGE STYLES ===== */

/* Admin view card styling */
.admin-view-card {
    transition: all 0.3s ease;
}

.admin-view-card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12) !important;
}

/* Profile info sections */
.profile-info-section,
.agent-info-section {
    transition: all 0.3s ease;
}

.profile-info-section:hover,
.agent-info-section:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* Info item styling */
.info-item {
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(0, 191, 165, 0.02);
    border-radius: 8px;
    margin: 0 -8px;
    padding: 12px 8px !important;
}

/* Info label and value styling */
.info-label {
    min-width: 120px;
}

.info-value a {
    transition: all 0.3s ease;
}

.info-value a:hover {
    text-decoration: underline !important;
}

/* Photo display styling */
.admin-view-photo {
    transition: all 0.3s ease;
}

.admin-view-photo:hover {
    transform: scale(1.05);
}

/* Status badge */
.photo-status-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Mobile responsive styles for admin view */
@media (max-width: 768px) {
    .admin-view-card {
        margin: 16px;
        border-radius: 16px !important;
    }

    .admin-view-header {
        padding: 24px 20px 16px !important;
    }

    .admin-view-header h3 {
        font-size: 1.25rem !important;
    }

    .admin-view-content {
        padding: 24px 20px !important;
    }

    .profile-info-section,
    .agent-info-section {
        padding: 20px !important;
        margin-bottom: 20px !important;
    }

    .admin-view-photo {
        width: 100px !important;
        height: 100px !important;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 8px;
    }

    .info-label {
        min-width: auto;
        width: 100%;
    }

    .info-value {
        width: 100%;
        text-align: left;
    }

    .profile-actions {
        margin-top: 24px;
        padding-top: 20px;
    }

    .modern-btn-primary {
        padding: 12px 20px !important;
        font-size: 0.8rem !important;
    }
}

@media (max-width: 480px) {
    .admin-view-card {
        margin: 12px;
        border-radius: 12px !important;
    }

    .admin-view-header {
        padding: 20px 16px 12px !important;
    }

    .admin-view-header h3 {
        font-size: 1.125rem !important;
    }

    .admin-view-content {
        padding: 20px 16px !important;
    }

    .profile-info-section,
    .agent-info-section {
        padding: 16px !important;
        margin-bottom: 16px !important;
    }

    .admin-view-photo {
        width: 80px !important;
        height: 80px !important;
    }

    .info-item {
        padding: 10px 0 !important;
    }

    .info-label,
    .info-value {
        font-size: 0.8rem !important;
    }

    .profile-actions {
        margin-top: 20px;
        padding-top: 16px;
    }

    .modern-btn-primary {
        padding: 10px 16px !important;
        font-size: 0.75rem !important;
    }
}

/* Agent info section specific styling */
.agent-info-section h4 {
    color: #059669;
}

.agent-info-list .info-item {
    border-bottom-color: #a7f3d0;
}

.agent-info-list .info-label {
    color: #059669;
}

.agent-info-list .info-value {
    color: #047857;
}

.agent-info-list .info-value a {
    color: #059669;
}

/* Responsive agent section */
@media (max-width: 768px) {
    .agent-info-section {
        background: #f0fdfa !important;
        border-color: #a7f3d0 !important;
    }

    .agent-info-section h4 {
        font-size: 1rem !important;
        margin-bottom: 16px !important;
    }
}

/* ========================================
   ORDER PLAN MOBILE OPTIMIZATION
   ======================================== */

/* Order Plan Container - Mobile Responsive Grid */
@media (max-width: 768px) {
    /* Order Plan Section Container */
    .order-plan-container,
    .box-body.row {
        display: flex !important;
        flex-wrap: wrap !important;
        margin: 0 -8px !important;
        gap: 0 !important;
    }

    /* Plan Card Grid Layout */
    .order-plan-container .col,
    .order-plan-container .col-md-4,
    .box-body.row .col,
    .box-body.row .col-md-4 {
        /* 2-column grid for mobile (480px-768px) */
        flex: 0 0 50% !important;
        max-width: 50% !important;
        width: 50% !important;
        padding: 0 8px !important;
        margin-bottom: 16px !important;
        box-sizing: border-box !important;
    }

    /* Plan Card Styling */
    .order-plan-container .box,
    .box-body.row .box {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        margin-bottom: 0 !important;
        border-radius: 16px !important;
        overflow: hidden !important;
        box-shadow: 0 4px 20px rgba(0, 191, 165, 0.15) !important;
        border: 1px solid rgba(0, 191, 165, 0.1) !important;
        transition: all 0.3s ease !important;
    }

    .order-plan-container .box:hover,
    .box-body.row .box:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 30px rgba(0, 191, 165, 0.25) !important;
    }

    /* Plan Card Header */
    .order-plan-container .box-header,
    .box-body.row .box-header {
        background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%) !important;
        color: white !important;
        padding: 16px 12px !important;
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        text-align: center !important;
        border-bottom: none !important;
        margin: 0 !important;
        min-height: 60px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Plan Details Table */
    .order-plan-container .table-responsive,
    .box-body.row .table-responsive {
        flex: 1 !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .order-plan-container .table,
    .box-body.row .table {
        margin: 0 !important;
        font-size: 0.8rem !important;
    }

    .order-plan-container .table td,
    .box-body.row .table td {
        padding: 8px 12px !important;
        border-color: rgba(0, 191, 165, 0.1) !important;
        vertical-align: middle !important;
    }

    .order-plan-container .table td:first-child,
    .box-body.row .table td:first-child {
        font-weight: 600 !important;
        color: #374151 !important;
        width: 40% !important;
    }

    .order-plan-container .table td:last-child,
    .box-body.row .table td:last-child {
        color: #00BFA5 !important;
        font-weight: 500 !important;
    }

    /* Plan Card Body (Buttons) */
    .order-plan-container .box-body,
    .box-body.row .box .box-body {
        padding: 16px 12px !important;
        margin-top: auto !important;
        background: #f8fafc !important;
    }

    /* Buy Buttons */
    .order-plan-container .btn,
    .box-body.row .btn {
        min-height: 44px !important;
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        border-radius: 12px !important;
        margin-bottom: 8px !important;
        transition: all 0.3s ease !important;
        border: none !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .order-plan-container .btn-warning,
    .box-body.row .btn-warning {
        background: linear-gradient(135deg, #FFA726 0%, #FF9800 100%) !important;
        color: white !important;
        box-shadow: 0 4px 15px rgba(255, 167, 38, 0.3) !important;
    }

    .order-plan-container .btn-warning:hover,
    .box-body.row .btn-warning:hover {
        background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 6px 20px rgba(255, 167, 38, 0.4) !important;
    }

    .order-plan-container .btn-primary,
    .box-body.row .btn-primary {
        background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%) !important;
        color: white !important;
        box-shadow: 0 4px 15px rgba(0, 191, 165, 0.3) !important;
    }

    .order-plan-container .btn-primary:hover,
    .box-body.row .btn-primary:hover {
        background: linear-gradient(135deg, #00ACC1 0%, #00BFA5 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 6px 20px rgba(0, 191, 165, 0.4) !important;
    }

    /* Price Styling */
    .order-plan-container .table td:contains('Price') + td,
    .box-body.row .table td:contains('Price') + td {
        font-size: 1rem !important;
        font-weight: 700 !important;
        color: #00BFA5 !important;
    }

    /* Old Price Strikethrough */
    .order-plan-container sup,
    .box-body.row sup {
        font-size: 0.7rem !important;
        margin-left: 4px !important;
    }

    /* Router Section Headers */
    .box.box-solid.box-primary {
        margin-bottom: 24px !important;
    }

    .box.box-solid.box-primary .box-header {
        background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%) !important;
        color: white !important;
        padding: 20px !important;
        font-size: 1.1rem !important;
        font-weight: 700 !important;
        text-align: center !important;
        border-radius: 16px 16px 0 0 !important;
    }

    /* Plan Type Headers within Router Sections */
    .box.box-solid.box-primary .box-header.text-white {
        background: linear-gradient(135deg, #37474F 0%, #455A64 100%) !important;
        padding: 16px 20px !important;
        font-size: 1rem !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }

    /* Breadcrumb Styling */
    .breadcrumb {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
        border: 1px solid rgba(0, 191, 165, 0.1) !important;
        border-radius: 12px !important;
        padding: 12px 16px !important;
        margin-bottom: 20px !important;
    }

    .breadcrumb li {
        color: #00BFA5 !important;
        font-weight: 600 !important;
    }
}

/* Single column for extra small devices (≤479px) */
@media (max-width: 479px) {
    .order-plan-container .col,
    .order-plan-container .col-md-4,
    .box-body.row .col,
    .box-body.row .col-md-4 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 8px !important;
    }

    /* Responsive Typography for Extra Small Devices */
    .order-plan-container .box-header,
    .box-body.row .box-header {
        font-size: 0.85rem !important;
        padding: 14px 10px !important;
        min-height: 56px !important;
    }

    .order-plan-container .table,
    .box-body.row .table {
        font-size: 0.75rem !important;
    }

    .order-plan-container .table td,
    .box-body.row .table td {
        padding: 6px 10px !important;
    }

    .order-plan-container .btn,
    .box-body.row .btn {
        font-size: 0.8rem !important;
        min-height: 42px !important;
    }

    .box.box-solid.box-primary .box-header {
        font-size: 1rem !important;
        padding: 16px !important;
    }

    .box.box-solid.box-primary .box-header.text-white {
        font-size: 0.9rem !important;
        padding: 14px 16px !important;
    }
}
