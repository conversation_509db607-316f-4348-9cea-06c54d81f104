<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>{Lang::T('Login')} - {$_c['CompanyName']}</title>
    <link rel="shortcut icon" href="{$app_url}/ui/ui/images/logo.png" type="image/x-icon" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>
    <script src="{$app_url}/ui/ui/scripts/jquery.min.js"></script>
    <script src="{$app_url}/ui/ui/scripts/bootstrap.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 50%, #26A69A 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        .login-container {
            position: relative;
            z-index: 2;
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 20px;
        }

        .login-content {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 420px;
            gap: 40px;
            align-items: center;
        }

        .announcement-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .announcement-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }

        .announcement-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }

        .announcement-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .announcement-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
        }

        .announcement-content {
            color: #475569;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00BFA5, #4DB6AC, #26A69A);
            border-radius: 24px 24px 0 0;
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00BFA5, #4DB6AC);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(0, 191, 165, 0.3);
        }

        .login-logo i {
            font-size: 2rem;
            color: white;
        }

        .login-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            letter-spacing: -0.025em;
        }

        .login-subtitle {
            color: #64748b;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input-group {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 500;
            color: #1e293b;
            background: #ffffff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #00BFA5;
            box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
            transform: translateY(-1px);
        }

        .form-input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }

        .form-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .form-input:focus + .form-icon {
            color: #00BFA5;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }

        .btn-register {
            padding: 14px 20px;
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 0.95rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            color: white;
            text-decoration: none;
        }

        .login-btn {
            padding: 14px 20px;
            background: linear-gradient(135deg, #00BFA5, #4DB6AC);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 191, 165, 0.4);
        }

        .login-links {
            text-align: center;
            margin-top: 24px;
        }

        .forgot-link {
            display: inline-block;
            color: #00BFA5;
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 16px;
            transition: color 0.3s ease;
        }

        .forgot-link:hover {
            color: #26A69A;
            text-decoration: none;
        }

        .policy-links {
            display: flex;
            justify-content: center;
            gap: 16px;
            font-size: 0.875rem;
        }

        .policy-links a {
            color: #64748b;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .policy-links a:hover {
            color: #00BFA5;
            text-decoration: none;
        }

        .policy-separator {
            color: #cbd5e1;
        }

        /* Enhanced Alert Styles */
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 24px;
            padding: 16px 20px;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.15);
            animation: slideInDown 0.3s ease-out;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border-left: 4px solid #ef4444;
            color: #dc2626;
        }

        .alert-success {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border-left: 4px solid #22c55e;
            color: #16a34a;
        }

        .alert-info {
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            border-left: 4px solid #3b82f6;
            color: #2563eb;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fffbeb, #fef3c7);
            border-left: 4px solid #f59e0b;
            color: #d97706;
        }

        .alert-content {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .alert-content i {
            font-size: 1.2rem;
            margin-right: 12px;
        }

        .alert-danger .alert-content i {
            color: #ef4444;
        }

        .alert-success .alert-content i {
            color: #22c55e;
        }

        .alert-info .alert-content i {
            color: #3b82f6;
        }

        .alert-warning .alert-content i {
            color: #f59e0b;
        }

        .alert-message {
            font-weight: 500;
            font-size: 0.95rem;
        }

        .alert-suggestion {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid rgba(239, 68, 68, 0.2);
        }

        .alert-suggestion small {
            color: #7f1d1d;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
        }

        .alert-suggestion i {
            margin-right: 6px;
            font-size: 0.9rem;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .login-content {
                grid-template-columns: 1fr;
                gap: 24px;
                max-width: 500px;
            }

            .announcement-section {
                order: 2;
            }

            .login-card {
                order: 1;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 10px;
            }

            .login-card,
            .announcement-section {
                padding: 24px;
                border-radius: 20px;
            }

            .login-title {
                font-size: 1.5rem;
            }

            .form-input {
                padding: 14px 18px 14px 45px;
            }

            .action-buttons {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-content">
            <div class="announcement-section">
                <div class="announcement-header">
                    <div class="announcement-icon">
                        <i class="fa fa-bullhorn"></i>
                    </div>
                    <h2 class="announcement-title">{Lang::T('Announcement')}</h2>
                </div>
                <div class="announcement-content">
                    {$Announcement = "{$PAGES_PATH}/Announcement.html"}
                    {if file_exists($Announcement)}
                        {include file=$Announcement}
                    {else}
                        <p>Welcome to {$_c['CompanyName']}! Please login to access your account and manage your services.</p>
                        <p>If you don't have an account yet, you can register by clicking the Register button.</p>
                    {/if}
                </div>
            </div>

            <div class="login-card">
                <div class="login-header">
                    <div class="login-logo">
                        <i class="fa fa-user"></i>
                    </div>
                    <h1 class="login-title">Member Portal</h1>
                    <p class="login-subtitle">{Lang::T('Log in to Member Panel')}</p>
                </div>

                {if isset($notify)}
                    <div class="alert alert-{if $notify_t == 's'}success{elseif $notify_t == 'i'}info{elseif $notify_t == 'w'}warning{else}danger{/if}" id="loginNotify">
                        <div class="alert-content">
                            <i class="fa {if $notify_t == 's'}fa-check-circle{elseif $notify_t == 'i'}fa-info-circle{elseif $notify_t == 'w'}fa-exclamation-triangle{else}fa-times-circle{/if}"></i>
                            <span class="alert-message">{$notify}</span>
                        </div>
                        {if $notify_t == 'e'}
                            <div class="alert-suggestion">
                                <small><i class="fa fa-info-circle"></i> Please check your credentials and try again. Contact support if the problem persists.</small>
                            </div>
                        {/if}
                    </div>
                {/if}

                <form action="{Text::url('login/post')}" method="post" id="customerLoginForm">
                    <input type="hidden" name="csrf_token" value="{$csrf_token}">

                    <div class="form-group">
                        <label class="form-label">
                            {if $_c['registration_username'] == 'phone'}
                                {Lang::T('Phone Number')}
                            {elseif $_c['registration_username'] == 'email'}
                                {Lang::T('Email')}
                            {else}
                                {Lang::T('Usernames')}
                            {/if}
                        </label>
                        <div class="form-input-group">
                            <input type="text"
                                   required
                                   class="form-input"
                                   name="username"
                                   placeholder="{if $_c['country_code_phone']!= '' || $_c['registration_username'] == 'phone'}{$_c['country_code_phone']} {Lang::T('Phone Number')}{elseif $_c['registration_username'] == 'email'}{Lang::T('Email')}{else}{Lang::T('Usernames')}{/if}"
                                   autocomplete="username">
                            <i class="fa {if $_c['registration_username'] == 'phone'}fa-phone{elseif $_c['registration_username'] == 'email'}fa-envelope{else}fa-user{/if} form-icon"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">{Lang::T('Password')}</label>
                        <div class="form-input-group">
                            <input type="password"
                                   required
                                   class="form-input"
                                   name="password"
                                   placeholder="{Lang::T('Password')}"
                                   autocomplete="current-password">
                            <i class="fa fa-lock form-icon"></i>
                        </div>
                    </div>

                    <div class="action-buttons">
                        {if $_c['disable_registration'] != 'noreg'}
                            <a href="{Text::url('register')}" class="btn-register">
                                <i class="fa fa-user-plus" style="margin-right: 8px;"></i>
                                {Lang::T('Register')}
                            </a>
                        {/if}
                        <button type="submit" class="login-btn" id="loginButton">
                            <span id="loginText">{Lang::T('Login')}</span>
                        </button>
                    </div>

                    <div class="login-links">
                        <a href="{Text::url('forgot')}" class="forgot-link">
                            <i class="fa fa-key" style="margin-right: 6px;"></i>
                            {Lang::T('Forgot Password')}
                        </a>
                        <div class="policy-links">
                            <a href="javascript:showPrivacy()">Privacy Policy</a>
                            <span class="policy-separator">•</span>
                            <a href="javascript:showTaC()">Terms & Conditions</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modern Modal -->
    <div class="modal fade" id="HTMLModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="border-radius: 16px; border: none; overflow: hidden;">
                <div class="modal-header" style="background: linear-gradient(135deg, #00BFA5, #4DB6AC); color: white; border: none;">
                    <h4 class="modal-title" style="font-weight: 600;">Information</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="HTMLModal_konten" style="padding: 24px; line-height: 1.6;"></div>
                <div class="modal-footer" style="border: none; padding: 16px 24px;">
                    <button type="button" class="btn btn-default" data-dismiss="modal" style="border-radius: 8px; padding: 8px 20px;">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('customerLoginForm');
            const loginButton = document.getElementById('loginButton');
            const loginText = document.getElementById('loginText');

            // Form submission handling
            form.addEventListener('submit', function(e) {
                loginButton.classList.add('loading');
                loginText.style.opacity = '0';
                loginButton.disabled = true;

                // Add loading animation
                loginButton.innerHTML = '<div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>';
            });

            // Handle notifications
            const loginNotify = document.getElementById('loginNotify');
            if (loginNotify) {
                // Auto-dismiss notification after 8 seconds
                setTimeout(() => {
                    loginNotify.style.opacity = '0';
                    loginNotify.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        loginNotify.style.display = 'none';
                    }, 300);
                }, 8000);

                // Focus username field if there's an error
                const notifyType = '{if isset($notify_t)}{$notify_t}{/if}';
                if (notifyType === 'e') {
                    const usernameInput = form.querySelector('input[name="username"]');
                    if (usernameInput) {
                        usernameInput.focus();
                    }
                } else {
                    // Auto-focus first input for other cases
                    const firstInput = form.querySelector('input[type="text"]');
                    if (firstInput) {
                        firstInput.focus();
                    }
                }
            } else {
                // Auto-focus first input if no notification
                const firstInput = form.querySelector('input[type="text"]');
                if (firstInput) {
                    firstInput.focus();
                }
            }

            // Enhanced form validation
            const inputs = form.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateInput(this);
                });

                input.addEventListener('input', function() {
                    if (this.style.borderColor === 'rgb(239, 68, 68)') {
                        this.style.borderColor = '#e2e8f0';
                        hideError(this);
                    }
                });

                // Enter key navigation
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const nextInput = getNextInput(this);
                        if (nextInput) {
                            nextInput.focus();
                        } else {
                            form.submit();
                        }
                    }
                });
            });

            function validateInput(input) {
                const value = input.value.trim();
                let isValid = true;
                let errorMessage = '';

                if (value === '') {
                    isValid = false;
                    errorMessage = 'This field is required';
                } else if (input.type === 'password' && value.length < 3) {
                    isValid = false;
                    errorMessage = 'Password must be at least 3 characters';
                } else if (input.name === 'username') {
                    // Basic username validation
                    if (value.length < 3) {
                        isValid = false;
                        errorMessage = 'Username must be at least 3 characters';
                    }
                }

                if (!isValid) {
                    showError(input, errorMessage);
                } else {
                    hideError(input);
                }

                return isValid;
            }

            function showError(input, message) {
                input.style.borderColor = '#ef4444';

                // Remove existing error
                hideError(input);

                // Add error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.style.cssText = 'color: #ef4444; font-size: 0.75rem; margin-top: 4px; font-weight: 500;';
                errorDiv.textContent = message;

                input.parentNode.parentNode.appendChild(errorDiv);
            }

            function hideError(input) {
                const errorMessage = input.parentNode.parentNode.querySelector('.error-message');
                if (errorMessage) {
                    errorMessage.remove();
                }
            }

            function getNextInput(currentInput) {
                const inputs = Array.from(form.querySelectorAll('.form-input'));
                const currentIndex = inputs.indexOf(currentInput);
                return inputs[currentIndex + 1] || null;
            }

            // Privacy and Terms functions
            window.showPrivacy = function() {
                fetch('{$app_url}/pages/Privacy_Policy.html')
                    .then(response => response.text())
                    .then(data => {
                        document.getElementById('HTMLModal_konten').innerHTML = data || '<p>Privacy Policy content not available.</p>';
                        $('#HTMLModal').modal('show');
                    })
                    .catch(error => {
                        document.getElementById('HTMLModal_konten').innerHTML = '<p>Privacy Policy content not available.</p>';
                        $('#HTMLModal').modal('show');
                    });
            };

            window.showTaC = function() {
                fetch('{$app_url}/pages/Terms_and_Conditions.html')
                    .then(response => response.text())
                    .then(data => {
                        document.getElementById('HTMLModal_konten').innerHTML = data || '<p>Terms and Conditions content not available.</p>';
                        $('#HTMLModal').modal('show');
                    })
                    .catch(error => {
                        document.getElementById('HTMLModal_konten').innerHTML = '<p>Terms and Conditions content not available.</p>';
                        $('#HTMLModal').modal('show');
                    });
            };

            // Add CSS animation for loading spinner
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                .loading {
                    pointer-events: none;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>

</html>