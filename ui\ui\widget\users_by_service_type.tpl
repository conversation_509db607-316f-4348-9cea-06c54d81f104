<!-- Users by Service Type Widget -->
<div class="modern-card" style="padding: 24px;">
    <h3 style="font-size: 1.125rem; font-weight: 600; color: #1e293b; margin-bottom: 20px;">
        <i class="fa fa-users" style="margin-right: 8px; color: #2196F3;"></i>
        {$widget_title}
    </h3>
    
    {if $service_stats && count($service_stats) > 0}
        <!-- Service Type Table -->
        <div style="overflow-x: auto; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse; background: white;">
                <thead>
                    <tr style="background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);">
                        <th style="
                            padding: 16px 20px;
                            text-align: left;
                            font-size: 0.875rem;
                            font-weight: 700;
                            color: white;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                            border: none;
                            position: relative;
                        ">
                            SERVICE TYPE
                        </th>
                        <th style="
                            padding: 16px 20px;
                            text-align: center;
                            font-size: 0.875rem;
                            font-weight: 700;
                            color: white;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                            border: none;
                            position: relative;
                        ">
                            USERS
                        </th>
                        <th style="
                            padding: 16px 20px;
                            text-align: center;
                            font-size: 0.875rem;
                            font-weight: 700;
                            color: white;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                            border: none;
                            position: relative;
                        ">
                            PERCENTAGE
                        </th>
                    </tr>
                </thead>
                <tbody style="background: white;">
                    {foreach $service_stats as $stat}
                        <tr style="
                            border-bottom: 1px solid #e2e8f0;
                            transition: background-color 0.2s ease;
                        " onmouseover="this.style.backgroundColor='#f8fafc'" onmouseout="this.style.backgroundColor='white'">
                            <td style="
                                padding: 20px;
                                vertical-align: middle;
                                border: none;
                            ">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div style="
                                        width: 44px;
                                        height: 44px;
                                        border-radius: 50%;
                                        background: {$stat.color};
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        color: white;
                                        font-size: 1.125rem;
                                        box-shadow: 0 2px 8px rgba({if $stat.color == '#2196F3'}33, 150, 243{elseif $stat.color == '#9C27B0'}156, 39, 176{elseif $stat.color == '#FF9800'}255, 152, 0{else}96, 125, 139{/if}, 0.3);
                                    ">
                                        <i class="fa {$stat.icon}"></i>
                                    </div>
                                    <div>
                                        <div style="
                                            font-size: 1rem;
                                            font-weight: 600;
                                            color: #1e293b;
                                            margin-bottom: 2px;
                                        ">
                                            {$stat.type}
                                        </div>
                                        <div style="
                                            font-size: 0.75rem;
                                            color: #64748b;
                                            font-weight: 500;
                                        ">
                                            Service Type
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td style="
                                padding: 20px;
                                text-align: center;
                                vertical-align: middle;
                                border: none;
                            ">
                                <div style="
                                    font-size: 1.5rem;
                                    font-weight: 700;
                                    color: #1e293b;
                                    margin-bottom: 2px;
                                ">
                                    {$stat.count}
                                </div>
                                <div style="
                                    font-size: 0.75rem;
                                    color: #64748b;
                                    font-weight: 500;
                                ">
                                    Active Users
                                </div>
                            </td>
                            <td style="
                                padding: 20px;
                                text-align: center;
                                vertical-align: middle;
                                border: none;
                            ">
                                <div style="display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                    <div style="
                                        font-size: 1.25rem;
                                        font-weight: 700;
                                        color: #1e293b;
                                    ">
                                        {$stat.percentage}%
                                    </div>
                                    <div style="
                                        width: 80px;
                                        height: 6px;
                                        background: #e2e8f0;
                                        border-radius: 3px;
                                        overflow: hidden;
                                        position: relative;
                                    ">
                                        <div style="
                                            width: {$stat.percentage}%;
                                            height: 100%;
                                            background: {$stat.color};
                                            border-radius: 3px;
                                            transition: width 0.3s ease;
                                        "></div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>
        
        <!-- Total Summary -->
        <div style="
            margin-top: 20px;
            padding: 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        ">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="font-size: 0.875rem; color: #64748b; font-weight: 500;">
                    Total Active Users
                </div>
                <div style="font-size: 1.5rem; font-weight: 700; color: #1e293b;">
                    {$total_users}
                </div>
            </div>
        </div>
        
    {else}
        <!-- No Data State -->
        <div style="text-align: center; padding: 40px 20px; color: #64748b;">
            <div style="font-size: 3rem; margin-bottom: 16px; opacity: 0.3;">
                <i class="fa fa-users"></i>
            </div>
            <div style="font-size: 1rem; font-weight: 500;">
                No user data available
            </div>
            <div style="font-size: 0.875rem; margin-top: 8px;">
                Users will appear here once they are registered
            </div>
        </div>
    {/if}
</div>

<!-- Mobile Responsive Styles -->
<style>
/* Ensure table headers are always visible */
.modern-card table thead th {
    background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%) !important;
    color: white !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    position: relative !important;
    z-index: 10 !important;
}

/* Tablet styles */
@media (max-width: 768px) {
    .modern-card table {
        font-size: 0.875rem;
    }

    .modern-card th {
        padding: 14px 12px !important;
        font-size: 0.8rem !important;
        font-weight: 700 !important;
        color: white !important;
    }

    .modern-card td {
        padding: 16px 12px !important;
    }

    .modern-card div[style*="width: 44px"] {
        width: 36px !important;
        height: 36px !important;
        font-size: 1rem !important;
    }

    .modern-card div[style*="font-size: 1.5rem"] {
        font-size: 1.25rem !important;
    }

    .modern-card div[style*="width: 80px"] {
        width: 60px !important;
    }
}

/* Mobile styles */
@media (max-width: 480px) {
    .modern-card table {
        font-size: 0.75rem;
    }

    .modern-card th {
        padding: 12px 8px !important;
        font-size: 0.7rem !important;
        font-weight: 700 !important;
        color: white !important;
        background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%) !important;
    }

    .modern-card td {
        padding: 12px 8px !important;
    }

    .modern-card div[style*="width: 44px"] {
        width: 32px !important;
        height: 32px !important;
        font-size: 0.875rem !important;
    }

    .modern-card div[style*="font-size: 1.5rem"] {
        font-size: 1.125rem !important;
    }

    .modern-card div[style*="font-size: 1.25rem"] {
        font-size: 1rem !important;
    }

    .modern-card div[style*="font-size: 1rem"] {
        font-size: 0.875rem !important;
    }

    .modern-card div[style*="width: 80px"] {
        width: 50px !important;
    }

    /* Stack percentage info vertically on very small screens */
    .modern-card td:last-child > div {
        flex-direction: column !important;
        gap: 4px !important;
    }
}

/* Extra small screens */
@media (max-width: 360px) {
    .modern-card th {
        padding: 10px 6px !important;
        font-size: 0.65rem !important;
    }

    .modern-card td {
        padding: 10px 6px !important;
    }

    .modern-card div[style*="width: 32px"] {
        width: 28px !important;
        height: 28px !important;
        font-size: 0.75rem !important;
    }
}
</style>
