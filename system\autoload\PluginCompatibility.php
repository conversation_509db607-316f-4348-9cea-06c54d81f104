<?php

/**
 * Plugin Compatibility Helper for PHPNuxBill
 * Provides fallback functions for plugins that use system-specific functions
 * 
 * <AUTHOR> Enhanced
 */

class PluginCompatibility
{
    /**
     * Check if a function exists and is enabled
     */
    public static function isFunctionAvailable($function_name)
    {
        return function_exists($function_name) && 
               !in_array($function_name, self::getDisabledFunctions());
    }
    
    /**
     * Get list of disabled functions
     */
    public static function getDisabledFunctions()
    {
        $disabled = ini_get('disable_functions');
        return $disabled ? array_map('trim', explode(',', $disabled)) : [];
    }
    
    /**
     * Safe wrapper for sys_getloadavg() - Windows compatible
     */
    public static function getLoadAverage()
    {
        // Check if the original function exists and is available (not our fallback)
        if (function_exists('sys_getloadavg') &&
            !in_array('sys_getloadavg', self::getDisabledFunctions()) &&
            PHP_OS_FAMILY !== 'Windows') {
            return sys_getloadavg();
        }
        
        // Windows fallback using WMI
        if (PHP_OS_FAMILY === 'Windows' && self::isFunctionAvailable('shell_exec')) {
            try {
                $output = shell_exec('wmic cpu get loadpercentage /value');
                if ($output && preg_match('/LoadPercentage=(\d+)/', $output, $matches)) {
                    $load = intval($matches[1]) / 100;
                    return [$load, $load, $load]; // Simulate 1, 5, 15 minute averages
                }
            } catch (Exception $e) {
                // Ignore error
            }
        }
        
        // Fallback: return dummy values
        return [0.0, 0.0, 0.0];
    }
    
    /**
     * Safe wrapper for shell_exec() with fallbacks
     */
    public static function safeShellExec($command, $fallback = '')
    {
        if (!self::isFunctionAvailable('shell_exec')) {
            return $fallback;
        }
        
        try {
            $result = shell_exec($command);
            return $result !== null ? $result : $fallback;
        } catch (Exception $e) {
            return $fallback;
        }
    }
    
    /**
     * Get system information with fallbacks
     */
    public static function getSystemInfo()
    {
        $info = [];
        
        // Operating System
        $info['os'] = PHP_OS_FAMILY;
        $info['os_version'] = php_uname('r');
        
        // PHP Version
        $info['php_version'] = PHP_VERSION;
        
        // Server Software
        $info['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
        
        // Memory Usage
        $info['memory_usage'] = memory_get_usage(true);
        $info['memory_peak'] = memory_get_peak_usage(true);
        $info['memory_limit'] = ini_get('memory_limit');
        
        // Load Average
        $info['load_average'] = self::getLoadAverage();
        
        // Disk Space (if possible)
        try {
            $info['disk_free'] = disk_free_space('.');
            $info['disk_total'] = disk_total_space('.');
        } catch (Exception $e) {
            $info['disk_free'] = 0;
            $info['disk_total'] = 0;
        }
        
        // CPU Info (basic)
        if (self::isFunctionAvailable('shell_exec')) {
            if (PHP_OS_FAMILY === 'Linux') {
                $cpu_info = self::safeShellExec('cat /proc/cpuinfo | grep "model name" | head -1', '');
                if ($cpu_info && preg_match('/model name\s*:\s*(.+)/', $cpu_info, $matches)) {
                    $info['cpu_model'] = trim($matches[1]);
                }
            } elseif (PHP_OS_FAMILY === 'Windows') {
                $cpu_info = self::safeShellExec('wmic cpu get name /value', '');
                if ($cpu_info && preg_match('/Name=(.+)/', $cpu_info, $matches)) {
                    $info['cpu_model'] = trim($matches[1]);
                }
            }
        }
        
        if (!isset($info['cpu_model'])) {
            $info['cpu_model'] = 'Unknown CPU';
        }
        
        return $info;
    }
    
    /**
     * Get system distribution info with fallbacks
     */
    public static function getSystemDistro()
    {
        if (PHP_OS_FAMILY !== 'Linux') {
            return PHP_OS_FAMILY;
        }
        
        // Try different methods to get distro info
        $methods = [
            'cat /etc/os-release | grep PRETTY_NAME',
            'cat /etc/redhat-release',
            'cat /etc/debian_version',
            'lsb_release -d'
        ];
        
        foreach ($methods as $method) {
            $result = self::safeShellExec($method, '');
            if (!empty($result)) {
                if (strpos($method, 'os-release') !== false && preg_match('/PRETTY_NAME="([^"]+)"/', $result, $matches)) {
                    return $matches[1];
                } elseif (strpos($method, 'lsb_release') !== false && preg_match('/Description:\s*(.+)/', $result, $matches)) {
                    return trim($matches[1]);
                } elseif (!empty(trim($result))) {
                    return trim($result);
                }
            }
        }
        
        return 'Linux (Unknown Distribution)';
    }
    
    /**
     * Format bytes to human readable format
     */
    public static function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Check if running on Windows
     */
    public static function isWindows()
    {
        return PHP_OS_FAMILY === 'Windows';
    }
    
    /**
     * Check if running on Linux
     */
    public static function isLinux()
    {
        return PHP_OS_FAMILY === 'Linux';
    }
}

// Define global fallback functions if they don't exist
// Note: We need to be careful to avoid infinite recursion
if (!function_exists('sys_getloadavg')) {
    function sys_getloadavg() {
        // Directly implement fallback logic here to avoid recursion
        if (PHP_OS_FAMILY === 'Windows' && function_exists('shell_exec')) {
            try {
                $output = shell_exec('wmic cpu get loadpercentage /value');
                if ($output && preg_match('/LoadPercentage=(\d+)/', $output, $matches)) {
                    $load = intval($matches[1]) / 100;
                    return [$load, $load, $load];
                }
            } catch (Exception $e) {
                // Ignore error
            }
        }
        // Fallback: return dummy values
        return [0.0, 0.0, 0.0];
    }
}

// Safe wrapper for shell_exec that plugins can use
if (!function_exists('safe_shell_exec')) {
    function safe_shell_exec($command, $fallback = '') {
        return PluginCompatibility::safeShellExec($command, $fallback);
    }
}

// Helper functions for system info plugins
if (!function_exists('system_info_getCpuInfo')) {
    function system_info_getCpuInfo() {
        $load = PluginCompatibility::getLoadAverage();
        return [
            'load_average' => $load,
            'load_1min' => $load[0] ?? 0.0,
            'load_5min' => $load[1] ?? 0.0,
            'load_15min' => $load[2] ?? 0.0,
            'cpu_usage' => ($load[0] ?? 0.0) * 100 // Convert to percentage
        ];
    }
}

if (!function_exists('system_info_getSystemDistro')) {
    function system_info_getSystemDistro() {
        return PluginCompatibility::getSystemDistro();
    }
}

if (!function_exists('system_info_getSystemInfo')) {
    function system_info_getSystemInfo() {
        return PluginCompatibility::getSystemInfo();
    }
}
