{include file="sections/header.tpl"}
<style>
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        display: inline-block;
        padding: 5px 10px;
        margin-right: 5px;
        border: 1px solid #ccc;
        background-color: #fff;
        color: #333;
        cursor: pointer;
    }

    /* Modern Customer Section with Soft Theme Colors */
    .modern-customer-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid rgba(0, 191, 165, 0.1);
        border-radius: 20px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 8px 25px rgba(0, 191, 165, 0.08);
    }

    .modern-customer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        flex-wrap: wrap;
        gap: 16px;
    }

    .modern-customer-title {
        color: #495057;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .modern-customer-title i {
        color: #00BFA5;
    }

    .modern-customer-actions {
        display: flex;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;
    }

    .modern-csv-btn {
        background: rgba(0, 191, 165, 0.1);
        border: 1px solid rgba(0, 191, 165, 0.2);
        color: #00BFA5;
        padding: 8px 16px;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .modern-csv-btn:hover {
        background: rgba(0, 191, 165, 0.15);
        color: #00A693;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 191, 165, 0.2);
    }

    /* Horizontal Tab Menu Styles with Soft Theme */
    .horizontal-tab-container {
        background: rgba(255, 255, 255, 0.7);
        border-radius: 16px;
        padding: 8px;
        margin-bottom: 24px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(0, 191, 165, 0.15);
        box-shadow: 0 2px 10px rgba(0, 191, 165, 0.05);
    }

    .horizontal-tab-menu {
        display: flex;
        gap: 4px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        padding: 4px;
    }

    .horizontal-tab-menu::-webkit-scrollbar {
        display: none;
    }

    .horizontal-tab-item {
        background: rgba(248, 249, 250, 0.8);
        border: 1px solid rgba(0, 191, 165, 0.1);
        color: #6c757d;
        padding: 12px 20px;
        border-radius: 12px;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
        min-width: fit-content;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .horizontal-tab-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 191, 165, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .horizontal-tab-item:hover::before {
        left: 100%;
    }

    .horizontal-tab-item:hover {
        background: rgba(0, 191, 165, 0.1);
        color: #495057;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 191, 165, 0.15);
        border-color: rgba(0, 191, 165, 0.3);
    }

    .horizontal-tab-item:hover i {
        color: #00BFA5;
    }

    .horizontal-tab-item.active {
        background: linear-gradient(135deg, #00BFA5, #4DB6AC);
        color: white;
        font-weight: 600;
        border-color: #00BFA5;
        box-shadow: 0 4px 15px rgba(0, 191, 165, 0.3);
    }

    .horizontal-tab-item.active:hover {
        background: linear-gradient(135deg, #00A693, #45A29E);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0, 191, 165, 0.4);
    }

    .horizontal-tab-item.active i {
        color: white;
    }

    .horizontal-tab-item i {
        font-size: 1rem;
        opacity: 0.8;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    /* Search Section with Soft Theme */
    .modern-search-section {
        background: rgba(255, 255, 255, 0.6);
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 16px;
        border: 1px solid rgba(0, 191, 165, 0.1);
    }

    .modern-search-grid {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 12px;
        align-items: end;
    }

    .modern-search-input {
        background: white;
        border: 1px solid rgba(0, 191, 165, 0.2);
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 0.95rem;
        color: #495057;
        transition: all 0.3s ease;
    }

    .modern-search-input:focus {
        outline: none;
        border-color: #00BFA5;
        box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
        background: white;
    }

    .modern-search-input::placeholder {
        color: #adb5bd;
    }

    .modern-add-btn {
        background: #00BFA5;
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 12px;
        font-size: 0.95rem;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
    }

    .modern-add-btn:hover {
        background: #00A693;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 191, 165, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .modern-customer-container {
            padding: 16px;
            border-radius: 16px;
        }

        .modern-customer-header {
            flex-direction: column;
            align-items: stretch;
            text-align: center;
        }

        .horizontal-tab-item {
            padding: 10px 16px;
            font-size: 0.85rem;
        }

        .modern-search-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .modern-customer-title {
            font-size: 1.25rem;
            justify-content: center;
        }

        .modern-customer-actions {
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .horizontal-tab-item {
            padding: 8px 12px;
            font-size: 0.8rem;
            gap: 6px;
        }

        .horizontal-tab-item i {
            font-size: 0.9rem;
        }
    }
</style>

<div class="row">
    <div class="col-sm-12">
        <!-- Modern Customer Section with Horizontal Tabs -->
        <div class="modern-customer-container">
            <div class="modern-customer-header">
                <h1 class="modern-customer-title">
                    <i class="fa fa-users"></i>
                    {Lang::T('Customer')}
                </h1>
                <div class="modern-customer-actions">
                    {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                    <a class="modern-csv-btn"
                        href="{Text::url('customers/csv&token=', $csrf_token)}"
                        onclick="return ask(this, '{Lang::T("This will export to CSV")}?')">
                        <i class="fa fa-download"></i>
                        CSV
                    </a>
                    {/if}
                </div>
            </div>

            <!-- Horizontal Tab Menu -->
            <div class="horizontal-tab-container">
                <div class="horizontal-tab-menu">
                    {foreach $tab_filters as $tab_key => $tab_info}
                        {if $tab_key == 'All'}
                            <a href="{Text::url('customers')}"
                               class="horizontal-tab-item {if $filter == $tab_key || $filter == '' || !$filter}active{/if}">
                                <i class="fa {$tab_info.icon}"></i>
                                {Lang::T($tab_info.label)}
                            </a>
                        {else}
                            <a href="{Text::url('customers')}&filter={$tab_key}"
                               class="horizontal-tab-item {if $filter == $tab_key}active{/if}">
                                <i class="fa {$tab_info.icon}"></i>
                                {Lang::T($tab_info.label)}
                            </a>
                        {/if}
                    {/foreach}
                </div>
            </div>

            <!-- Search Section -->
            <div class="modern-search-section">
                <form id="site-search" method="post" action="{Text::url('customers')}">
                    <input type="hidden" name="csrf_token" value="{$csrf_token}">
                    <div class="modern-search-grid">
                        <input type="text" name="search" class="modern-search-input"
                            placeholder="{Lang::T('Search')}..." value="{$search}">
                        <a href="{Text::url('customers/add')}" class="modern-add-btn">
                            <i class="fa fa-plus"></i>
                            {Lang::T('Add New Contact')}
                        </a>
                    </div>

                    <!-- Hidden fields for maintaining current filters -->
                    <input type="hidden" name="order" value="{$order}">
                    <input type="hidden" name="orderby" value="{$orderby}">
                    <input type="hidden" name="filter" value="{$filter}">
                </form>
            </div>
        </div>

        <!-- Table Section -->
        <div class="panel panel-hovered mb20 panel-primary">
            <div class="panel-body">
                <div class="table-responsive table_mobile">
                    <table id="customerTable" class="table table-bordered table-striped table-condensed">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all"></th>
                                <th>{Lang::T('Username')}</th>
                                <th>Photo</th>
                                <th>{Lang::T('Account Type')}</th>
                                <th>{Lang::T('Full Name')}</th>
                                <th>{Lang::T('Balance')}</th>
                                <th>{Lang::T('Contact')}</th>
                                <th>{Lang::T('Package')}</th>
                                <th>{Lang::T('Service Type')}</th>
                                <th>PPPOE</th>
                                <th>{Lang::T('Status')}</th>
                                <th>{Lang::T('Created On')}</th>
                                <th>{Lang::T('Manage')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach $d as $ds}
                            <tr {if $ds['status'] !='Active' }class="danger" {/if}>
                                <td><input type="checkbox" name="customer_ids[]" value="{$ds['id']}"></td>
                                <td onclick="window.location.href = '{Text::url('customers/view/', $ds['id'])}'"
                                    style="cursor:pointer;">{$ds['username']}</td>
                                <td>
                                    <a href="{$app_url}/{$UPLOAD_PATH}{$ds['photo']}" target="photo">
                                        <img src="{$app_url}/{$UPLOAD_PATH}{$ds['photo']}.thumb.jpg" width="32" alt="">
                                    </a>
                                </td>
                                <td>{$ds['account_type']}</td>
                                <td onclick="window.location.href = '{Text::url('customers/view/', $ds['id'])}'"
                                    style="cursor: pointer;">{$ds['fullname']}</td>
                                <td>{Lang::moneyFormat($ds['balance'])}</td>
                                <td align="center">
                                    {if $ds['phonenumber']}
                                    <a href="tel:{$ds['phonenumber']}" class="btn btn-default btn-xs"
                                        title="{$ds['phonenumber']}"><i class="glyphicon glyphicon-earphone"></i></a>
                                    {/if}
                                    {if $ds['email']}
                                    <a href="mailto:{$ds['email']}" class="btn btn-default btn-xs"
                                        title="{$ds['email']}"><i class="glyphicon glyphicon-envelope"></i></a>
                                    {/if}
                                    {if $ds['coordinates']}
                                    <a href="https://www.google.com/maps/dir//{$ds['coordinates']}/" target="_blank"
                                        class="btn btn-default btn-xs" title="{$ds['coordinates']}"><i
                                            class="glyphicon glyphicon-map-marker"></i></a>
                                    {/if}
                                </td>
                                <td align="center" api-get-text="{Text::url('autoload/plan_is_active/')}{$ds['id']}">
                                    <span class="label label-default">&bull;</span>
                                </td>
                                <td>{$ds['service_type']}</td>
                                <td>
                                    {$ds['pppoe_username']}
                                    {if !empty($ds['pppoe_username']) && !empty($ds['pppoe_ip'])}:{/if}
                                    {$ds['pppoe_ip']}
                                </td>
                                <td>{Lang::T($ds['status'])}</td>
                                <td>{Lang::dateTimeFormat($ds['created_at'])}</td>
                                <td align="center">
                                    <a href="{Text::url('customers/view/')}{$ds['id']}" id="{$ds['id']}"
                                        style="margin: 0px; color:black"
                                        class="btn btn-success btn-xs">&nbsp;&nbsp;{Lang::T('View')}&nbsp;&nbsp;</a>
                                    <a href="{Text::url('customers/edit/', $ds['id'], '&token=', $csrf_token)}"
                                        id="{$ds['id']}" style="margin: 0px; color:black"
                                        class="btn btn-info btn-xs">&nbsp;&nbsp;{Lang::T('Edit')}&nbsp;&nbsp;</a>
                                    <a href="{Text::url('customers/sync/', $ds['id'], '&token=', $csrf_token)}"
                                        id="{$ds['id']}" style="margin: 5px; color:black"
                                        class="btn btn-success btn-xs">&nbsp;&nbsp;{Lang::T('Sync')}&nbsp;&nbsp;</a>
                                    <a href="{Text::url('plan/recharge/', $ds['id'], '&token=', $csrf_token)}"
                                        id="{$ds['id']}" style="margin: 0px;"
                                        class="btn btn-primary btn-xs">{Lang::T('Recharge')}</a>
                                </td>
                            </tr>
                            {/foreach}
                        </tbody>
                    </table>
                    <div class="row" style="padding: 5px">
                        <div class="col-lg-3 col-lg-offset-9">
                            <div class="btn-group btn-group-justified" role="group">
                                <!-- <div class="btn-group" role="group">
                                    {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                                    <button id="deleteSelectedTokens" class="btn btn-danger">{Lang::T('Delete
                                        Selected')}</button>
                                    {/if}
                                </div> -->
                                <div class="btn-group" role="group">
                                    <button id="sendMessageToSelected" class="btn btn-success">{Lang::T('Send
                                        Message')}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {include file="pagination.tpl"}
            </div>
        </div>
    </div>
</div>
<!-- Modal for Sending Messages -->
<div id="sendMessageModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="sendMessageModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendMessageModalLabel">{Lang::T('Send Message')}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <select id="messageType" class="form-control">
                    <option value="all">{Lang::T('All')}</option>
                    <option value="email">{Lang::T('Email')}</option>
                    <option value="inbox">{Lang::T('Inbox')}</option>
                    <option value="sms">{Lang::T('SMS')}</option>
                    <option value="wa">{Lang::T('WhatsApp')}</option>
                </select>
                <br>
                <textarea id="messageContent" class="form-control" rows="4"
                    placeholder="{Lang::T('Enter your message here...')}"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{Lang::T('Close')}</button>
                <button type="button" id="sendMessageButton" class="btn btn-primary">{Lang::T('Send Message')}</button>
            </div>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    // Handle search form submission with current filter
    document.getElementById('site-search').addEventListener('submit', function(e) {
        e.preventDefault();

        const searchValue = this.querySelector('input[name="search"]').value.trim();
        const currentFilter = this.querySelector('input[name="filter"]').value;

        // Build URL with current filter and search
        const baseUrl = '{Text::url("customers")}';
        const searchParams = new URLSearchParams();

        if (currentFilter && currentFilter !== 'All') {
            searchParams.append('filter', currentFilter);
        }
        if (searchValue) {
            searchParams.append('search', searchValue);
        }

        // Redirect with search parameters
        const separator = baseUrl.includes('?') ? '&' : '?';
        const finalUrl = searchParams.toString() ? baseUrl + separator + searchParams.toString() : baseUrl;
        window.location.href = finalUrl;
    });

    // Handle search input on Enter key
    document.querySelector('.modern-search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('site-search').dispatchEvent(new Event('submit'));
        }
    });

    // Add smooth scrolling for horizontal tabs on mobile
    document.addEventListener('DOMContentLoaded', function() {
        const tabMenu = document.querySelector('.horizontal-tab-menu');
        const activeTab = document.querySelector('.horizontal-tab-item.active');

        if (activeTab && tabMenu) {
            // Scroll active tab into view
            activeTab.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
            });
        }

        // Add touch scroll support for mobile
        let isDown = false;
        let startX;
        let scrollLeft;

        tabMenu.addEventListener('mousedown', (e) => {
            isDown = true;
            startX = e.pageX - tabMenu.offsetLeft;
            scrollLeft = tabMenu.scrollLeft;
        });

        tabMenu.addEventListener('mouseleave', () => {
            isDown = false;
        });

        tabMenu.addEventListener('mouseup', () => {
            isDown = false;
        });

        tabMenu.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - tabMenu.offsetLeft;
            const walk = (x - startX) * 2;
            tabMenu.scrollLeft = scrollLeft - walk;
        });
    });
</script>
<script>
    // Select or deselect all checkboxes
    document.getElementById('select-all').addEventListener('change', function () {
        var checkboxes = document.querySelectorAll('input[name="customer_ids[]"]');
        for (var checkbox of checkboxes) {
            checkbox.checked = this.checked;
        }
    });

    $(document).ready(function () {
        let selectedCustomerIds = [];

        // Collect selected customer IDs when the button is clicked
        $('#sendMessageToSelected').on('click', function () {
            selectedCustomerIds = $('input[name="customer_ids[]"]:checked').map(function () {
                return $(this).val();
            }).get();

            if (selectedCustomerIds.length === 0) {
                Swal.fire({
                    title: 'Error!',
                    text: "{Lang::T('Please select at least one customer to send a message.')}",
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Open the modal
            $('#sendMessageModal').modal('show');
        });

        // Handle sending the message
        $('#sendMessageButton').on('click', function () {
            const message = $('#messageContent').val().trim();
            const messageType = $('#messageType').val();

            if (!message) {
                Swal.fire({
                    title: 'Error!',
                    text: "{Lang::T('Please enter a message to send.')}",
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Disable the button and show loading text
            $(this).prop('disabled', true).text('{Lang::T('Sending...')}');

            $.ajax({
                url: '?_route=message/send_bulk_selected',
                method: 'POST',
                data: {
                    customer_ids: selectedCustomerIds,
                    message_type: messageType,
                    message: message
                },
                dataType: 'json',
                success: function (response) {
                    // Handle success response
                    if (response.status === 'success') {
                        Swal.fire({
                            title: 'Success!',
                            text: "{Lang::T('Message sent successfully.')}",
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: "{Lang::T('Error sending message: ')}" + response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                    $('#sendMessageModal').modal('hide');
                    $('#messageContent').val(''); // Clear the message content
                },
                error: function () {
                    Swal.fire({
                        title: 'Error!',
                        text: "{Lang::T('Failed to send the message. Please try again.')}",
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                },
                complete: function () {
                    // Re-enable the button and reset text
                    $('#sendMessageButton').prop('disabled', false).text('{Lang::T('Send Message')}');
                }
            });
        });
    });

    $(document).ready(function () {
        $('#sendMessageModal').on('show.bs.modal', function () {
            $(this).attr('inert', 'true');
        });
        $('#sendMessageModal').on('shown.bs.modal', function () {
            $('#messageContent').focus();
            $(this).removeAttr('inert');
        });
        $('#sendMessageModal').on('hidden.bs.modal', function () {
            // $('#button').focus();
        });
    });
</script>
{include file = "sections/footer.tpl" }