{include file="sections/header.tpl"}
<!-- Modern Admin Profile Edit -->

<div class="row">
    <div class="col-lg-10 col-lg-offset-1 col-md-12">
        <div class="modern-card admin-profile-card" style="
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin-bottom: 30px;
        ">
            <!-- Profile Header -->
            <div class="admin-profile-header" style="
                background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                padding: 30px 24px 20px;
                color: white;
                text-align: center;
                position: relative;
            ">
                <div style="
                    position: absolute;
                    top: -50px;
                    right: -50px;
                    width: 150px;
                    height: 150px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                "></div>
                <h3 style="
                    font-size: 1.5rem;
                    font-weight: 600;
                    margin: 0 0 8px 0;
                    position: relative;
                    z-index: 2;
                ">
                    <i class="fa fa-user-cog" style="margin-right: 8px; opacity: 0.9;"></i>
                    {Lang::T('Profile')} - {$d['user_type']}
                </h3>
                <p style="
                    font-size: 0.875rem;
                    opacity: 0.9;
                    margin: 0;
                    position: relative;
                    z-index: 2;
                ">Edit admin profile information and credentials</p>
            </div>

            <!-- Profile Form -->
            <div class="admin-profile-form-container" style="padding: 30px 24px;">
                <form class="modern-admin-form" method="post" enctype="multipart/form-data" role="form"
                    action="{Text::url('settings/users-edit-post')}">
                    <input type="hidden" name="csrf_token" value="{$csrf_token}">
                    <input type="hidden" name="id" value="{$d['id']}">

                    <div class="row">
                        <!-- Left Column - Profile Information -->
                        <div class="col-md-6">
                            <div class="profile-section" style="
                                background: #f8fafc;
                                border-radius: 16px;
                                padding: 24px;
                                margin-bottom: 24px;
                                border: 1px solid #e5e7eb;
                            ">
                                <h4 style="
                                    font-size: 1.125rem;
                                    font-weight: 600;
                                    color: #374151;
                                    margin: 0 0 20px 0;
                                    padding-bottom: 12px;
                                    border-bottom: 2px solid #00BFA5;
                                ">
                                    <i class="fa fa-user" style="margin-right: 8px; color: #00BFA5;"></i>
                                    Personal Information
                                </h4>

                                <!-- Profile Photo Section -->
                                <div class="admin-photo-section" style="text-align: center; margin-bottom: 24px;">
                                    <div class="admin-photo-container" style="
                                        display: inline-block;
                                        position: relative;
                                        margin-bottom: 16px;
                                    ">
                                        <img src="{$app_url}/{$UPLOAD_PATH}{$d['photo']}.thumb.jpg"
                                            class="admin-profile-photo"
                                            alt="Admin Photo"
                                            onclick="return deletePhoto({$d['id']})"
                                            data-avatar="true"
                                            data-avatar-name="{$d['fullname']}"
                                            data-avatar-type="{$d['user_type']}"
                                            data-avatar-size="profile"
                                            style="
                                                width: 120px;
                                                height: 120px;
                                                border-radius: 50%;
                                                border: 4px solid #00BFA5;
                                                box-shadow: 0 8px 24px rgba(0, 191, 165, 0.3);
                                                cursor: pointer;
                                                transition: all 0.3s ease;
                                                object-fit: cover;
                                            "
                                            onmouseover="this.style.transform='scale(1.05)'"
                                            onmouseout="this.style.transform='scale(1)'">
                                        <div class="photo-status-badge" style="
                                            position: absolute;
                                            bottom: 8px;
                                            right: 8px;
                                            width: 24px;
                                            height: 24px;
                                            background: {if $d['status'] == 'Active'}#10b981{else}#ef4444{/if};
                                            border: 3px solid white;
                                            border-radius: 50%;
                                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                                        "></div>
                                    </div>
                                </div>
                                <!-- Photo Upload Section -->
                                <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                    <label class="modern-form-label" style="
                                        display: block;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                        margin-bottom: 8px;
                                    ">
                                        <i class="fa fa-camera" style="margin-right: 8px; color: #00BFA5;"></i>
                                        {Lang::T('Photo')}
                                    </label>
                                    <div class="row">
                                        <div class="col-md-8 col-sm-8 col-xs-12">
                                            <div class="modern-file-input" style="
                                                position: relative;
                                                display: inline-block;
                                                width: 100%;
                                            ">
                                                <input type="file"
                                                       class="file-input"
                                                       name="photo"
                                                       accept="image/*"
                                                       id="admin-photo-input"
                                                       style="
                                                           position: absolute;
                                                           opacity: 0;
                                                           width: 100%;
                                                           height: 100%;
                                                           cursor: pointer;
                                                       ">
                                                <div class="file-input-display" style="
                                                    background: #f8fafc;
                                                    border: 2px dashed #d1d5db;
                                                    border-radius: 12px;
                                                    padding: 12px;
                                                    text-align: center;
                                                    transition: all 0.3s ease;
                                                    cursor: pointer;
                                                " onmouseover="this.style.borderColor='#00BFA5'; this.style.background='#f0fdfa'"
                                                   onmouseout="this.style.borderColor='#d1d5db'; this.style.background='#f8fafc'">
                                                    <i class="fa fa-cloud-upload" style="font-size: 1.25rem; color: #6b7280; margin-bottom: 6px;"></i>
                                                    <div style="font-size: 0.75rem; color: #6b7280;">
                                                        Upload new photo
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12" style="margin-top: 4px;">
                                            <div class="face-detect-option" style="
                                                background: #f0fdfa;
                                                border: 1px solid #a7f3d0;
                                                border-radius: 12px;
                                                padding: 8px;
                                            ">
                                                <label class="modern-checkbox" style="
                                                    display: flex;
                                                    align-items: center;
                                                    font-size: 0.7rem;
                                                    color: #059669;
                                                    cursor: pointer;
                                                    margin: 0;
                                                ">
                                                    <input type="checkbox"
                                                           checked
                                                           name="faceDetect"
                                                           value="yes"
                                                           style="margin-right: 6px;">
                                                    <i class="fa fa-eye" style="margin-right: 4px;"></i>
                                                    Face Detect
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Full Name -->
                                <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                    <label class="modern-form-label" style="
                                        display: block;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                        margin-bottom: 8px;
                                    ">
                                        <i class="fa fa-id-card" style="margin-right: 8px; color: #00BFA5;"></i>
                                        {Lang::T('Full Name')}
                                    </label>
                                    <input type="text"
                                           class="modern-form-input"
                                           id="fullname"
                                           name="fullname"
                                           value="{$d['fullname']}"
                                           style="
                                               width: 100%;
                                               padding: 10px 14px;
                                               border: 2px solid #e5e7eb;
                                               border-radius: 12px;
                                               font-size: 0.875rem;
                                               transition: all 0.3s ease;
                                           "
                                           onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                </div>

                                <!-- Phone -->
                                <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                    <label class="modern-form-label" style="
                                        display: block;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                        margin-bottom: 8px;
                                    ">
                                        <i class="fa fa-phone" style="margin-right: 8px; color: #00BFA5;"></i>
                                        {Lang::T('Phone')}
                                    </label>
                                    <input type="tel"
                                           class="modern-form-input"
                                           id="phone"
                                           name="phone"
                                           value="{$d['phone']}"
                                           style="
                                               width: 100%;
                                               padding: 10px 14px;
                                               border: 2px solid #e5e7eb;
                                               border-radius: 12px;
                                               font-size: 0.875rem;
                                               transition: all 0.3s ease;
                                           "
                                           onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                </div>

                                <!-- Email -->
                                <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                    <label class="modern-form-label" style="
                                        display: block;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                        margin-bottom: 8px;
                                    ">
                                        <i class="fa fa-envelope" style="margin-right: 8px; color: #00BFA5;"></i>
                                        {Lang::T('Email')}
                                    </label>
                                    <input type="email"
                                           class="modern-form-input"
                                           id="email"
                                           name="email"
                                           value="{$d['email']}"
                                           style="
                                               width: 100%;
                                               padding: 10px 14px;
                                               border: 2px solid #e5e7eb;
                                               border-radius: 12px;
                                               font-size: 0.875rem;
                                               transition: all 0.3s ease;
                                           "
                                           onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                </div>

                                <!-- Location Fields -->
                                <div class="location-fields" style="margin-bottom: 20px;">
                                    <label class="modern-form-label" style="
                                        display: block;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                        margin-bottom: 8px;
                                    ">
                                        <i class="fa fa-map-marker" style="margin-right: 8px; color: #00BFA5;"></i>
                                        Location Information
                                    </label>
                                    <div class="row">
                                        <div class="col-md-4 col-sm-4 col-xs-12" style="margin-bottom: 12px;">
                                            <input type="text"
                                                   class="modern-form-input"
                                                   id="city"
                                                   name="city"
                                                   placeholder="{Lang::T('City')}"
                                                   value="{$d['city']}"
                                                   style="
                                                       width: 100%;
                                                       padding: 10px 14px;
                                                       border: 2px solid #e5e7eb;
                                                       border-radius: 12px;
                                                       font-size: 0.875rem;
                                                       transition: all 0.3s ease;
                                                   "
                                                   onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12" style="margin-bottom: 12px;">
                                            <input type="text"
                                                   class="modern-form-input"
                                                   id="subdistrict"
                                                   name="subdistrict"
                                                   placeholder="{Lang::T('Sub District')}"
                                                   value="{$d['subdistrict']}"
                                                   style="
                                                       width: 100%;
                                                       padding: 10px 14px;
                                                       border: 2px solid #e5e7eb;
                                                       border-radius: 12px;
                                                       font-size: 0.875rem;
                                                       transition: all 0.3s ease;
                                                   "
                                                   onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12" style="margin-bottom: 12px;">
                                            <input type="text"
                                                   class="modern-form-input"
                                                   id="ward"
                                                   name="ward"
                                                   placeholder="{Lang::T('Ward')}"
                                                   value="{$d['ward']}"
                                                   style="
                                                       width: 100%;
                                                       padding: 10px 14px;
                                                       border: 2px solid #e5e7eb;
                                                       border-radius: 12px;
                                                       font-size: 0.875rem;
                                                       transition: all 0.3s ease;
                                                   "
                                                   onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Right Column - Credentials -->
                        <div class="col-md-6">
                            <div class="credentials-section" style="
                                background: #f8fafc;
                                border-radius: 16px;
                                padding: 24px;
                                margin-bottom: 24px;
                                border: 1px solid #e5e7eb;
                            ">
                                <h4 style="
                                    font-size: 1.125rem;
                                    font-weight: 600;
                                    color: #374151;
                                    margin: 0 0 20px 0;
                                    padding-bottom: 12px;
                                    border-bottom: 2px solid #00BFA5;
                                ">
                                    <i class="fa fa-key" style="margin-right: 8px; color: #00BFA5;"></i>
                                    {Lang::T('Credentials')}
                                </h4>
                                {if ($_admin['id']) neq ($d['id'])}
                                    <!-- Status -->
                                    <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                        <label class="modern-form-label" style="
                                            display: block;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            color: #374151;
                                            margin-bottom: 8px;
                                        ">
                                            <i class="fa fa-toggle-on" style="margin-right: 8px; color: #00BFA5;"></i>
                                            {Lang::T('Status')}
                                        </label>
                                        <select name="status"
                                                id="status"
                                                class="modern-form-select"
                                                style="
                                                    width: 100%;
                                                    padding: 10px 14px;
                                                    border: 2px solid #e5e7eb;
                                                    border-radius: 12px;
                                                    font-size: 0.875rem;
                                                    transition: all 0.3s ease;
                                                    background: white;
                                                "
                                                onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                                onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                            <option value="Active" {if $d['status'] eq 'Active'}selected="selected" {/if}>
                                                {Lang::T('Active')}</option>
                                            <option value="Inactive" {if $d['status'] eq 'Inactive'}selected="selected" {/if}>
                                                {Lang::T('Inactive')}</option>
                                        </select>
                                    </div>

                                    <!-- User Type -->
                                    <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                        <label class="modern-form-label" style="
                                            display: block;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            color: #374151;
                                            margin-bottom: 8px;
                                        ">
                                            <i class="fa fa-users" style="margin-right: 8px; color: #00BFA5;"></i>
                                            {Lang::T('User Type')}
                                        </label>
                                        <select name="user_type"
                                                id="user_type"
                                                class="modern-form-select"
                                                onchange="checkUserType(this)"
                                                style="
                                                    width: 100%;
                                                    padding: 10px 14px;
                                                    border: 2px solid #e5e7eb;
                                                    border-radius: 12px;
                                                    font-size: 0.875rem;
                                                    transition: all 0.3s ease;
                                                    background: white;
                                                "
                                                onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                                onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                            {if $_admin['user_type'] eq 'Agent'}
                                                <option value="Sales" {if $d['user_type'] eq 'Sales'}selected="selected" {/if}>Sales</option>
                                            {/if}
                                            {if $_admin['user_type'] eq 'Admin' || $_admin['user_type'] eq 'SuperAdmin'}
                                                <option value="Report" {if $d['user_type'] eq 'Report'}selected="selected" {/if}>Report Viewer</option>
                                                <option value="Agent" {if $d['user_type'] eq 'Agent'}selected="selected" {/if}>Agent</option>
                                                <option value="Sales" {if $d['user_type'] eq 'Sales'}selected="selected" {/if}>Sales</option>
                                            {/if}
                                            {if $_admin['user_type'] eq 'SuperAdmin'}
                                                <option value="Admin" {if $d['user_type'] eq 'Admin'}selected="selected" {/if}>Administrator</option>
                                                <option value="SuperAdmin" {if $d['user_type'] eq 'SuperAdmin'}selected="selected" {/if}>Super Administrator</option>
                                            {/if}
                                        </select>
                                    </div>

                                    <!-- Agent Chooser -->
                                    <div class="form-group modern-form-group {if $d['user_type'] neq 'Sales'}hidden{/if}"
                                         id="agentChooser"
                                         style="margin-bottom: 20px;">
                                        <label class="modern-form-label" style="
                                            display: block;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            color: #374151;
                                            margin-bottom: 8px;
                                        ">
                                            <i class="fa fa-user-tie" style="margin-right: 8px; color: #00BFA5;"></i>
                                            {Lang::T('Agent')}
                                        </label>
                                        <select name="root"
                                                id="root"
                                                class="modern-form-select"
                                                style="
                                                    width: 100%;
                                                    padding: 10px 14px;
                                                    border: 2px solid #e5e7eb;
                                                    border-radius: 12px;
                                                    font-size: 0.875rem;
                                                    transition: all 0.3s ease;
                                                    background: white;
                                                "
                                                onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                                onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                            {foreach $agents as $agent}
                                                <option value="{$agent['id']}">{$agent['username']} | {$agent['fullname']} | {$agent['phone']}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                {/if}
                                <!-- Username -->
                                <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                    <label class="modern-form-label" style="
                                        display: block;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                        margin-bottom: 8px;
                                    ">
                                        <i class="fa fa-user" style="margin-right: 8px; color: #00BFA5;"></i>
                                        {Lang::T('Username')}
                                    </label>
                                    <input type="text"
                                           class="modern-form-input"
                                           id="username"
                                           name="username"
                                           value="{$d['username']}"
                                           style="
                                               width: 100%;
                                               padding: 10px 14px;
                                               border: 2px solid #e5e7eb;
                                               border-radius: 12px;
                                               font-size: 0.875rem;
                                               transition: all 0.3s ease;
                                           "
                                           onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                </div>

                                <!-- Password -->
                                <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                    <label class="modern-form-label" style="
                                        display: block;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                        margin-bottom: 8px;
                                    ">
                                        <i class="fa fa-lock" style="margin-right: 8px; color: #00BFA5;"></i>
                                        {Lang::T('Password')}
                                    </label>
                                    <input type="password"
                                           class="modern-form-input"
                                           id="password"
                                           name="password"
                                           style="
                                               width: 100%;
                                               padding: 10px 14px;
                                               border: 2px solid #e5e7eb;
                                               border-radius: 12px;
                                               font-size: 0.875rem;
                                               transition: all 0.3s ease;
                                           "
                                           onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                    <div class="help-text" style="
                                        font-size: 0.75rem;
                                        color: #6b7280;
                                        margin-top: 6px;
                                        display: flex;
                                        align-items: center;
                                    ">
                                        <i class="fa fa-info-circle" style="margin-right: 6px; color: #9ca3af;"></i>
                                        {Lang::T('Keep Blank to do not change Password')}
                                    </div>
                                </div>

                                <!-- Confirm Password -->
                                <div class="form-group modern-form-group" style="margin-bottom: 20px;">
                                    <label class="modern-form-label" style="
                                        display: block;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                        color: #374151;
                                        margin-bottom: 8px;
                                    ">
                                        <i class="fa fa-lock" style="margin-right: 8px; color: #00BFA5;"></i>
                                        {Lang::T('Confirm Password')}
                                    </label>
                                    <input type="password"
                                           class="modern-form-input"
                                           id="cpassword"
                                           name="cpassword"
                                           placeholder="{Lang::T('Confirm Password')}"
                                           style="
                                               width: 100%;
                                               padding: 10px 14px;
                                               border: 2px solid #e5e7eb;
                                               border-radius: 12px;
                                               font-size: 0.875rem;
                                               transition: all 0.3s ease;
                                           "
                                           onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                                    <div class="help-text" style="
                                        font-size: 0.75rem;
                                        color: #6b7280;
                                        margin-top: 6px;
                                        display: flex;
                                        align-items: center;
                                    ">
                                        <i class="fa fa-info-circle" style="margin-right: 6px; color: #9ca3af;"></i>
                                        {Lang::T('Keep Blank to do not change Password')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Form Actions -->
                    <div class="form-actions" style="
                        margin-top: 32px;
                        padding-top: 24px;
                        border-top: 1px solid #e5e7eb;
                        text-align: center;
                    ">
                        <div class="row">
                            <div class="col-md-6 col-md-offset-3">
                                <button class="modern-btn-primary"
                                        onclick="return ask(this, 'Continue the Admin change process?')"
                                        type="submit"
                                        style="
                                            width: 100%;
                                            padding: 14px 24px;
                                            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                                            color: white;
                                            border: none;
                                            border-radius: 12px;
                                            font-size: 0.875rem;
                                            font-weight: 600;
                                            cursor: pointer;
                                            transition: all 0.3s ease;
                                            box-shadow: 0 4px 12px rgba(0, 191, 165, 0.3);
                                            margin-bottom: 12px;
                                        "
                                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(0, 191, 165, 0.4)'"
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 191, 165, 0.3)'">
                                    <i class="fa fa-save" style="margin-right: 8px;"></i>
                                    {Lang::T('Save Changes')}
                                </button>
                                <div style="
                                    font-size: 0.875rem;
                                    color: #6b7280;
                                ">
                                    Or <a href="{Text::url('settings/users')}"
                                          style="
                                              color: #00BFA5;
                                              text-decoration: none;
                                              font-weight: 500;
                                          "
                                          onmouseover="this.style.textDecoration='underline'"
                                          onmouseout="this.style.textDecoration='none'">
                                        {Lang::T('Cancel')}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function checkUserType($field) {
        if ($field.value == 'Sales') {
            $('#agentChooser').removeClass('hidden');
        } else {
            $('#agentChooser').addClass('hidden');
        }
    }

    function deletePhoto(id) {
        if (confirm('Delete photo?')) {
            if (confirm('Are you sure to delete photo?')) {
                window.location.href = '{Text::url('settings/users-edit/')}'+id+'/deletePhoto'
            }
        }
    }

    // Initialize admin profile page features
    document.addEventListener('DOMContentLoaded', function() {
        // File input display functionality for admin
        const fileInput = document.getElementById('admin-photo-input');
        const fileDisplay = document.querySelector('.file-input-display');

        if (fileInput && fileDisplay) {
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const fileName = file.name;
                    const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

                    fileDisplay.innerHTML =
                        '<i class="fa fa-check-circle" style="font-size: 1.25rem; color: #00BFA5; margin-bottom: 6px;"></i>' +
                        '<div style="font-size: 0.75rem; color: #00BFA5; font-weight: 600;">' +
                            fileName +
                        '</div>' +
                        '<div style="font-size: 0.65rem; color: #6b7280; margin-top: 2px;">' +
                            fileSize + ' MB' +
                        '</div>';
                    fileDisplay.style.borderColor = '#00BFA5';
                    fileDisplay.style.background = '#f0fdfa';
                }
            });
        }

        // Password confirmation validation
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('cpassword');

        if (password && confirmPassword) {
            function validatePasswords() {
                if (password.value && confirmPassword.value) {
                    if (password.value !== confirmPassword.value) {
                        confirmPassword.style.borderColor = '#ef4444';
                        confirmPassword.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
                    } else {
                        confirmPassword.style.borderColor = '#10b981';
                        confirmPassword.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';
                    }
                }
            }

            password.addEventListener('input', validatePasswords);
            confirmPassword.addEventListener('input', validatePasswords);
        }
    });
</script>
{include file="sections/footer.tpl"}