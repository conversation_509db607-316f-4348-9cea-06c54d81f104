<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>{$_title} - {$_c['CompanyName']}</title>
    <link rel="shortcut icon" href="{$app_url}/ui/ui/images/logo.png" type="image/x-icon" />

    <script>
        var appUrl = '{$app_url}';
    </script>

    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/bootstrap.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/fonts/ionicons/css/ionicons.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/fonts/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/modern-AdminLTE.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/select2.min.css" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/select2-bootstrap.min.css" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/sweetalert2.min.css" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/plugins/pace.css" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/summernote/summernote.min.css" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/phpnuxbill.css?2025.2.4" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/7.css" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/modern-components.css?2025.3.13" />

    <script src="{$app_url}/ui/ui/scripts/sweetalert2.all.min.js"></script>
    <script src="{$app_url}/ui/ui/scripts/modern-dashboard.js?2025.3.13"></script>
    <script src="{$app_url}/ui/ui/scripts/mobile-table.js?2025.3.20"></script>
    <script src="{$app_url}/ui/ui/scripts/mobile-table-scroll.js?2025.3.13"></script>
    <script>
        // Set global language variable for JavaScript
        window.phpnuxbillLanguage = '{$config.language}';
    </script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.5.1/dist/chart.min.js"></script>
    <style>

    </style>
    {if isset($xheader)}
        {$xheader}
    {/if}

</head>

<body class="hold-transition modern-skin-dark sidebar-mini {if $_kolaps}sidebar-collapse{/if}">
    <div class="wrapper">
        <header class="main-header modern-header">
            <!-- Desktop Logo -->
            <a href="{Text::url('dashboard')}" class="logo desktop-logo">
                <span class="logo-mini"><b>N</b>uX</span>
                <span class="logo-lg">{$_c['CompanyName']}</span>
            </a>

            <!-- Mobile Header -->
            <div class="mobile-header">
                <a href="{Text::url('dashboard')}" class="mobile-logo">
                    <span class="mobile-logo-text">{$_c['CompanyName']}</span>
                </a>
                <div class="mobile-header-actions">
                    <button id="openSearch" class="mobile-search-btn">
                        <i class="fa fa-search"></i>
                    </button>
                    <div class="mobile-user-menu dropdown">
                        <img src="{$app_url}/{$UPLOAD_PATH}{$_admin['photo']}.thumb.jpg"
                            class="mobile-user-avatar dropdown-toggle"
                            alt="Avatar"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                            data-avatar="true"
                            data-avatar-name="{$_admin['fullname']}"
                            data-avatar-type="{$_admin['user_type']}"
                            data-avatar-size="md">

                        <!-- Mobile User Dropdown Menu -->
                        <div class="dropdown-menu mobile-user-dropdown-menu">
                            <div class="mobile-user-info">
                                <img src="{$app_url}/{$UPLOAD_PATH}{$_admin['photo']}.thumb.jpg"
                                    class="mobile-user-avatar-large"
                                    alt="Avatar"
                                    data-avatar="true"
                                    data-avatar-name="{$_admin['fullname']}"
                                    data-avatar-type="{$_admin['user_type']}"
                                    data-avatar-size="xl">
                                <div class="mobile-user-details">
                                    <div class="mobile-user-name">{$_admin['fullname']}</div>
                                    <div class="mobile-user-role">{Lang::T($_admin['user_type'])}</div>
                                </div>
                            </div>
                            <div class="mobile-user-actions">
                                <a href="{Text::url('settings/change-password')}" class="mobile-user-action">
                                    <i class="fa fa-key"></i>
                                    <span>{Lang::T('Change Password')}</span>
                                </a>
                                <a href="{Text::url('settings/users-view/', $_admin['id'])}" class="mobile-user-action">
                                    <i class="fa fa-user"></i>
                                    <span>{Lang::T('My Account')}</span>
                                </a>
                                <a href="{Text::url('logout')}" class="mobile-user-action logout">
                                    <i class="fa fa-sign-out"></i>
                                    <span>{Lang::T('Logout')}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <nav class="navbar navbar-static-top">
                <!-- Desktop Sidebar Toggle (Hidden on Mobile) -->
                <a href="#" class="sidebar-toggle desktop-sidebar-toggle" data-toggle="push-menu" role="button" onclick="return setKolaps()">
                    <span class="sr-only">Toggle navigation</span>
                </a>

                <div class="navbar-custom-menu">
                    <ul class="nav navbar-nav desktop-nav">
                        <!-- Desktop Search -->
                        <li class="desktop-search">
                            <button id="openSearchDesktop" class="search-btn">
                                <i class="fa fa-search"></i>
                            </button>
                        </li>

                        <!-- Dark Mode Toggle -->
                        <li>
                            <a class="toggle-container" href="#">
                                <i class="toggle-icon" id="toggleIcon">🌜</i>
                            </a>
                        </li>

                        <!-- Desktop User Menu -->
                        <li class="dropdown user user-menu">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <img src="{$app_url}/{$UPLOAD_PATH}{$_admin['photo']}.thumb.jpg"
                                    class="user-image"
                                    alt="Avatar"
                                    data-avatar="true"
                                    data-avatar-name="{$_admin['fullname']}"
                                    data-avatar-type="{$_admin['user_type']}"
                                    data-avatar-size="sm">
                                <span class="hidden-xs">{$_admin['fullname']}</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li class="user-header">
                                    <img src="{$app_url}/{$UPLOAD_PATH}{$_admin['photo']}.thumb.jpg"
                                        class="img-circle"
                                        alt="Avatar"
                                        data-avatar="true"
                                        data-avatar-name="{$_admin['fullname']}"
                                        data-avatar-type="{$_admin['user_type']}"
                                        data-avatar-size="lg">
                                    <p>
                                        {$_admin['fullname']}
                                        <small>{Lang::T($_admin['user_type'])}</small>
                                    </p>
                                </li>
                                <li class="user-body">
                                    <div class="row">
                                        <div class="col-xs-7 text-center text-sm">
                                            <a href="{Text::url('settings/change-password')}"><i
                                                    class="ion ion-settings"></i>
                                                {Lang::T('Change Password')}</a>
                                        </div>
                                        <div class="col-xs-5 text-center text-sm">
                                            <a href="{Text::url('settings/users-view/', $_admin['id'])}">
                                                <i class="ion ion-person"></i> {Lang::T('My Account')}</a>
                                        </div>
                                    </div>
                                </li>
                                <li class="user-footer">
                                    <div class="pull-right">
                                        <a href="{Text::url('logout')}" class="btn btn-default btn-flat"><i
                                                class="ion ion-power"></i> {Lang::T('Logout')}</a>
                                    </div>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </nav>


        </header>

        <!-- Enhanced Search Overlay -->
        <div id="searchOverlay" class="modern-search-overlay">
            <div class="modern-search-container">
                <div class="modern-search-header">
                    <h3>Search Users</h3>
                    <button type="button" id="closeSearch" class="modern-search-close">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modern-search-input-container">
                    <i class="fa fa-search modern-search-icon"></i>
                    <input type="text" id="searchTerm" class="modern-search-input"
                        placeholder="{Lang::T('Search Users')}" autocomplete="off">
                </div>
                <div id="searchResults" class="modern-search-results">
                    <!-- Search results will be displayed here -->
                </div>
            </div>
        </div>
        <aside class="main-sidebar desktop-sidebar">
            <section class="sidebar">
                <ul class="sidebar-menu" data-widget="tree">
                    <li {if $_system_menu eq 'dashboard' }class="active" {/if}>
                        <a href="{Text::url('dashboard')}">
                            <i class="ion ion-monitor"></i>
                            <span>{Lang::T('Dashboard')}</span>
                        </a>
                    </li>
                    {$_MENU_AFTER_DASHBOARD}
                    <li {if $_system_menu eq 'customers' }class="active" {/if}>
                        <a href="{Text::url('customers')}">
                            <i class="fa fa-user"></i>
                            <span>{Lang::T('Customer')}</span>
                        </a>
                    </li>
                    {$_MENU_AFTER_CUSTOMERS}
                    {if !in_array($_admin['user_type'],['Report'])}
                        <li class="{if $_routes[0] eq 'plan' || $_routes[0] eq 'coupons'}active{/if} treeview">
                            <a href="#">
                                <i class="fa fa-ticket"></i> <span>{Lang::T('Services')}</span>
                                <span class="pull-right-container">
                                    <i class="fa fa-angle-left pull-right"></i>
                                </span>
                            </a>
                            <ul class="treeview-menu">
                                <li {if $_routes[1] eq 'list' }class="active" {/if}><a
                                        href="{Text::url('plan/list')}">{Lang::T('Active Customers')}</a></li>
                                {if $_c['disable_voucher'] != 'yes'}
                                    <li {if $_routes[1] eq 'refill' }class="active" {/if}><a
                                            href="{Text::url('plan/refill')}">{Lang::T('Refill Customer')}</a></li>
                                {/if}
                                {if $_c['disable_voucher'] != 'yes'}
                                    <li {if $_routes[1] eq 'voucher' }class="active" {/if}><a
                                            href="{Text::url('plan/voucher')}">{Lang::T('Vouchers')}</a></li>
                                {/if}
                                {if $_c['enable_coupons'] == 'yes'}
                                    <li {if $_routes[0] eq 'coupons' }class="active" {/if}><a
                                            href="{Text::url('coupons')}">{Lang::T('Coupons')}</a></li>
                                {/if}
                                <li {if $_routes[1] eq 'recharge' }class="active" {/if}><a
                                        href="{Text::url('plan/recharge')}">{Lang::T('Recharge Customer')}</a></li>
                                {if $_c['enable_balance'] == 'yes'}
                                    <li {if $_routes[1] eq 'deposit' }class="active" {/if}><a
                                            href="{Text::url('plan/deposit')}">{Lang::T('Refill Balance')}</a></li>
                                {/if}
                                {$_MENU_SERVICES}
                            </ul>
                        </li>
                    {/if}
                    {$_MENU_AFTER_SERVICES}
                    {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                        <li class="{if $_system_menu eq 'services'}active{/if} treeview">
                            <a href="#">
                                <i class="ion ion-cube"></i> <span>{Lang::T('Internet Plan')}</span>
                                <span class="pull-right-container">
                                    <i class="fa fa-angle-left pull-right"></i>
                                </span>
                            </a>
                            <ul class="treeview-menu">
                                <li {if $_routes[1] eq 'hotspot' }class="active" {/if}><a
                                        href="{Text::url('services/hotspot')}"><i class="fa fa-wifi"></i> Hotspot</a></li>
                                <li {if $_routes[1] eq 'pppoe' }class="active" {/if}><a
                                        href="{Text::url('services/pppoe')}"><i class="fa fa-plug"></i> PPPOE</a></li>
                                <li {if $_routes[1] eq 'vpn' }class="active" {/if}><a href="{Text::url('services/vpn')}"><i class="fa fa-shield"></i> VPN</a>
                                </li>
                                <li {if $_routes[1] eq 'list' }class="active" {/if}><a
                                        href="{Text::url('bandwidth/list')}"><i class="fa fa-tachometer"></i> Bandwidth</a></li>
                                {if $_c['enable_balance'] == 'yes'}
                                    <li {if $_routes[1] eq 'balance' }class="active" {/if}><a
                                            href="{Text::url('services/balance')}"><i class="fa fa-credit-card"></i> {Lang::T('Customer Balance')}</a></li>
                                {/if}
                                {$_MENU_PLANS}
                            </ul>
                        </li>
                    {/if}
                    {$_MENU_AFTER_PLANS}
                    <li class="{if in_array($_routes[0], ['maps'])}active{/if} treeview">
                        <a href="#">
                            <i class="fa fa-map-marker"></i> <span>{Lang::T('Maps')}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li {if $_routes[1] eq 'customer' }class="active" {/if}><a
                                    href="{Text::url('maps/customer')}"><i class="fa fa-users"></i> {Lang::T('Customer')}</a></li>
                            <li {if $_routes[1] eq 'routers' }class="active" {/if}><a
                                    href="{Text::url('maps/routers')}"><i class="fa fa-sitemap"></i> {Lang::T('Routers')}</a></li>
                            {$_MENU_MAPS}
                        </ul>
                    </li>
                    <li class="{if $_system_menu eq 'reports'}active{/if} treeview">
                        {if in_array($_admin['user_type'],['SuperAdmin','Admin', 'Report'])}
                            <a href="#">
                                <i class="ion ion-clipboard"></i> <span>{Lang::T('Reports')}</span>
                                <span class="pull-right-container">
                                    <i class="fa fa-angle-left pull-right"></i>
                                </span>
                            </a>
                        {/if}
                        <ul class="treeview-menu">
                            <li {if $_routes[1] eq 'reports' }class="active" {/if}><a
                                    href="{Text::url('reports')}"><i class="fa fa-calendar"></i> {Lang::T('Daily Reports')}</a></li>
                            <li {if $_routes[1] eq 'activation' }class="active" {/if}><a
                                    href="{Text::url('reports/activation')}"><i class="fa fa-history"></i> {Lang::T('Activation History')}</a></li>
                            {$_MENU_REPORTS}
                        </ul>
                    </li>
                    {$_MENU_AFTER_REPORTS}
                    <li class="{if $_system_menu eq 'message'}active{/if} treeview">
                        <a href="#">
                            <i class="ion ion-android-chat"></i> <span>{Lang::T('Send Message')}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li {if $_routes[1] eq 'send' }class="active" {/if}><a
                                    href="{Text::url('message/send')}"><i class="fa fa-user"></i> {Lang::T('Single Customer')}</a></li>
                            <li {if $_routes[1] eq 'send_bulk' }class="active" {/if}><a
                                    href="{Text::url('message/send_bulk')}"><i class="fa fa-users"></i> {Lang::T('Bulk Customers')}</a></li>
                            {$_MENU_MESSAGE}
                        </ul>
                    </li>
                    {$_MENU_AFTER_MESSAGE}
                    {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                        <li class="{if $_system_menu eq 'network'}active{/if} treeview">
                            <a href="#">
                                <i class="ion ion-network"></i> <span>{Lang::T('Network')}</span>
                                <span class="pull-right-container">
                                    <i class="fa fa-angle-left pull-right"></i>
                                </span>
                            </a>
                            <ul class="treeview-menu">
                                <li {if $_routes[0] eq 'routers' and $_routes[1] eq '' }class="active" {/if}><a
                                        href="{Text::url('routers')}"><i class="fa fa-server"></i> Routers</a></li>
                                <li {if $_routes[0] eq 'pool' and $_routes[1] eq 'list' }class="active" {/if}><a
                                        href="{Text::url('pool/list')}"><i class="fa fa-list-ol"></i> IP Pool</a></li>
                                <li {if $_routes[0] eq 'pool' and $_routes[1] eq 'port' }class="active" {/if}><a
                                        href="{Text::url('pool/port')}"><i class="fa fa-plug"></i> Port Pool</a></li>
                                {$_MENU_NETWORK}
                            </ul>
                        </li>
                        {$_MENU_AFTER_NETWORKS}
                        {if $_c['radius_enable']}
                            <li class="{if $_system_menu eq 'radius'}active{/if} treeview">
                                <a href="#">
                                    <i class="fa fa-database"></i> <span>{Lang::T('Radius')}</span>
                                    <span class="pull-right-container">
                                        <i class="fa fa-angle-left pull-right"></i>
                                    </span>
                                </a>
                                <ul class="treeview-menu">
                                    <li {if $_routes[0] eq 'radius' and $_routes[1] eq 'nas-list' }class="active" {/if}><a
                                            href="{Text::url('radius/nas-list')}"><i class="fa fa-server"></i> {Lang::T('Radius NAS')}</a></li>
                                    <li {if $_routes[0] eq 'radius' and $_routes[1] eq 'data-statistics' }class="active" {/if}><a
                                            href="{Text::url('radius/data-statistics')}"><i class="fa fa-bar-chart"></i> Data Usages Statistics</a></li>
                                    {$_MENU_RADIUS}
                                </ul>
                            </li>
                        {/if}
                        {$_MENU_AFTER_RADIUS}
                        <li class="{if $_system_menu eq 'pages'}active{/if} treeview">
                            <a href="#">
                                <i class="ion ion-document"></i> <span>{Lang::T("Static Pages")}</span>
                                <span class="pull-right-container">
                                    <i class="fa fa-angle-left pull-right"></i>
                                </span>
                            </a>
                            <ul class="treeview-menu">
                                <li {if $_routes[1] eq 'Order_Voucher' }class="active" {/if}><a
                                        href="{Text::url('pages/Order_Voucher')}"><i class="fa fa-shopping-cart"></i> {Lang::T('Order Voucher')}</a></li>
                                <li {if $_routes[1] eq 'Voucher' }class="active" {/if}><a
                                        href="{Text::url('pages/Voucher')}"><i class="fa fa-paint-brush"></i> {Lang::T('Theme Voucher')}</a></li>
                                <li {if $_routes[1] eq 'Announcement' }class="active" {/if}><a
                                        href="{Text::url('pages/Announcement')}"><i class="fa fa-bullhorn"></i> {Lang::T('Announcement')}</a></li>
                                <li {if $_routes[1] eq 'Announcement_Customer' }class="active" {/if}><a
                                        href="{Text::url('pages/Announcement_Customer')}"><i class="fa fa-volume-up"></i> {Lang::T('Customer Announcement')}</a>
                                </li>
                                <li {if $_routes[1] eq 'Registration_Info' }class="active" {/if}><a
                                        href="{Text::url('pages/Registration_Info')}"><i class="fa fa-user-plus"></i> {Lang::T('Registration Info')}</a></li>
                                <li {if $_routes[1] eq 'Payment_Info' }class="active" {/if}><a
                                        href="{Text::url('pages/Payment_Info')}"><i class="fa fa-money"></i> {Lang::T('Payment Info')}</a></li>
                                <li {if $_routes[1] eq 'Privacy_Policy' }class="active" {/if}><a
                                        href="{Text::url('pages/Privacy_Policy')}"><i class="fa fa-lock"></i> {Lang::T('Privacy Policy')}</a></li>
                                <li {if $_routes[1] eq 'Terms_and_Conditions' }class="active" {/if}><a
                                        href="{Text::url('pages/Terms_and_Conditions')}"><i class="fa fa-file-text"></i> {Lang::T('Terms and Conditions')}</a></li>
                                {$_MENU_PAGES}
                            </ul>
                        </li>
                    {/if}
                    {$_MENU_AFTER_PAGES}
                    <li
                        class="{if $_system_menu eq 'settings' || $_system_menu eq 'paymentgateway' }active{/if} treeview">
                        <a href="#">
                            <i class="ion ion-gear-a"></i> <span>{Lang::T('Settings')}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                                <li {if $_routes[1] eq 'app' }class="active" {/if}><a
                                        href="{Text::url('settings/app')}"><i class="fa fa-cogs"></i> {Lang::T('General Settings')}</a></li>
                                <li {if $_routes[1] eq 'localisation' }class="active" {/if}><a
                                        href="{Text::url('settings/localisation')}"><i class="fa fa-globe"></i> {Lang::T('Localisation')}</a></li>
                                <li {if $_routes[0] eq 'customfield' }class="active" {/if}><a
                                        href="{Text::url('customfield')}"><i class="fa fa-edit"></i> {Lang::T('Custom Fields')}</a></li>
                                <li {if $_routes[1] eq 'miscellaneous' }class="active" {/if}><a
                                        href="{Text::url('settings/miscellaneous')}"><i class="fa fa-wrench"></i> {Lang::T('Miscellaneous')}</a></li>
                                <li {if $_routes[1] eq 'maintenance' }class="active" {/if}><a
                                        href="{Text::url('settings/maintenance')}"><i class="fa fa-exclamation-triangle"></i> {Lang::T('Maintenance Mode')}</a></li>
                                <li {if $_routes[0] eq 'widgets' }class="active" {/if}><a
                                            href="{Text::url('widgets')}"><i class="fa fa-th"></i> {Lang::T('Widgets')}</a></li>
                                <li {if $_routes[1] eq 'notifications' }class="active" {/if}><a
                                        href="{Text::url('settings/notifications')}"><i class="fa fa-bell"></i> {Lang::T('User Notification')}</a></li>
                                <li {if $_routes[1] eq 'devices' }class="active" {/if}><a
                                        href="{Text::url('settings/devices')}"><i class="fa fa-mobile"></i> {Lang::T('Devices')}</a></li>
                            {/if}
                            {if in_array($_admin['user_type'],['SuperAdmin','Admin','Agent'])}
                                <li {if $_routes[1] eq 'users' }class="active" {/if}><a
                                        href="{Text::url('settings/users')}"><i class="fa fa-user-secret"></i> {Lang::T('Administrator Users')}</a></li>
                            {/if}
                            {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                                <li {if $_routes[1] eq 'dbstatus' }class="active" {/if}><a
                                        href="{Text::url('settings/dbstatus')}"><i class="fa fa-database"></i> {Lang::T('Backup/Restore')}</a></li>
                                <li {if $_system_menu eq 'paymentgateway' }class="active" {/if}>
                                    <a href="{Text::url('paymentgateway')}">
                                        <i class="fa fa-credit-card"></i> <span class="text">{Lang::T('Payment Gateway')}</span>
                                    </a>
                                </li>
                                {$_MENU_SETTINGS}
                                <li {if $_routes[0] eq 'pluginmanager' }class="active" {/if}>
                                    <a href="{Text::url('pluginmanager')}"><i class="glyphicon glyphicon-tasks"></i>
                                        {Lang::T('Plugin Manager')}</a>
                                </li>
                            {/if}
                        </ul>
                    </li>
                    {$_MENU_AFTER_SETTINGS}
                    {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                        <li class="{if $_system_menu eq 'logs' }active{/if} treeview">
                            <a href="#">
                                <i class="ion ion-clock"></i> <span>{Lang::T('Logs')}</span>
                                <span class="pull-right-container">
                                    <i class="fa fa-angle-left pull-right"></i>
                                </span>
                            </a>
                            <ul class="treeview-menu">
                                <li {if $_routes[1] eq 'list' }class="active" {/if}><a
                                        href="{Text::url('logs/phpnuxbill')}"><i class="fa fa-file-code-o"></i> PhpNuxBill</a></li>
                                {if $_c['radius_enable']}
                                    <li {if $_routes[1] eq 'radius' }class="active" {/if}><a
                                            href="{Text::url('logs/radius')}"><i class="fa fa-database"></i> Radius</a>
                                    </li>
                                {/if}
                                <li {if $_routes[1] eq 'message' }class="active" {/if}><a
                                    href="{Text::url('logs/message')}"><i class="fa fa-envelope"></i> Message</a></li>
                                {$_MENU_LOGS}
                            </ul>
                        </li>
                    {/if}
                    {$_MENU_AFTER_LOGS}
                    {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                        <li {if $_routes[1] eq 'docs' }class="active" {/if}>
                            <a href="{if $_c['docs_clicked'] != 'yes'}{Text::url('settings/docs')}{else}{$app_url}/docs{/if}">
                                <i class="ion ion-ios-bookmarks"></i>
                                <span class="text">{Lang::T('Documentation')}</span>
                                {if $_c['docs_clicked'] != 'yes'}
                                    <span class="pull-right-container"><small
                                            class="label pull-right bg-green">New</small></span>
                                {/if}
                            </a>
                        </li>
                        <li {if $_system_menu eq 'community' }class="active" {/if}>
                            <a href="{Text::url('community')}">
                                <i class="ion ion-chatboxes"></i>
                                <span class="text">Community</span>
                            </a>
                        </li>
                    {/if}
                    {$_MENU_AFTER_COMMUNITY}
                </ul>
            </section>
        </aside>

        {if $_c['maintenance_mode'] == 1}
            <div class="notification-top-bar">
                <p>{Lang::T('The website is currently in maintenance mode, this means that some or all functionality may be
                unavailable to regular users during this time.')}<small> &nbsp;&nbsp;<a
                            href="{Text::url('settings/maintenance')}">{Lang::T('Turn Off')}</a></small></p>
            </div>
        {/if}

        <div class="content-wrapper">
            <section class="content-header">
                <h1>
                    {$_title}
                </h1>
            </section>

            <section class="content">
                {if isset($notify)}
                    <script>
                        // Enhanced SweetAlert toast notification
                        Swal.fire({
                            icon: '{if $notify_t == "s"}success{elseif $notify_t == "w"}warning{elseif $notify_t == "i"}info{else}error{/if}',
                            title: '{if $notify_t == "s"}Success!{elseif $notify_t == "w"}Warning!{elseif $notify_t == "i"}Information{else}Error!{/if}',
                            html: '{$notify}',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 8000,
                            timerProgressBar: true,
                            background: '{if $notify_t == "s"}#f0fdf4{elseif $notify_t == "w"}#fffbeb{elseif $notify_t == "i"}#eff6ff{else}#fef2f2{/if}',
                            color: '{if $notify_t == "s"}#166534{elseif $notify_t == "w"}#92400e{elseif $notify_t == "i"}#1e40af{else}#991b1b{/if}',
                            iconColor: '{if $notify_t == "s"}#10b981{elseif $notify_t == "w"}#f59e0b{elseif $notify_t == "i"}#3b82f6{else}#ef4444{/if}',
                            customClass: {
                                popup: 'swal-enhanced-toast',
                                title: 'swal-enhanced-title',
                                htmlContainer: 'swal-enhanced-content'
                            },
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer);
                                toast.addEventListener('mouseleave', Swal.resumeTimer);

                                // Add enhanced styling
                                const style = document.createElement('style');
                                style.textContent = `
                                    .swal-enhanced-toast {
                                        border-radius: 12px !important;
                                        border-left: 4px solid {if $notify_t == "s"}#10b981{elseif $notify_t == "w"}#f59e0b{elseif $notify_t == "i"}#3b82f6{else}#ef4444{/if} !important;
                                        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
                                        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                                    }
                                    .swal-enhanced-title {
                                        font-weight: 600 !important;
                                        font-size: 0.95rem !important;
                                    }
                                    .swal-enhanced-content {
                                        font-size: 0.9rem !important;
                                        line-height: 1.5 !important;
                                    }
                                `;
                                document.head.appendChild(style);
                            }
                        });
                    </script>
{/if}