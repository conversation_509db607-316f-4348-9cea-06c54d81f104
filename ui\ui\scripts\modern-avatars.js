/**
 * Modern Avatar System for PHPNuxBill
 * Generates beautiful profile picture placeholders
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeModernAvatars();
});

/**
 * Initialize modern avatar system
 */
function initializeModernAvatars() {
    // Find all images that need avatar fallback
    const avatarImages = document.querySelectorAll('img[data-avatar]');
    
    avatarImages.forEach(img => {
        setupAvatarFallback(img);
    });
    
    // Also handle existing onerror images
    const legacyImages = document.querySelectorAll('img[onerror*="default"]');
    legacyImages.forEach(img => {
        if (!img.hasAttribute('data-avatar')) {
            convertLegacyAvatar(img);
        }
    });
}

/**
 * Setup avatar fallback for an image
 */
function setupAvatarFallback(img) {
    const userName = img.getAttribute('data-avatar-name') || '';
    const userType = img.getAttribute('data-avatar-type') || 'customer';
    const size = img.getAttribute('data-avatar-size') || 'md';
    
    // Handle image load error
    img.onerror = function() {
        replaceWithModernAvatar(this, userName, userType, size);
    };
    
    // Check if image is already broken
    if (img.complete && img.naturalWidth === 0) {
        replaceWithModernAvatar(img, userName, userType, size);
    }
}

/**
 * Convert legacy avatar to modern system
 */
function convertLegacyAvatar(img) {
    // Extract user info from context
    const userName = extractUserName(img);
    const userType = extractUserType(img);
    const size = determineSizeFromClasses(img);
    
    // Add modern avatar attributes
    img.setAttribute('data-avatar', 'true');
    img.setAttribute('data-avatar-name', userName);
    img.setAttribute('data-avatar-type', userType);
    img.setAttribute('data-avatar-size', size);
    
    // Setup fallback
    setupAvatarFallback(img);
}

/**
 * Replace image with modern avatar placeholder
 */
function replaceWithModernAvatar(img, userName, userType, size) {
    const container = document.createElement('div');
    container.className = `modern-avatar size-${size} user-${userType.toLowerCase()}`;
    
    // Copy relevant classes from original image
    const relevantClasses = ['user-image', 'mobile-user-avatar', 'mobile-user-avatar-large', 'img-circle', 'profile-user-img'];
    relevantClasses.forEach(cls => {
        if (img.classList.contains(cls)) {
            container.classList.add(cls);
        }
    });
    
    // Copy styles
    if (img.style.width) container.style.width = img.style.width;
    if (img.style.height) container.style.height = img.style.height;
    
    // Generate content
    const initials = generateInitials(userName);
    if (initials) {
        container.innerHTML = `<span class="avatar-initials">${initials}</span>`;
    } else {
        container.innerHTML = `<i class="avatar-icon fa fa-user"></i>`;
    }
    
    // Copy event handlers
    if (img.onclick) container.onclick = img.onclick;
    if (img.getAttribute('data-toggle')) {
        container.setAttribute('data-toggle', img.getAttribute('data-toggle'));
    }
    if (img.getAttribute('aria-haspopup')) {
        container.setAttribute('aria-haspopup', img.getAttribute('aria-haspopup'));
    }
    if (img.getAttribute('aria-expanded')) {
        container.setAttribute('aria-expanded', img.getAttribute('aria-expanded'));
    }
    
    // Replace image with container
    img.parentNode.replaceChild(container, img);
}

/**
 * Generate initials from user name
 */
function generateInitials(name) {
    if (!name || name.trim() === '') return '';
    
    const words = name.trim().split(/\s+/);
    if (words.length === 1) {
        return words[0].substring(0, 2).toUpperCase();
    } else {
        return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }
}

/**
 * Extract user name from context
 */
function extractUserName(img) {
    // Try to find user name from nearby elements
    const container = img.closest('.user-header, .mobile-user-info, .box-profile, .panel-body');
    if (container) {
        const nameElement = container.querySelector('.profile-username, .mobile-user-name, h3, .user-name');
        if (nameElement) {
            return nameElement.textContent.trim();
        }
    }
    
    // Try to extract from alt attribute
    if (img.alt && img.alt !== 'Avatar' && img.alt !== 'User Image' && img.alt !== 'Foto') {
        return img.alt;
    }
    
    // Try to find from dropdown toggle text
    const dropdownToggle = img.closest('.dropdown-toggle');
    if (dropdownToggle) {
        const textNode = Array.from(dropdownToggle.childNodes)
            .find(node => node.nodeType === Node.TEXT_NODE && node.textContent.trim());
        if (textNode) {
            return textNode.textContent.trim();
        }
        
        const spanElement = dropdownToggle.querySelector('span:not(.user-balance)');
        if (spanElement) {
            return spanElement.textContent.trim();
        }
    }
    
    return '';
}

/**
 * Extract user type from context
 */
function extractUserType(img) {
    // Check URL path to determine context
    const path = window.location.pathname;
    
    if (path.includes('/admin/') || path.includes('/settings/')) {
        // Try to find user type from nearby elements
        const container = img.closest('.user-header, .mobile-user-info');
        if (container) {
            const roleElement = container.querySelector('.mobile-user-role, small');
            if (roleElement) {
                const roleText = roleElement.textContent.toLowerCase();
                if (roleText.includes('superadmin')) return 'superadmin';
                if (roleText.includes('admin')) return 'admin';
                if (roleText.includes('agent')) return 'agent';
                if (roleText.includes('report')) return 'report';
            }
        }
        return 'admin'; // Default for admin interface
    }
    
    return 'customer'; // Default for customer interface
}

/**
 * Determine size from image classes
 */
function determineSizeFromClasses(img) {
    if (img.style.width) {
        const width = parseInt(img.style.width);
        if (width >= 200) return 'profile';
        if (width >= 128) return 'xxl';
        if (width >= 96) return 'xl';
        if (width >= 64) return 'lg';
        if (width >= 48) return 'md';
        if (width >= 32) return 'sm';
        return 'xs';
    }
    
    if (img.classList.contains('mobile-user-avatar-large')) return 'xl';
    if (img.classList.contains('mobile-user-avatar')) return 'md';
    if (img.classList.contains('user-image')) return 'sm';
    if (img.classList.contains('profile-user-img')) return 'xl';
    
    // Check if it's in a profile context
    if (img.closest('.box-profile, .panel-body')) {
        return 'profile';
    }
    
    return 'md'; // Default size
}

/**
 * Create modern avatar element
 */
function createModernAvatar(userName, userType, size, additionalClasses = []) {
    const container = document.createElement('div');
    container.className = `modern-avatar size-${size} user-${userType.toLowerCase()} ${additionalClasses.join(' ')}`;
    
    const initials = generateInitials(userName);
    if (initials) {
        container.innerHTML = `<span class="avatar-initials">${initials}</span>`;
    } else {
        container.innerHTML = `<i class="avatar-icon fa fa-user"></i>`;
    }
    
    return container;
}

/**
 * Update avatar when user data changes
 */
function updateAvatar(element, userName, userType) {
    if (element.classList.contains('modern-avatar')) {
        const initials = generateInitials(userName);
        if (initials) {
            element.innerHTML = `<span class="avatar-initials">${initials}</span>`;
        } else {
            element.innerHTML = `<i class="avatar-icon fa fa-user"></i>`;
        }
        
        // Update user type class
        element.className = element.className.replace(/user-\w+/, `user-${userType.toLowerCase()}`);
    }
}

// Export functions for external use
window.ModernAvatars = {
    init: initializeModernAvatars,
    create: createModernAvatar,
    update: updateAvatar,
    generateInitials: generateInitials
};
