<?php

/**
 * Customer Data Usage Controller
 * Handles customer-side radius data usage statistics and monitoring
 */

_auth();
$ui->assign('_title', 'Internet Data Usages');
$ui->assign('_system_menu', 'data-usage');

$action = $routes['1'];
$user = User::_info();
$ui->assign('_user', $user);

if (empty($action)) {
    $action = 'dashboard';
}

switch ($action) {
    case 'dashboard':
        // Get customer's current package details
        $packages = User::_billing($user['id']);
        $ui->assign('packages', $packages);
        
        // Get customer's usage data
        $startDate = _get('start_date') ?: date('Y-m-01'); // Default to current month
        $endDate = _get('end_date') ?: date('Y-m-d');
        
        $ui->assign('start_date', $startDate);
        $ui->assign('end_date', $endDate);
        
        // Get usage statistics
        $usage = RadiusStats::getCustomerUsage($user['id'], $startDate, $endDate);
        $ui->assign('usage', $usage);
        
        // Get daily usage for chart
        $dailyUsage = getCustomerDailyUsage($user['username'], $startDate, $endDate);
        $ui->assign('daily_usage', $dailyUsage);

        // Get overall usage statistics
        $overallUsage = getCustomerOverallUsage($user['username']);
        $ui->assign('overall_usage', $overallUsage);
        
        // Get usage history table data
        $search = _post('search');
        $ui->assign('search', $search);
        
        $query = ORM::for_table('rad_acct')
            ->where('username', $user['username'])
            ->order_by_desc('dateAdded');
            
        if ($search) {
            $query->where_like('framedipaddress', '%' . $search . '%');
        }
        
        $history = Paginator::findMany($query, ['search' => $search]);
        $ui->assign('history', $history);
        
        $ui->display('customer/data-usage.tpl');
        break;
        
    case 'api-chart':
        header('Content-Type: application/json');
        $type = _get('type');
        $startDate = _get('start_date') ?: date('Y-m-01');
        $endDate = _get('end_date') ?: date('Y-m-d');
        
        switch ($type) {
            case 'daily':
                $data = getCustomerDailyUsage($user['username'], $startDate, $endDate);
                break;
            case 'overall':
                $data = getCustomerOverallUsage($user['username']);
                break;
            default:
                $data = ['labels' => [], 'datasets' => []];
        }
        
        echo json_encode($data);
        break;
        
    case 'export':
        // Export customer usage data
        $startDate = _get('start_date') ?: date('Y-m-01');
        $endDate = _get('end_date') ?: date('Y-m-d');
        
        $usage = ORM::for_table('rad_acct')
            ->where('username', $user['username'])
            ->where_gte('dateAdded', $startDate . ' 00:00:00')
            ->where_lte('dateAdded', $endDate . ' 23:59:59')
            ->order_by_desc('dateAdded')
            ->find_array();
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="data-usage-' . $user['username'] . '-' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        fputcsv($output, ['Date', 'IP Address', 'Uptime', 'Service', 'Caller ID', 'Download', 'Upload', 'Total']);
        
        foreach ($usage as $u) {
            fputcsv($output, [
                $u['dateAdded'],
                $u['framedipaddress'],
                RadiusStats::formatTime($u['acctsessiontime']),
                $u['acctstatustype'],
                $u['acctsessionid'],
                RadiusStats::formatBytes($u['acctoutputoctets']),
                RadiusStats::formatBytes($u['acctinputoctets']),
                RadiusStats::formatBytes($u['acctoutputoctets'] + $u['acctinputoctets'])
            ]);
        }
        
        fclose($output);
        break;
        
    default:
        r2(getUrl('data-usage/dashboard'), 'e', 'Invalid action');
        break;
}

/**
 * Get customer daily usage data for charts
 */
function getCustomerDailyUsage($username, $startDate, $endDate)
{
    $data = [];
    $labels = [];
    $downloads = [];
    $uploads = [];
    
    $start = new DateTime($startDate);
    $end = new DateTime($endDate);
    $interval = DateInterval::createFromDateString('1 day');
    $period = new DatePeriod($start, $interval, $end->add($interval));
    
    foreach ($period as $date) {
        $dateStr = $date->format('Y-m-d');
        $labels[] = $date->format('M j');
        
        $usage = ORM::for_table('rad_acct')
            ->where('username', $username)
            ->where_like('dateAdded', $dateStr . '%')
            ->find_array();
        
        $totalDownload = 0;
        $totalUpload = 0;
        
        foreach ($usage as $u) {
            $totalDownload += intval($u['acctoutputoctets']);
            $totalUpload += intval($u['acctinputoctets']);
        }
        
        $downloads[] = round($totalDownload / (1024 * 1024), 2); // Convert to MB
        $uploads[] = round($totalUpload / (1024 * 1024), 2); // Convert to MB
    }
    
    return [
        'labels' => $labels,
        'datasets' => [
            [
                'label' => 'Download (MB)',
                'data' => $downloads,
                'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                'borderColor' => 'rgba(54, 162, 235, 1)',
                'borderWidth' => 2,
                'fill' => true
            ],
            [
                'label' => 'Upload (MB)',
                'data' => $uploads,
                'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                'borderColor' => 'rgba(255, 99, 132, 1)',
                'borderWidth' => 2,
                'fill' => true
            ]
        ]
    ];
}

/**
 * Get customer overall usage statistics
 */
function getCustomerOverallUsage($username)
{
    // Get last 30 days usage
    $thirtyDaysAgo = date('Y-m-d', strtotime('-30 days'));
    
    $usage = ORM::for_table('rad_acct')
        ->where('username', $username)
        ->where_gte('dateAdded', $thirtyDaysAgo . ' 00:00:00')
        ->find_array();
    
    $totalDownload = 0;
    $totalUpload = 0;
    
    foreach ($usage as $u) {
        $totalDownload += intval($u['acctoutputoctets']);
        $totalUpload += intval($u['acctinputoctets']);
    }
    
    $total = $totalDownload + $totalUpload;
    
    return [
        'labels' => ['Download', 'Upload'],
        'datasets' => [
            [
                'data' => [
                    round($totalDownload / (1024 * 1024 * 1024), 2),
                    round($totalUpload / (1024 * 1024 * 1024), 2)
                ],
                'backgroundColor' => [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 99, 132, 0.8)'
                ],
                'borderColor' => [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                'borderWidth' => 2
            ]
        ]
    ];
}
