{include file="customer/header.tpl"}

<!-- Internet Data Usages Dashboard -->
<div class="container-fluid">
    
    <!-- <PERSON> Header -->
    <div class="row">
        <div class="col-md-12">
            <div class="page-header" style="margin-top: 0; padding: 20px 0; border-bottom: 2px solid #e0e0e0;">
                <h1 style="margin: 0; color: #2c3e50; font-weight: 300;">
                    <i class="fa fa-bar-chart" style="color: #3498db;"></i> 
                    Internet Data Usages
                    <small style="color: #7f8c8d;">Monitor your data consumption</small>
                </h1>
            </div>
        </div>
    </div>

    <!-- Current Package Details -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default" style="border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div class="panel-heading" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px 10px 0 0;">
                    <h4 class="panel-title">
                        <i class="fa fa-info-circle"></i> Current Package Details
                        <div class="pull-right">
                            <span class="badge" style="background: rgba(255,255,255,0.2);">
                                {if $packages}{count($packages)}{else}0{/if} Active
                            </span>
                        </div>
                    </h4>
                </div>
                <div class="panel-body">
                    {if $packages}
                        <div class="row">
                            {foreach $packages as $package}
                                <div class="col-md-6 col-sm-12">
                                    <div class="package-card" style="background: linear-gradient(135deg, #2196F3, #21CBF3); border-radius: 15px; padding: 20px; margin-bottom: 15px; color: white;">
                                        <div class="row">
                                            <div class="col-xs-8">
                                                <h4 style="margin: 0 0 10px 0; font-weight: bold;">{$package.namebp}</h4>
                                                <p style="margin: 0; opacity: 0.9;">
                                                    <i class="fa fa-calendar"></i> 
                                                    {$package.recharged_on} - {$package.expiration}
                                                </p>
                                                <p style="margin: 5px 0 0 0; opacity: 0.9;">
                                                    <i class="fa fa-server"></i> {$package.routers}
                                                </p>
                                            </div>
                                            <div class="col-xs-4 text-right">
                                                {if $package.status == 'on'}
                                                    <span class="label label-success" style="font-size: 12px; padding: 8px 12px;">
                                                        <i class="fa fa-check-circle"></i> Active
                                                    </span>
                                                {else}
                                                    <span class="label label-danger" style="font-size: 12px; padding: 8px 12px;">
                                                        <i class="fa fa-times-circle"></i> Expired
                                                    </span>
                                                {/if}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                    {else}
                        <div class="text-center" style="padding: 40px;">
                            <i class="fa fa-exclamation-triangle fa-3x text-warning"></i>
                            <h4 class="text-muted">No Active Package</h4>
                            <p class="text-muted">You don't have any active internet package.</p>
                            <a href="{Text::url('order/package')}" class="btn btn-primary">
                                <i class="fa fa-shopping-cart"></i> Buy Package
                            </a>
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Data Usages -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default" style="border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div class="panel-heading" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border-radius: 10px 10px 0 0;">
                    <h4 class="panel-title">
                        <i class="fa fa-filter"></i> Filter Data Usages
                    </h4>
                </div>
                <div class="panel-body">
                    <form method="get" action="{Text::url('data-usage/dashboard')}" class="form-inline">
                        <div class="row">
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group" style="width: 100%;">
                                    <label for="start_date">Start Date:</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{$start_date}" style="width: 100%;">
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group" style="width: 100%;">
                                    <label for="end_date">End Date:</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{$end_date}" style="width: 100%;">
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group" style="width: 100%; margin-top: 25px;">
                                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                                        <i class="fa fa-filter"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Daily Usage Chart -->
        <div class="col-lg-6 col-md-12">
            <div class="panel panel-default" style="border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div class="panel-heading" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; border-radius: 10px 10px 0 0;">
                    <h4 class="panel-title">
                        <i class="fa fa-line-chart"></i> Radius Total Daily Usages
                        <div class="pull-right">
                            <button class="btn btn-xs" style="background: rgba(255,255,255,0.2); border: none; color: white;" onclick="refreshDailyChart()">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                    </h4>
                </div>
                <div class="panel-body">
                    <canvas id="dailyChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Overall Usage Chart -->
        <div class="col-lg-6 col-md-12">
            <div class="panel panel-default" style="border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div class="panel-heading" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; border-radius: 10px 10px 0 0;">
                    <h4 class="panel-title">
                        <i class="fa fa-pie-chart"></i> Overall Data Usages
                        <div class="pull-right">
                            <button class="btn btn-xs" style="background: rgba(255,255,255,0.2); border: none; color: white;" onclick="refreshOverallChart()">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                    </h4>
                </div>
                <div class="panel-body">
                    <canvas id="overallChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage History Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default" style="border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div class="panel-heading" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; border-radius: 10px 10px 0 0;">
                    <h4 class="panel-title">
                        <i class="fa fa-table"></i> Data Usage History
                        <div class="pull-right">
                            <a href="{Text::url('data-usage/export')}?start_date={$start_date}&end_date={$end_date}" 
                               class="btn btn-xs btn-success">
                                <i class="fa fa-download"></i> Export
                            </a>
                        </div>
                    </h4>
                </div>
                <div class="panel-body">
                    
                    <!-- Search -->
                    <div class="row" style="margin-bottom: 15px;">
                        <div class="col-md-6">
                            <form method="post" action="{Text::url('data-usage/dashboard')}">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Search by IP Address..." value="{$search}">
                                    <div class="input-group-btn">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fa fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead style="background: #f8f9fa;">
                                <tr>
                                    <th><i class="fa fa-user"></i> Username</th>
                                    <th><i class="fa fa-globe"></i> Address</th>
                                    <th><i class="fa fa-clock-o"></i> Uptime</th>
                                    <th><i class="fa fa-cogs"></i> Service</th>
                                    <th><i class="fa fa-phone"></i> Caller ID</th>
                                    <th><i class="fa fa-download"></i> Download</th>
                                    <th><i class="fa fa-upload"></i> Upload</th>
                                    <th><i class="fa fa-database"></i> Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {if $history}
                                    {foreach $history as $h}
                                        <tr>
                                            <td><strong>{$h.username}</strong></td>
                                            <td><code>{$h.framedipaddress}</code></td>
                                            <td>{RadiusStats::formatTime($h.acctsessiontime)}</td>
                                            <td>
                                                {if $h.acctstatustype == 'Start'}
                                                    <span class="label label-success">Online</span>
                                                {elseif $h.acctstatustype == 'Stop'}
                                                    <span class="label label-danger">Offline</span>
                                                {else}
                                                    <span class="label label-warning">Update</span>
                                                {/if}
                                            </td>
                                            <td><small>{$h.acctsessionid}</small></td>
                                            <td class="text-success">
                                                <i class="fa fa-download"></i> 
                                                {RadiusStats::formatBytes($h.acctoutputoctets)}
                                            </td>
                                            <td class="text-danger">
                                                <i class="fa fa-upload"></i> 
                                                {RadiusStats::formatBytes($h.acctinputoctets)}
                                            </td>
                                            <td class="text-primary">
                                                <strong>{RadiusStats::formatBytes($h.acctoutputoctets + $h.acctinputoctets)}</strong>
                                            </td>
                                        </tr>
                                    {/foreach}
                                {else}
                                    <tr>
                                        <td colspan="8" class="text-center">
                                            <div style="padding: 40px;">
                                                <i class="fa fa-database fa-3x text-muted"></i>
                                                <h4 class="text-muted">No Data Available</h4>
                                                <p class="text-muted">No usage data found for the selected period.</p>
                                            </div>
                                        </td>
                                    </tr>
                                {/if}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {if $history}
                        {include file="pagination.tpl"}
                    {/if}

                </div>
            </div>
        </div>
    </div>

</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let dailyChart, overallChart;

// Initialize charts when page loads
$(document).ready(function() {
    initializeCharts();
    
    // Auto refresh every 5 minutes
    setInterval(function() {
        refreshDailyChart();
        refreshOverallChart();
    }, 300000);
});

function initializeCharts() {
    // Daily Chart
    const dailyCtx = document.getElementById('dailyChart').getContext('2d');
    dailyChart = new Chart(dailyCtx, {
        type: 'line',
        data: { labels: [], datasets: [] },
        options: getLineChartOptions('Daily Usage')
    });
    
    // Overall Chart
    const overallCtx = document.getElementById('overallChart').getContext('2d');
    overallChart = new Chart(overallCtx, {
        type: 'doughnut',
        data: { labels: [], datasets: [] },
        options: getDoughnutChartOptions('Overall Usage (Last 30 Days)')
    });
    
    // Load initial data
    refreshDailyChart();
    refreshOverallChart();
}

function getLineChartOptions(title) {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: title
            },
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Data Usage (MB)'
                }
            }
        }
    };
}

function getDoughnutChartOptions(title) {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: title
            },
            legend: {
                position: 'bottom'
            }
        }
    };
}

function refreshDailyChart() {
    const startDate = $('#start_date').val() || '{$start_date}';
    const endDate = $('#end_date').val() || '{$end_date}';
    
    $.ajax({
        url: '{Text::url("data-usage/api-chart")}?type=daily&start_date=' + startDate + '&end_date=' + endDate,
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            dailyChart.data = data;
            dailyChart.update();
        },
        error: function() {
            console.error('Failed to load daily chart data');
        }
    });
}

function refreshOverallChart() {
    $.ajax({
        url: '{Text::url("data-usage/api-chart")}?type=overall',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            overallChart.data = data;
            overallChart.update();
        },
        error: function() {
            console.error('Failed to load overall chart data');
        }
    });
}
</script>

<!-- Mobile Responsive Styles -->
<style>
@media (max-width: 768px) {
    .package-card {
        margin-bottom: 10px !important;
    }
    
    .package-card h4 {
        font-size: 16px !important;
    }
    
    .package-card p {
        font-size: 12px !important;
    }
    
    .form-inline .form-group {
        margin-bottom: 10px;
    }
    
    .table-responsive {
        font-size: 12px;
    }
    
    .table th, .table td {
        padding: 6px;
    }
    
    .panel-body canvas {
        height: 250px !important;
    }
    
    .btn-xs {
        padding: 2px 6px;
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .page-header h1 {
        font-size: 24px !important;
    }
    
    .page-header small {
        display: block;
        margin-top: 5px;
    }
    
    .package-card {
        padding: 15px !important;
    }
    
    .table-responsive {
        font-size: 10px;
    }
    
    .label {
        font-size: 9px;
        padding: 2px 4px;
    }
}

/* Custom scrollbar for mobile tables */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>

{include file="customer/footer.tpl"}
