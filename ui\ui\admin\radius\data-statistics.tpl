{include file="sections/header.tpl"}

<!-- Radius Data Usage Statistics Dashboard -->
<div class="row">
    <div class="col-sm-12">
        <div class="panel panel-hovered mb20 panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <i class="fa fa-bar-chart"></i> Radius Data Usage Statistics
                </div>
            </div>
            <div class="panel-body">
                
                <!-- Statistics Cards Row -->
                <div class="row stats-cards-row" style="margin-bottom: 30px;">
                    <!-- Online Radius Users -->
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-6">
                        <div class="modern-stats-card" style="background: linear-gradient(135deg, #4CAF50, #45a049); position: relative; overflow: hidden;">
                            <!-- Background Pattern -->
                            <div class="card-pattern" style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; z-index: 1;"></div>
                            <div class="card-pattern" style="position: absolute; bottom: -30px; left: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.05); border-radius: 50%; z-index: 1;"></div>

                            <div class="card-content" style="position: relative; z-index: 2; padding: 20px; color: white;">
                                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <div class="card-icon" style="background: rgba(255,255,255,0.2); padding: 12px; border-radius: 12px; backdrop-filter: blur(10px);">
                                        <i class="fa fa-users" style="font-size: 1.5em; color: white;"></i>
                                    </div>
                                    <div class="card-trend" style="background: rgba(255,255,255,0.15); padding: 4px 8px; border-radius: 20px; font-size: 0.75em;">
                                        <i class="fa fa-arrow-up"></i> Live
                                    </div>
                                </div>

                                <div class="card-body">
                                    <h3 style="margin: 0; font-size: 2.5em; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">{$stats.online_users}</h3>
                                    <p style="margin: 5px 0 0 0; font-size: 0.9em; opacity: 0.9; font-weight: 500;">Online Radius Users</p>
                                    <div style="margin-top: 10px; font-size: 0.8em; opacity: 0.8;">
                                        <i class="fa fa-clock-o"></i> Real-time monitoring
                                    </div>
                                </div>

                                <div class="card-footer" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                                    <a href="{Text::url('radius/data-history')}" style="color: white; text-decoration: none; font-size: 0.85em; font-weight: 500;">
                                        <i class="fa fa-external-link"></i> View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Failed Login -->
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-6">
                        <div class="modern-stats-card" style="background: linear-gradient(135deg, #FF5722, #E64A19); position: relative; overflow: hidden;">
                            <!-- Background Pattern -->
                            <div class="card-pattern" style="position: absolute; top: -15px; right: -15px; width: 90px; height: 90px; background: rgba(255,255,255,0.08); border-radius: 50%; z-index: 1;"></div>
                            <div class="card-pattern" style="position: absolute; bottom: -25px; left: -25px; width: 70px; height: 70px; background: rgba(255,255,255,0.05); border-radius: 50%; z-index: 1;"></div>

                            <div class="card-content" style="position: relative; z-index: 2; padding: 20px; color: white;">
                                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <div class="card-icon" style="background: rgba(255,255,255,0.2); padding: 12px; border-radius: 12px; backdrop-filter: blur(10px);">
                                        <i class="fa fa-exclamation-triangle" style="font-size: 1.5em; color: white;"></i>
                                    </div>
                                    <div class="card-trend" style="background: rgba(255,255,255,0.15); padding: 4px 8px; border-radius: 20px; font-size: 0.75em;">
                                        <i class="fa fa-clock-o"></i> 24h
                                    </div>
                                </div>

                                <div class="card-body">
                                    <h3 style="margin: 0; font-size: 2.5em; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">{$stats.failed_logins}</h3>
                                    <p style="margin: 5px 0 0 0; font-size: 0.9em; opacity: 0.9; font-weight: 500;">Failed Login Attempts</p>
                                    <div style="margin-top: 10px; font-size: 0.8em; opacity: 0.8;">
                                        <i class="fa fa-shield"></i> Security monitoring
                                    </div>
                                </div>

                                <div class="card-footer" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                                    <a href="{Text::url('logs/phpnuxbill')}" style="color: white; text-decoration: none; font-size: 0.85em; font-weight: 500;">
                                        <i class="fa fa-search"></i> View Logs
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Radius Status -->
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-6">
                        <div class="modern-stats-card" style="background: linear-gradient(135deg, #FF9800, #F57C00); position: relative; overflow: hidden;">
                            <!-- Background Pattern -->
                            <div class="card-pattern" style="position: absolute; top: -10px; right: -10px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%; z-index: 1;"></div>
                            <div class="card-pattern" style="position: absolute; bottom: -20px; left: -20px; width: 60px; height: 60px; background: rgba(255,255,255,0.05); border-radius: 50%; z-index: 1;"></div>

                            <div class="card-content" style="position: relative; z-index: 2; padding: 20px; color: white;">
                                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <div class="card-icon" style="background: rgba(255,255,255,0.2); padding: 12px; border-radius: 12px; backdrop-filter: blur(10px);">
                                        <i class="fa fa-server" style="font-size: 1.5em; color: white;"></i>
                                    </div>
                                    <div class="card-trend" style="background: rgba(255,255,255,0.15); padding: 4px 8px; border-radius: 20px; font-size: 0.75em;">
                                        {if $stats.radius_status == 'Online'}
                                            <i class="fa fa-circle" style="color: #4CAF50;"></i> Active
                                        {else}
                                            <i class="fa fa-circle" style="color: #F44336;"></i> Inactive
                                        {/if}
                                    </div>
                                </div>

                                <div class="card-body">
                                    <h3 style="margin: 0; font-size: 1.8em; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">{$stats.radius_status}</h3>
                                    <p style="margin: 5px 0 0 0; font-size: 0.9em; opacity: 0.9; font-weight: 500;">Radius Server Status</p>
                                    <div style="margin-top: 10px; font-size: 0.8em; opacity: 0.8;">
                                        <i class="fa fa-heartbeat"></i> Health monitoring
                                    </div>
                                </div>

                                <div class="card-footer" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                                    <a href="{Text::url('radius/nas-list')}" style="color: white; text-decoration: none; font-size: 0.85em; font-weight: 500;">
                                        <i class="fa fa-cogs"></i> Manage NAS
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Radius Accounts -->
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-6">
                        <div class="modern-stats-card" style="background: linear-gradient(135deg, #9C27B0, #7B1FA2); position: relative; overflow: hidden;">
                            <!-- Background Pattern -->
                            <div class="card-pattern" style="position: absolute; top: -25px; right: -25px; width: 110px; height: 110px; background: rgba(255,255,255,0.08); border-radius: 50%; z-index: 1;"></div>
                            <div class="card-pattern" style="position: absolute; bottom: -35px; left: -35px; width: 90px; height: 90px; background: rgba(255,255,255,0.05); border-radius: 50%; z-index: 1;"></div>

                            <div class="card-content" style="position: relative; z-index: 2; padding: 20px; color: white;">
                                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <div class="card-icon" style="background: rgba(255,255,255,0.2); padding: 12px; border-radius: 12px; backdrop-filter: blur(10px);">
                                        <i class="fa fa-user-plus" style="font-size: 1.5em; color: white;"></i>
                                    </div>
                                    <div class="card-trend" style="background: rgba(255,255,255,0.15); padding: 4px 8px; border-radius: 20px; font-size: 0.75em;">
                                        <i class="fa fa-check-circle"></i> Total
                                    </div>
                                </div>

                                <div class="card-body">
                                    <h3 style="margin: 0; font-size: 2.5em; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">{$stats.total_accounts}</h3>
                                    <p style="margin: 5px 0 0 0; font-size: 0.9em; opacity: 0.9; font-weight: 500;">Active Radius Accounts</p>
                                    <div style="margin-top: 10px; font-size: 0.8em; opacity: 0.8;">
                                        <i class="fa fa-check-circle"></i> Active subscriptions
                                    </div>
                                </div>

                                <div class="card-footer" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                                    <a href="{Text::url('customers')}" style="color: white; text-decoration: none; font-size: 0.85em; font-weight: 500;">
                                        <i class="fa fa-users"></i> Manage Users
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row">
                    <!-- Daily Usage Chart -->
                    <div class="col-lg-6 col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-line-chart"></i> Radius Total Daily Usages
                                    <div class="pull-right">
                                        <button class="btn btn-xs btn-primary" onclick="refreshChart('daily')">
                                            <i class="fa fa-refresh"></i>
                                        </button>
                                    </div>
                                </h4>
                            </div>
                            <div class="panel-body">
                                <canvas id="dailyChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Usage Chart -->
                    <div class="col-lg-6 col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-area-chart"></i> Radius Total Monthly Usages
                                    <div class="pull-right">
                                        <button class="btn btn-xs btn-primary" onclick="refreshChart('monthly')">
                                            <i class="fa fa-refresh"></i>
                                        </button>
                                    </div>
                                </h4>
                            </div>
                            <div class="panel-body">
                                <canvas id="monthlyChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Weekly Chart and Data History -->
                <div class="row">
                    <!-- Weekly Usage Chart -->
                    <div class="col-lg-6 col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-bar-chart"></i> Radius Total Weekly Usages
                                    <div class="pull-right">
                                        <button class="btn btn-xs btn-primary" onclick="refreshChart('weekly')">
                                            <i class="fa fa-refresh"></i>
                                        </button>
                                    </div>
                                </h4>
                            </div>
                            <div class="panel-body">
                                <canvas id="weeklyChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Data History -->
                    <div class="col-lg-6 col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-table"></i> Radius Users Data History
                                    <div class="pull-right">
                                        <a href="{Text::url('radius/data-history')}" class="btn btn-xs btn-success">
                                            <i class="fa fa-external-link"></i> View All
                                        </a>
                                    </div>
                                </h4>
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-condensed table-striped">
                                        <thead>
                                            <tr>
                                                <th>Username</th>
                                                <th>IP Address</th>
                                                <th>Upload</th>
                                                <th>Download</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recentHistory">
                                            <tr>
                                                <td colspan="5" class="text-center">
                                                    <i class="fa fa-spinner fa-spin"></i> Loading...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Animate.css for smooth animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<script>
let dailyChart, monthlyChart, weeklyChart;

// Initialize charts when page loads
$(document).ready(function() {
    initializeCharts();
    loadRecentHistory();
    initializeCardAnimations();

    // Auto refresh every 5 minutes
    setInterval(function() {
        refreshAllCharts();
        loadRecentHistory();
        updateCardStats();
    }, 300000);

    // Update stats every 30 seconds
    setInterval(function() {
        updateCardStats();
    }, 30000);
});

// Initialize card animations and interactions
function initializeCardAnimations() {
    // Add loading animation to cards initially
    $('.modern-stats-card').addClass('card-loading');

    // Remove loading animation after 2 seconds
    setTimeout(function() {
        $('.modern-stats-card').removeClass('card-loading');
    }, 2000);

    // Add click handlers for cards
    $('.modern-stats-card').on('click', function() {
        const cardFooterLink = $(this).find('.card-footer a');
        if (cardFooterLink.length > 0) {
            window.location.href = cardFooterLink.attr('href');
        }
    });

    // Add hover effects for better UX
    $('.modern-stats-card').hover(
        function() {
            $(this).find('.card-icon').addClass('animate__animated animate__pulse');
        },
        function() {
            $(this).find('.card-icon').removeClass('animate__animated animate__pulse');
        }
    );
}

// Update card statistics via AJAX
function updateCardStats() {
    $.ajax({
        url: '{Text::url("radius/api-stats")}?type=cards',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            // Update online users
            if (data.online_users !== undefined) {
                animateNumber($('.modern-stats-card:eq(0) .card-body h3'), data.online_users);
            }

            // Update failed logins
            if (data.failed_logins !== undefined) {
                animateNumber($('.modern-stats-card:eq(1) .card-body h3'), data.failed_logins);
            }

            // Update radius status
            if (data.radius_status !== undefined) {
                $('.modern-stats-card:eq(2) .card-body h3').text(data.radius_status);

                // Update status indicator
                const statusIndicator = $('.modern-stats-card:eq(2) .card-trend i');
                if (data.radius_status === 'Online') {
                    statusIndicator.css('color', '#4CAF50');
                    $('.modern-stats-card:eq(2) .card-trend').html('<i class="fa fa-circle" style="color: #4CAF50;"></i> Active');
                } else {
                    statusIndicator.css('color', '#F44336');
                    $('.modern-stats-card:eq(2) .card-trend').html('<i class="fa fa-circle" style="color: #F44336;"></i> Inactive');
                }
            }

            // Update total accounts
            if (data.total_accounts !== undefined) {
                animateNumber($('.modern-stats-card:eq(3) .card-body h3'), data.total_accounts);
            }
        },
        error: function() {
            console.log('Failed to update card statistics');
        }
    });
}

// Animate number changes
function animateNumber(element, newValue) {
    const currentValue = parseInt(element.text()) || 0;
    const increment = newValue > currentValue ? 1 : -1;
    const duration = 1000; // 1 second
    const steps = Math.abs(newValue - currentValue);
    const stepDuration = duration / steps;

    if (steps === 0) return;

    let current = currentValue;
    const timer = setInterval(function() {
        current += increment;
        element.text(current);

        if (current === newValue) {
            clearInterval(timer);
            // Add a flash effect
            element.addClass('animate__animated animate__flash');
            setTimeout(function() {
                element.removeClass('animate__animated animate__flash');
            }, 1000);
        }
    }, stepDuration);
}

function initializeCharts() {
    // Daily Chart
    const dailyCtx = document.getElementById('dailyChart').getContext('2d');
    dailyChart = new Chart(dailyCtx, {
        type: 'line',
        data: { labels: [], datasets: [] },
        options: getChartOptions('Daily Usage (Last 30 Days)')
    });
    
    // Monthly Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    monthlyChart = new Chart(monthlyCtx, {
        type: 'bar',
        data: { labels: [], datasets: [] },
        options: getChartOptions('Monthly Usage (Last 12 Months)')
    });
    
    // Weekly Chart
    const weeklyCtx = document.getElementById('weeklyChart').getContext('2d');
    weeklyChart = new Chart(weeklyCtx, {
        type: 'line',
        data: { labels: [], datasets: [] },
        options: getChartOptions('Weekly Usage (Last 12 Weeks)')
    });
    
    // Load initial data
    refreshAllCharts();
}

function getChartOptions(title) {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: title
            },
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Data Usage (GB)'
                }
            }
        }
    };
}

function refreshChart(type) {
    $.ajax({
        url: '{Text::url("radius/api-stats")}?type=' + type,
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            switch(type) {
                case 'daily':
                    dailyChart.data = data;
                    dailyChart.update();
                    break;
                case 'monthly':
                    monthlyChart.data = data;
                    monthlyChart.update();
                    break;
                case 'weekly':
                    weeklyChart.data = data;
                    weeklyChart.update();
                    break;
            }
        },
        error: function() {
            console.error('Failed to load ' + type + ' chart data');
        }
    });
}

function refreshAllCharts() {
    refreshChart('daily');
    refreshChart('monthly');
    refreshChart('weekly');
}

function loadRecentHistory() {
    $.ajax({
        url: '{Text::url("radius/data-history")}',
        method: 'GET',
        success: function(response) {
            // Extract table rows from response (simplified)
            $('#recentHistory').html('<tr><td colspan="5" class="text-center"><small>Recent data loaded</small></td></tr>');
        },
        error: function() {
            $('#recentHistory').html('<tr><td colspan="5" class="text-center text-danger">Failed to load recent history</td></tr>');
        }
    });
}
</script>

<!-- Modern Card & Mobile Responsive Styles -->
<style>
/* Modern Stats Cards */
.modern-stats-card {
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.modern-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
}

.card-pattern {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.card-content {
    backdrop-filter: blur(10px);
}

.card-icon {
    transition: all 0.3s ease;
}

.modern-stats-card:hover .card-icon {
    transform: scale(1.1);
    background: rgba(255,255,255,0.3) !important;
}

.card-footer a {
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.card-footer a:hover {
    transform: translateX(5px);
    opacity: 0.8;
}

/* Chart Panel Improvements */
.panel {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;
}

.panel-heading {
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

.panel-body {
    padding: 25px;
}

/* ===== ENHANCED MOBILE 2-COLUMN LAYOUT ===== */

/* Ensure consistent 2-column layout - Same as Admin Dashboard */
.stats-cards-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
}

.stats-cards-row > div {
    padding: 0 8px;
    margin-bottom: 16px;
    box-sizing: border-box;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    /* Force 2-column layout for mobile cards - Consistent with dashboard */
    .stats-cards-row {
        display: flex !important;
        flex-wrap: wrap !important;
        margin: 0 -8px !important;
    }

    .stats-cards-row > div,
    .stats-cards-row .col-lg-3,
    .stats-cards-row .col-md-6,
    .stats-cards-row .col-sm-6,
    .stats-cards-row .col-xs-6,
    .stats-cards-row .col-xs-12 {
        width: 50% !important;
        float: none !important;
        flex: 0 0 50% !important;
        max-width: 50% !important;
        padding: 0 8px !important;
        margin-bottom: 16px !important;
        display: block !important;
        box-sizing: border-box !important;
        position: relative !important;
        min-height: 1px !important;
    }

    .modern-stats-card {
        margin-bottom: 16px;
        height: auto;
        min-height: 180px;
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    }

    .card-content {
        padding: 15px !important;
    }

    .card-body h3 {
        font-size: 1.8em !important;
    }

    .card-body p {
        font-size: 0.75em !important;
    }

    .card-header {
        margin-bottom: 10px !important;
    }

    .card-icon {
        padding: 8px !important;
    }

    .card-icon i {
        font-size: 1.2em !important;
    }

    .card-trend {
        font-size: 0.65em !important;
        padding: 3px 6px !important;
    }

    .panel-body canvas {
        height: 250px !important;
    }

    .table-responsive {
        font-size: 12px;
    }

    .btn-xs {
        padding: 2px 6px;
        font-size: 10px;
    }

    .card-pattern {
        display: none; /* Hide patterns on mobile for better performance */
    }
}

@media (max-width: 480px) {
    /* Single column for very small screens - Consistent with dashboard */
    .stats-cards-row {
        margin: 0 -15px !important;
    }

    .stats-cards-row > div,
    .stats-cards-row .col-lg-3,
    .stats-cards-row .col-md-6,
    .stats-cards-row .col-sm-6,
    .stats-cards-row .col-xs-6,
    .stats-cards-row .col-xs-12 {
        width: 100% !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
        padding: 0 15px !important;
    }

    .modern-stats-card {
        min-height: 160px;
    }

    .card-content {
        padding: 12px !important;
    }

    .card-body h3 {
        font-size: 1.6em !important;
    }

    .card-body p {
        font-size: 0.7em !important;
    }

    .card-header {
        margin-bottom: 8px !important;
    }

    .card-footer {
        margin-top: 8px !important;
        padding-top: 8px !important;
    }

    .panel-title {
        font-size: 14px;
    }

    .modern-stats-card:hover {
        transform: translateY(-2px) !important; /* Subtle hover effect on mobile */
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    }

    /* Touch optimization - Consistent with dashboard */
    .modern-stats-card,
    .panel,
    .box {
        min-height: 120px !important;
        touch-action: manipulation;
    }

    /* Better mobile typography */
    .modern-stats-card h3,
    .panel-title,
    .box-title {
        font-size: 1.125rem !important;
        line-height: 1.3 !important;
    }

    /* Improve button spacing on mobile */
    .btn-group .btn {
        padding: 8px 12px !important;
        font-size: 0.875rem !important;
        margin: 2px !important;
    }
}

/* Landscape mobile orientation optimization */
@media (max-width: 768px) and (orientation: landscape) {
    .stats-cards-row {
        margin-bottom: 12px;
    }

    .modern-stats-card {
        min-height: 100px !important;
    }

    .content {
        padding: 15px 10px !important;
    }
}

/* Loading Animation */
.card-loading {
    position: relative;
    overflow: hidden;
}

.card-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: loading 2s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Pulse Animation for Numbers */
.card-body h3 {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}
</style>

{include file="sections/footer.tpl"}
