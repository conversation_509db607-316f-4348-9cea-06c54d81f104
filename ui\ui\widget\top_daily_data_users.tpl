<!-- Top 5 Daily Data Users Widget -->
<div class="modern-card" style="padding: 24px;">
    <h3 style="font-size: 1.125rem; font-weight: 600; color: #1e293b; margin-bottom: 20px;">
        <i class="fa fa-download" style="margin-right: 8px; color: #FF9800;"></i>
        {$widget_title}
        <span style="font-size: 0.875rem; font-weight: 400; color: #64748b; margin-left: 8px;">
            ({$today_date})
        </span>
    </h3>
    
    {if $top_users && count($top_users) > 0}
        <!-- Data Usage Table -->
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%); color: white;">
                        <th style="padding: 12px 16px; text-align: left; font-size: 0.875rem; font-weight: 600; border-radius: 8px 0 0 8px;">
                            USERNAME
                        </th>
                        <th style="padding: 12px 16px; text-align: center; font-size: 0.875rem; font-weight: 600; border-radius: 0 8px 8px 0;">
                            DOWNLOAD
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {foreach $top_users as $index => $user}
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 16px; vertical-align: middle;">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <!-- Rank Badge -->
                                    <div style="
                                        width: 32px;
                                        height: 32px;
                                        border-radius: 50%;
                                        background: {$user.color};
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        color: white;
                                        font-size: 0.875rem;
                                        font-weight: 600;
                                    ">
                                        {$index + 1}
                                    </div>
                                    
                                    <!-- Service Type Icon -->
                                    <div style="
                                        width: 36px;
                                        height: 36px;
                                        border-radius: 8px;
                                        background: rgba({if $user.color == '#2196F3'}33, 150, 243{elseif $user.color == '#9C27B0'}156, 39, 176{elseif $user.color == '#FF9800'}255, 152, 0{else}96, 125, 139{/if}, 0.1);
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        color: {$user.color};
                                        font-size: 1rem;
                                    ">
                                        <i class="fa {$user.icon}"></i>
                                    </div>
                                    
                                    <!-- User Info -->
                                    <div>
                                        <div style="font-size: 0.875rem; font-weight: 600; color: #1e293b;">
                                            {$user.username}
                                        </div>
                                        <div style="font-size: 0.75rem; color: #64748b;">
                                            {$user.fullname}
                                        </div>
                                        <div style="font-size: 0.75rem; color: {$user.color}; font-weight: 500;">
                                            {$user.service_type}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td style="padding: 16px; text-align: center; vertical-align: middle;">
                                <div style="font-size: 1.125rem; font-weight: 700; color: #1e293b;">
                                    {$user.formatted_usage}
                                </div>
                                
                                <!-- Progress Bar -->
                                {if $index == 0}
                                    {assign var="max_usage" value=$user.total_bytes}
                                {/if}
                                
                                {if $max_usage > 0}
                                    {assign var="percentage" value=($user.total_bytes / $max_usage * 100)}
                                {else}
                                    {assign var="percentage" value=0}
                                {/if}
                                
                                <div style="
                                    width: 100%;
                                    height: 6px;
                                    background: #e2e8f0;
                                    border-radius: 3px;
                                    overflow: hidden;
                                    margin-top: 8px;
                                ">
                                    <div style="
                                        width: {$percentage}%;
                                        height: 100%;
                                        background: {$user.color};
                                        border-radius: 3px;
                                        transition: width 0.3s ease;
                                    "></div>
                                </div>
                            </td>
                        </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>
        
        <!-- Legend -->
        <div style="
            margin-top: 20px;
            padding: 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        ">
            <div style="font-size: 0.875rem; color: #64748b; text-align: center;">
                <i class="fa fa-info-circle" style="margin-right: 6px;"></i>
                Data usage calculated from today's RADIUS accounting records
            </div>
        </div>
        
    {else}
        <!-- No Data State -->
        <div style="text-align: center; padding: 40px 20px; color: #64748b;">
            <div style="font-size: 3rem; margin-bottom: 16px; opacity: 0.3;">
                <i class="fa fa-download"></i>
            </div>
            <div style="font-size: 1rem; font-weight: 500;">
                No data usage recorded today
            </div>
            <div style="font-size: 0.875rem; margin-top: 8px;">
                User data usage will appear here once they start using the internet
            </div>
        </div>
    {/if}
</div>

<!-- Mobile Responsive Styles -->
<style>
@media (max-width: 768px) {
    .modern-card table {
        font-size: 0.875rem;
    }
    
    .modern-card th,
    .modern-card td {
        padding: 12px 8px !important;
    }
    
    .modern-card th {
        font-size: 0.75rem !important;
    }
    
    .modern-card div[style*="width: 36px"] {
        width: 32px !important;
        height: 32px !important;
        font-size: 0.875rem !important;
    }
}

@media (max-width: 480px) {
    .modern-card table {
        font-size: 0.75rem;
    }
    
    .modern-card th,
    .modern-card td {
        padding: 8px 4px !important;
    }
    
    .modern-card div[style*="width: 32px"] {
        width: 28px !important;
        height: 28px !important;
        font-size: 0.75rem !important;
    }
    
    .modern-card div[style*="width: 36px"] {
        width: 28px !important;
        height: 28px !important;
        font-size: 0.875rem !important;
    }
    
    .modern-card div[style*="font-size: 1.125rem"] {
        font-size: 1rem !important;
    }
}
</style>
