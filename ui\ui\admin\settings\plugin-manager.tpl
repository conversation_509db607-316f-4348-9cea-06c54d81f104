{include file="sections/header.tpl"}

{if empty($_c['github_token'])}
    <p class="help-block">{Lang::T('To download from private/paid repository')}, <a
            href="{Text::url('')}settings/app#GithubAuthentication">
            {Lang::T('Set your Github Authentication first')}</a></p>
{/if}

{* Server Environment & Compatibility Warnings *}
{if !empty($compatibility_warnings)}
<div class="alert alert-warning">
    <h4><i class="fa fa-exclamation-triangle"></i> Plugin Compatibility Issues</h4>
    <p>Some installed plugins may not work properly on this server environment:</p>
    <ul>
        {foreach $compatibility_warnings as $warning}
            <li>{$warning}</li>
        {/foreach}
    </ul>
    <p><small><strong>Server Info:</strong> {$server_env.os} | PHP {$server_env.php_version} | System Functions: {if $server_env.system_functions_available}Available{else}Disabled{/if}</small></p>
</div>
{/if}

{if !$server_env.system_functions_available}
<div class="alert alert-info">
    <h4><i class="fa fa-info-circle"></i> Server Environment Notice</h4>
    <p>System functions (shell_exec, exec, etc.) are disabled on this server. Some plugins requiring system access may not work properly, but compatibility layer is active to provide fallback functionality.</p>
</div>
{/if}

<form method="post" enctype="multipart/form-data"
    onsubmit="return ask(this, 'Warning, installing unknown source can damage your server, continue?')"
    action="{Text::url('')}pluginmanager/dlinstall">
    <div class="panel panel-primary panel-hovered plugin-manager-panel">
        <div class="panel-heading plugin-manager-header">
            <div class="plugin-header-content">
                <h4 class="plugin-header-title">{Lang::T('Plugin Installer')}</h4>
                <div class="plugin-header-actions">
                    <a class="btn btn-warning btn-xs plugin-info-btn" title="info"
                        href="https://github.com/hotspotbilling/phpnuxbill/wiki/Installing-Plugin-or-Payment-Gateway"
                        target="_blank">
                        <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                        <span class="hidden-xs">info</span>
                    </a>
                    <a class="btn btn-success btn-xs plugin-refresh-btn" title="refresh cache"
                        href="{Text::url('')}pluginmanager/refresh">
                        <span class="glyphicon glyphicon-refresh" aria-hidden="true"></span>
                        <span class="hidden-xs">refresh</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="panel-body plugin-installer-body">
            <div class="row plugin-installer-row">
                <div class="col-md-4 col-sm-12 plugin-upload-section">
                    <div class="form-group plugin-upload-group">
                        <label class="plugin-upload-label">{Lang::T('Upload Zip Plugin/Theme/Device')}</label>
                        <div class="plugin-file-input-wrapper">
                            <input type="file" name="zip_plugin" accept="application/zip"
                                   class="plugin-file-input" id="plugin-file-input" onchange="this.submit()">
                            <label for="plugin-file-input" class="plugin-file-label">
                                <i class="glyphicon glyphicon-cloud-upload"></i>
                                <span class="plugin-file-text">Choose ZIP file</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-7 col-sm-12 plugin-github-section">
                    <div class="form-group plugin-github-group">
                        <label class="plugin-github-label">Github URL</label>
                        <input type="url" class="form-control plugin-github-input" name="gh_url"
                            placeholder="https://github.com/username/repository">
                    </div>
                </div>
                <div class="col-md-1 col-sm-12 plugin-install-section">
                    <div class="plugin-install-button-wrapper">
                        <button type="submit" class="btn btn-primary plugin-install-btn">{Lang::T('Install')}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<div>
    <!-- Nav tabs -->
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#plugin" aria-controls="plugin" role="tab"
                data-toggle="tab">{Lang::T('Plugin')}</a></li>
        <li role="presentation"><a href="#pg" aria-controls="pg" role="tab"
                data-toggle="tab">{Lang::T('Payment Gateway')}</a>
        </li>
        <li role="presentation"><a href="#device" aria-controls="device" role="tab"
                data-toggle="tab">{Lang::T('Devices')}</a>
        </li>
    </ul>
    <br>
    <!-- Tab panes -->
    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="plugin">
            <div class="row">
                {foreach $plugins as $plugin}
                    <div class="col-md-4">
                        <div class="box box-hovered mb20 box-primary">
                            <div class="box-header">
                                <h3 class="box-title text1line">{$plugin['name']}</h3>
                            </div>
                            <div class="box-body" style="overflow-y: scroll;">
                                <div style="max-height: 50px; min-height: 50px;">{$plugin['description']}</div>
                            </div>
                            <div class="box-footer">
                                <center><small><i>@{$plugin['author']} Last update: {$plugin['last_update']}</i></small>
                                </center>
                                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                                    <a href="{$plugin['url']}" target="_blank" style="color: black;"
                                        class="btn btn-{if $plugin['ispaid']}warning{else}primary{/if}"><i
                                            class="glyphicon glyphicon-globe"></i>
                                        {if $plugin['ispaid']}Buy{else}Web{/if}</a>
                                    <a href="{$plugin['github']}" target="_blank" style="color: black;"
                                        class="btn btn-info"><i class="glyphicon glyphicon-align-left"></i> Source</a>
                                </div>
                                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                                    <a href="{Text::url('')}pluginmanager/delete/plugin/{$plugin['id']}"
                                        onclick="return ask(this, '{Lang::T('Delete')}?')" class="btn btn-danger"><i
                                            class="glyphicon glyphicon-trash"></i> Delete</a>
                                    <a {if $zipExt } href="{Text::url('')}pluginmanager/install/plugin/{$plugin['id']}"
                                            onclick="return ask(this, 'Installing plugin will take some time to complete, do not close the page while it loading to install the plugin')"
                                        {else} href="#" onclick="alert('PHP ZIP extension is not installed')"
                                        {/if}
                                        style="color: black;" class="btn btn-success"><i
                                            class="glyphicon glyphicon-circle-arrow-down"></i> Install</a>
                                </div>
                            </div>
                        </div>
                    </div>
                {/foreach}
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="pg">
            <div class="row">
                {foreach $pgs as $pg}
                    <div class="col-md-4">
                        <div class="box box-hovered mb20 box-primary">
                            <div class="box-header">
                                <h3 class="box-title text1line">{$pg['name']}</h3>
                            </div>
                            <div class="box-body" style="overflow-y: scroll;">
                                <div style="max-height: 50px; min-height: 50px;">{$pg['description']}</div>
                            </div>
                            <div class="box-footer ">
                                <center><small><i>@{$pg['author']} Last update: {$pg['last_update']}</i></small>
                                </center>
                                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                                    <a href="{$pg['url']}" target="_blank" style="color: black;"
                                        class="btn btn-{if $pg['ispaid']}warning{else}primary{/if}"><i
                                            class="glyphicon glyphicon-globe"></i>
                                        {if $pg['ispaid']}Buy{else}Web{/if}
                                    </a>
                                    <a href="{$pg['github']}" target="_blank" style="color: black;" class="btn btn-info"><i
                                            class="glyphicon glyphicon-align-left"></i> Source</a>
                                    <a {if $zipExt } href="{Text::url('')}pluginmanager/install/payment/{$pg['id']}"
                                            onclick="return ask(this, 'Installing plugin will take some time to complete, do not close the page while it loading to install the plugin')"
                                        {else} href="#" onclick="alert('PHP ZIP extension is not available')"
                                        {/if}
                                        style="color: black;" class="btn btn-success"><i
                                            class="glyphicon glyphicon-circle-arrow-down"></i> Install</a>
                                </div>
                            </div>
                        </div>
                    </div>
                {/foreach}
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="device">
            <div class="row">
                {foreach $dvcs as $dvc}
                    <div class="col-md-4">
                        <div class="box box-hovered mb20 box-primary">
                            <div class="box-header">
                                <h3 class="box-title text1line">{$dvc['name']}</h3>
                            </div>
                            <div class="box-body" style="overflow-y: scroll;">
                                <div style="max-height: 50px; min-height: 50px;">{$dvc['description']}</div>
                            </div>
                            <div class="box-footer ">
                                <center><small><i>@{$dvc['author']} Last update: {$dvc['last_update']}</i></small>
                                </center>
                                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                                    <a href="{$dvc['url']}" target="_blank" style="color: black;"
                                        class="btn btn-{if $dvc['ispaid']}warning{else}primary{/if}"><i
                                            class="glyphicon glyphicon-globe"></i>
                                        {if $dvc['ispaid']}Buy{else}Web{/if}
                                    </a>
                                    <a href="{$dvc['github']}" target="_blank" style="color: black;" class="btn btn-info"><i
                                            class="glyphicon glyphicon-align-left"></i> Source</a>
                                    <a {if $zipExt } href="{Text::url('')}pluginmanager/install/device/{$dvc['id']}"
                                            onclick="return ask(this, 'Installing plugin will take some time to complete, do not close the page while it loading to install the plugin')"
                                        {else} href="#" onclick="alert('PHP ZIP extension is not available')"
                                        {/if}
                                        style="color: black;" class="btn btn-success"><i
                                            class="glyphicon glyphicon-circle-arrow-down"></i> Install</a>
                                </div>
                            </div>
                        </div>
                    </div>
                {/foreach}
            </div>
        </div>
    </div>

</div>
{include file="sections/footer.tpl"}