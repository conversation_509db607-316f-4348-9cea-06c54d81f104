<?php

/**
 * Plugin Patcher for PHPNuxBill
 * Automatically patches plugins to be compatible with different environments
 * 
 * <AUTHOR> Enhanced
 */

class PluginPatcher
{
    /**
     * Patch all installed plugins for compatibility
     */
    public static function patchAllPlugins()
    {
        global $PLUGIN_PATH;
        
        $plugins = glob(File::pathFixer($PLUGIN_PATH . DIRECTORY_SEPARATOR . '*.php'));
        $patched = 0;
        
        foreach ($plugins as $plugin_file) {
            if (self::patchPlugin($plugin_file)) {
                $patched++;
            }
        }
        
        return $patched;
    }
    
    /**
     * Patch a specific plugin file
     */
    public static function patchPlugin($plugin_file)
    {
        if (!file_exists($plugin_file) || !is_writable($plugin_file)) {
            return false;
        }
        
        $content = file_get_contents($plugin_file);
        $original_content = $content;
        $patched = false;
        
        // Patch 1: Replace direct sys_getloadavg() calls
        if (strpos($content, 'sys_getloadavg()') !== false) {
            $content = str_replace(
                'sys_getloadavg()',
                'PluginCompatibility::getLoadAverage()',
                $content
            );
            $patched = true;
        }
        
        // Patch 2: Replace direct shell_exec() calls with safe wrapper
        if (preg_match('/shell_exec\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)/', $content)) {
            $content = preg_replace(
                '/shell_exec\s*\(\s*([\'"][^\'"]+[\'"])\s*\)/',
                'PluginCompatibility::safeShellExec($1, \'\')',
                $content
            );
            $patched = true;
        }
        
        // Patch 3: Add function_exists checks for problematic functions
        $problematic_functions = [
            'sys_getloadavg',
            'shell_exec',
            'exec',
            'system',
            'passthru'
        ];
        
        foreach ($problematic_functions as $func) {
            // Look for direct function calls without function_exists check
            $pattern = '/(?<!function_exists\([\'"])' . preg_quote($func) . '\s*\(/';
            if (preg_match($pattern, $content)) {
                // Add function_exists wrapper
                $content = preg_replace(
                    '/(' . preg_quote($func) . '\s*\([^)]*\))/',
                    '(function_exists(\'' . $func . '\') ? $1 : null)',
                    $content
                );
                $patched = true;
            }
        }
        
        // Patch 4: Add compatibility includes at the top
        if (!strpos($content, 'PluginCompatibility')) {
            $php_open = '<?php';
            if (strpos($content, $php_open) === 0) {
                $content = str_replace(
                    $php_open,
                    $php_open . "\n// Auto-patched for compatibility\nrequire_once 'PluginCompatibility.php';\n",
                    $content
                );
                $patched = true;
            }
        }
        
        // Save patched content if changes were made
        if ($patched && $content !== $original_content) {
            // Create backup
            $backup_file = $plugin_file . '.backup.' . date('Y-m-d-H-i-s');
            copy($plugin_file, $backup_file);
            
            // Save patched version
            file_put_contents($plugin_file, $content);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if a plugin needs patching
     */
    public static function needsPatching($plugin_file)
    {
        if (!file_exists($plugin_file)) {
            return false;
        }
        
        $content = file_get_contents($plugin_file);
        
        // Check for problematic patterns
        $problematic_patterns = [
            '/sys_getloadavg\s*\(\s*\)/',
            '/shell_exec\s*\([^)]+\)/',
            '/exec\s*\([^)]+\)/',
            '/system\s*\([^)]+\)/',
            '/passthru\s*\([^)]+\)/'
        ];
        
        foreach ($problematic_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get list of plugins that need patching
     */
    public static function getPluginsNeedingPatch()
    {
        global $PLUGIN_PATH;
        
        $plugins = glob(File::pathFixer($PLUGIN_PATH . DIRECTORY_SEPARATOR . '*.php'));
        $needs_patch = [];
        
        foreach ($plugins as $plugin_file) {
            if (self::needsPatching($plugin_file)) {
                $needs_patch[] = basename($plugin_file);
            }
        }
        
        return $needs_patch;
    }
    
    /**
     * Restore plugin from backup
     */
    public static function restorePlugin($plugin_file)
    {
        $backup_files = glob($plugin_file . '.backup.*');
        if (empty($backup_files)) {
            return false;
        }
        
        // Get the most recent backup
        usort($backup_files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        $latest_backup = $backup_files[0];
        return copy($latest_backup, $plugin_file);
    }
    
    /**
     * Clean up old backup files
     */
    public static function cleanupBackups($days = 7)
    {
        global $PLUGIN_PATH;
        
        $backup_files = glob(File::pathFixer($PLUGIN_PATH . DIRECTORY_SEPARATOR . '*.backup.*'));
        $cutoff_time = time() - ($days * 24 * 60 * 60);
        $cleaned = 0;
        
        foreach ($backup_files as $backup_file) {
            if (filemtime($backup_file) < $cutoff_time) {
                if (unlink($backup_file)) {
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }
}
