{include file="sections/header.tpl"}

<center><a href="https://s.id/standwithpalestine" target="_blank"><img
            src="https://raw.githubusercontent.com/Safouene1/support-palestine-banner/master/banner-support.svg"
            class="img-responsive"></a></center>
<br><br>

<div class="row">
    <div class="col-sm-6">

        <div class="box box-hovered mb20 box-primary">
            <div class="box-header">
                <h3 class="box-title">{Lang::T('Contribution')} PHPNuxBill</h3>
            </div>
            <div class="box-body">
                <a href="https://github.com/hotspotbilling/phpnuxbill/graphs/contributors" target="_blank">
                    <img src="https://contrib.rocks/image?repo=hotspotbilling/phpnuxbill&columns=10" width="100%" />
                </a>
            </div>
        </div>
    </div>
    <div class="col-sm-6">
        <div class="box box-hovered mb20 box-primary">
            <div class="box-header">
                <h3 class="box-title">{Lang::T('Discussion – Get Help from the Community')}</h3>
            </div>
            <div class="box-body">{Lang::T('Join the discussion to find solutions and support from a community ready to help.')}</div>
            <div class="box-footer">
                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                    <a href="https://github.com/hotspotbilling/phpnuxbill/discussions" target="_blank"
                        class="btn btn-primary btn-sm btn-block"><i class="ion ion-chatboxes"></i> {Lang::T('Github
                        Discussions')}</a>
                    <a href="https://t.me/phpnuxbill" target="_blank" class="btn btn-primary btn-sm btn-block"><i
                            class="ion ion-chatboxes"></i> {Lang::T('Telegram Group')}</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-6">
        <div class="box box-hovered mb20 box-primary">
            <div class="box-header">
                <h3 class="box-title">{Lang::T('Donations')} 🇮🇩</h3>
            </div>
            <div class="box-body">{Lang::T('To support further development, please donate to iBNuX. Your donation will help ensure the continued development of this application.')}</div>
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <tbody>
                        <tr>
                            <td>BCA</td>
                            <td>5410-454-825</td>
                        </tr>
                        <tr>
                            <td>Mandiri</td>
                            <td>************-793</td>
                        </tr>
                        <tr>
                            <td>Atas nama</td>
                            <td>Ibnu Maksum</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="box-footer">
                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                    <a href="https://trakteer.id/ibnux" target="_blank"
                        class="btn btn-success text-black btn-sm btn-block">Trakteer</a>
                    <a href="https://karyakarsa.com/ibnux/support" target="_blank"
                        class="btn btn-info text-black btn-sm btn-block">karyakarsa</a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6">
        <div class="box box-hovered mb20 box-primary">
            <div class="box-header">
                <h3 class="box-title">{Lang::T('Donations other than')} 🇮🇩</h3>
            </div>
            <div class="box-body">
                {Lang::T('Your donation will help support and continue the development of PHPNuxBill.')}
            </div>
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <tbody>
                        <tr>
                            <td>Bank Central Asia</td>
                            <td>5410-454-825</td>
                        </tr>
                        <tr>
                            <td>SWIFT/BIC</td>
                            <td>CENAIDJA</td>
                        </tr>
                        <tr>
                            <td>Jakarta</td>
                            <td>Indonesia</td>
                        </tr>
                        <tr>
                            <td>Account Name</td>
                            <td>Ibnu Maksum</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="box-footer">
                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                    <a href="https://paypal.me/ibnux" target="_blank"
                        class="btn btn-primary btn-sm btn-block">Paypal</a>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-6">
        <div class="box box-hovered mb20 box-primary">
            <div class="box-header">
                <h3 class="box-title">{Lang::T('Chat with Me — Paid Support $50')}</h3>
            </div>
            <div class="box-body">
                {Lang::T('Confirm your donation to continue this paid support. Or, ask about alternative donations available to suit your needs.')}
            </div>
            <div class="box-footer">
                <a href="https://t.me/ibnux" target="_blank" class="btn btn-primary btn-sm btn-block">{Lang::T('Telegram')}</a>
            </div>
        </div>
        <div class="box box-primary box-hovered mb20 activities">
            <div class="box-header">
                <h3 class="box-title">{Lang::T('WhatsApp Gateway and Free Telegram Bot')}</h3>
            </div>
            <div class="box-body">
                {Lang::T('Connect your PHPNuxBill to WhatsApp efficiently using WhatsApp Gateway. Also, create Telegram bots easily and practically.')}
            </div>
            <div class="box-footer">
                <a href="https://wa.nux.my.id/login" target="_blank"
                    class="btn btn-primary btn-sm btn-block">wa.nux.my.id</a>
            </div>
        </div>
    </div>
    <div class="col-sm-6" id="update">
        <div class="box box-primary box-hovered mb20 activities">
            <div class="box-header">
                <h3 class="box-title">PHPNUXBILL</h3>
            </div>
            <div class="box-body">
                <b>PHPNuxBill</b>
                {Lang::T('is a Hotspot and PPPoE billing platform for Mikrotik developed using PHP. The application uses Mikrotik API to communicate with the router, ensuring efficient and easy integration. If you feel you get more benefits from this application, we would greatly appreciate your contribution through donation.')}<br>{Lang::T('Watch project –')} <a
                    href="https://github.com/hotspotbilling/phpnuxbill" target="_blank">{Lang::T('IN HERE')}</a>
            </div>
            <div class="box-footer" id="currentVersion">ver</div>
            <div class="box-footer" id="latestVersion">ver</div>
            <div class="box-footer">
                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                    <a href="./update.php"
                        class="btn btn-success btn-sm btn-block">{Lang::T('Install Latest Version')}</a>
                    <a href="https://github.com/hotspotbilling/phpnuxbill/archive/refs/heads/master.zip" target="_blank"
                        class="btn btn-warning btn-sm btn-block text-black">{Lang::T('Download Latest Version')}</a>
                </div>
                <center><a href="{Text::url('community/rollback')}"
                        class="btn btn-link btn-sm btn-block">{Lang::T('Select Old Version')}</a>
                </center>
            </div>
            <div class="box-footer">
                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                    <a href="./CHANGELOG.md" target="_blank"
                        class="btn btn-default btn-sm btn-block">{Lang::T('Current Changelog')}</a>
                    <a href="https://github.com/hotspotbilling/phpnuxbill/blob/master/CHANGELOG.md" target="_blank"
                        class="btn btn-default btn-sm btn-block">{Lang::T('Repo Changelog')}</a>
                </div>
            </div>
            <div class="box-footer">
                {Lang::T('If you download the update file manually, sometimes the update may change the database structure. After the file is successfully uploaded, click this button to update the database structure.')}
                <a href="./update.php?step=4" class="btn btn-default btn-sm btn-block">{Lang::T('Update Database')}</a>
            </div>
        </div>

        <div class="box box-hovered mb20 box-primary">
    <div class="box-header">
        <h3 class="box-title">{Lang::T('Credits')}</h3>
    </div>
    <div class="box-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover">
                <thead>
                    <tr>
                        <th>{Lang::T('Souce')}</th>
                        <th>{Lang::T('Details')}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Bootstrap V3</td>
                        <td>
                            <a href="https://getbootstrap.com/docs/3.4/" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>Admin LTE V3</td>
                        <td>
                            <a href="https://adminlte.io/themes/v3/" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>Smarty Template V4</td>
                        <td>
                            <a href="https://www.smarty.net/" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>PHP IdiORM</td>
                        <td>
                            <a href="https://idiorm.readthedocs.io/" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>PHP mPDF</td>
                        <td>
                            <a href="https://mpdf.github.io/" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                   <tr>
                        <td>PHP QRCode</td>
                        <td>
                            <a href="http://phpqrcode.sourceforge.net/" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                   <tr>
                        <td>PHP Net_RouterOS</td>
                        <td>
                            <a href="https://github.com/pear2/Net_RouterOS" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                   <tr>
                        <td>Summernote</td>
                        <td>
                            <a href="https://summernote.org/" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                  <tr>
                        <td>PHP Mailer</td>
                        <td>
                            <a href="https://github.com/PHPMailer/PHPMailer/" target="_blank">
                                <i class="glyphicon glyphicon-globe"></i> {Lang::T('Visit')}
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
</div>
<script>
    window.addEventListener('DOMContentLoaded', function() {
        $.getJSON("./version.json?" + Math.random(), function(data) {
            $('#currentVersion').html('Current Version: ' + data.version);
        });
        $.getJSON("https://raw.githubusercontent.com/hotspotbilling/phpnuxbill/master/version.json?" + Math
            .random(),
            function(data) {
                $('#latestVersion').html('Latest Version: ' + data.version);
            });
    });
</script>
{include file="sections/footer.tpl"}
