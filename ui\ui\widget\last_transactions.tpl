<!-- Last 5 Transactions Widget -->
<div class="modern-card" style="background: white; border-radius: 16px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); overflow: hidden; margin-bottom: 24px;">
    <!-- Header -->
    <div class="card-header" style="background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%); color: white; padding: 20px 24px; border: none;">
        <div style="display: flex; align-items: center; gap: 12px;">
            <i class="fa fa-money" style="font-size: 1.25rem;"></i>
            <h4 style="margin: 0; font-weight: 600; font-size: 1.1rem;">{$widget_title}</h4>
        </div>
    </div>
    
    <!-- Content -->
    <div class="card-body" style="padding: 0;">
        {if isset($error_message)}
            <!-- Error Message -->
            <div style="padding: 40px 24px; text-align: center;">
                <i class="fa fa-exclamation-triangle" style="font-size: 3rem; color: #dc3545; margin-bottom: 16px;"></i>
                <h5 style="color: #dc3545; margin-bottom: 8px; font-weight: 500;">
                    {Lang::T('Error')}
                </h5>
                <p style="color: #6c757d; margin: 0; font-size: 0.875rem;">
                    {$error_message}
                </p>
            </div>
        {elseif $transactions && count($transactions) > 0}
            <!-- Table Header -->
            <div style="background: #f8f9fa; padding: 16px 24px; border-bottom: 1px solid #e9ecef;">
                <div class="row" style="margin: 0; font-weight: 600; font-size: 0.875rem; color: #495057;">
                    <div class="col-md-4" style="padding: 0;">
                        <i class="fa fa-user" style="margin-right: 8px; color: #6c757d;"></i>
                        {Lang::T('Username')}
                    </div>
                    <div class="col-md-3" style="padding: 0; text-align: center;">
                        <i class="fa fa-money" style="margin-right: 8px; color: #6c757d;"></i>
                        {Lang::T('Amount')}
                    </div>
                    <div class="col-md-3" style="padding: 0; text-align: center;">
                        <i class="fa fa-calendar" style="margin-right: 8px; color: #6c757d;"></i>
                        {Lang::T('Date')}
                    </div>
                    <div class="col-md-2" style="padding: 0; text-align: center;">
                        <i class="fa fa-credit-card" style="margin-right: 8px; color: #6c757d;"></i>
                        {Lang::T('Method')}
                    </div>
                </div>
            </div>
            
            <!-- Transaction Rows -->
            {foreach $transactions as $transaction}
                <div style="padding: 16px 24px; border-bottom: 1px solid #f1f3f4; transition: background-color 0.2s ease;" 
                     onmouseover="this.style.backgroundColor='#f8f9fa'" 
                     onmouseout="this.style.backgroundColor='transparent'">
                    <div class="row" style="margin: 0; align-items: center;">
                        <!-- Username -->
                        <div class="col-md-4" style="padding: 0;">
                            <div style="display: flex; flex-direction: column;">
                                <span style="font-weight: 600; color: #212529; font-size: 0.9rem;">
                                    {if $transaction.fullname}
                                        {$transaction.fullname}
                                    {else}
                                        {$transaction.username}
                                    {/if}
                                </span>
                                {if $transaction.fullname && $transaction.username != $transaction.fullname}
                                    <span style="font-size: 0.75rem; color: #6c757d; margin-top: 2px;">
                                        @{$transaction.username}
                                    </span>
                                {/if}
                                {if $transaction.plan_name}
                                    <span style="font-size: 0.75rem; color: #00BFA5; margin-top: 2px; font-weight: 500;">
                                        {$transaction.plan_name}
                                    </span>
                                {/if}
                            </div>
                        </div>
                        
                        <!-- Amount -->
                        <div class="col-md-3" style="padding: 0; text-align: center;">
                            <span style="font-weight: 700; color: #28a745; font-size: 0.95rem;">
                                {Lang::moneyFormat($transaction.price)}
                            </span>
                        </div>
                        
                        <!-- Date -->
                        <div class="col-md-3" style="padding: 0; text-align: center;">
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <span style="font-weight: 500; color: #495057; font-size: 0.85rem;">
                                    {$transaction.recharged_on}
                                </span>
                                <span style="font-size: 0.75rem; color: #6c757d; margin-top: 2px;">
                                    {$transaction.recharged_time}
                                </span>
                            </div>
                        </div>
                        
                        <!-- Method -->
                        <div class="col-md-2" style="padding: 0; text-align: center;">
                            <span class="label" style="
                                background: {if $transaction.method == 'Cash'}#17a2b8{elseif $transaction.method == 'Bank Transfer'}#6f42c1{elseif $transaction.method == 'PayPal'}#fd7e14{elseif $transaction.method == 'Credit Card'}#e83e8c{else}#6c757d{/if}; 
                                color: white; 
                                padding: 4px 8px; 
                                border-radius: 12px; 
                                font-size: 0.75rem; 
                                font-weight: 500;
                                display: inline-block;
                            ">
                                {$transaction.method}
                            </span>
                        </div>
                    </div>
                </div>
            {/foreach}
            
            <!-- Footer -->
            <div style="padding: 16px 24px; background: #f8f9fa; text-align: center;">
                <a href="{Text::url('reports/by-date')}" 
                   style="color: #00BFA5; text-decoration: none; font-weight: 500; font-size: 0.875rem; display: inline-flex; align-items: center; gap: 8px;"
                   onmouseover="this.style.color='#00897B'" 
                   onmouseout="this.style.color='#00BFA5'">
                    <i class="fa fa-eye"></i>
                    {Lang::T('View_All_Transactions')}
                    <i class="fa fa-arrow-right" style="font-size: 0.75rem;"></i>
                </a>
            </div>
        {else}
            <!-- No Transactions -->
            <div style="padding: 40px 24px; text-align: center;">
                <i class="fa fa-money" style="font-size: 3rem; color: #dee2e6; margin-bottom: 16px;"></i>
                <h5 style="color: #6c757d; margin-bottom: 8px; font-weight: 500;">
                    {Lang::T('No_Transactions_Found')}
                </h5>
                <p style="color: #adb5bd; margin: 0; font-size: 0.875rem;">
                    {Lang::T('Transactions_will_appear_here_when_customers_make_payments')}
                </p>
            </div>
        {/if}
    </div>
</div>
