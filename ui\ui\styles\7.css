/*! 7.css v0.17.0 - https://khang-nd.github.io/7.css */

:root {
    --surface:#f0f0f0;
    --window-spacing: 6px;
    --window-border: 1px solid;
    --window-border-radius: 6px;
    --window-border-color: rgba(0, 0, 0, .7);
    --window-background-color: #4580c4;
    --window-background-glass-stripes: linear-gradient(135deg, #fff5 70px, transparent 100px), linear-gradient(225deg, #fff5 70px, transparent 100px), linear-gradient(54deg, #0002 0 4%, #6661 6% 6%, #0002 8% 10%, #0002 15% 16%, #aaa1 17% 18%, #0002 23% 24%, #bbb2 25% 26%, #0002 31% 33%, #0002 34% 34.5%, #bbb2 36% 40%, #0002 41% 41.5%, #bbb2 44% 45%, #bbb2 46% 47%, #0002 48% 49%, #0002 50% 50.5%, #0002 56% 56.5%, #bbb2 57% 63%, #0002 67% 69%, #bbb2 69.5% 70%, #0002 73.5% 74%, #bbb2 74.5% 79%, #0002 80% 84%, #aaa2 85% 86%, #0002 87%, #bbb1 90%) left center/100vw 100vh no-repeat fixed;
    --window-background: linear-gradient(to right, #ffffff66, #0000001a, #ffffff33), var(--window-background-color);
    --control-border-color: rgba(0, 0, 0, .3);
    --control-border-radius: 5px;
    --control-inset-shadow: inset 0 0 0 1px #fffa;
    --control-background: linear-gradient(hsla(0, 0%, 100%, .5), hsla(0, 0%, 100%, .3) 45%, rgba(0, 0, 0, .1) 50%, rgba(0, 0, 0, .1) 75%, hsla(0, 0%, 100%, .5));
    --control-background-hovered: radial-gradient(circle at bottom, #2aceda, transparent 65%), linear-gradient(#b6d9ee 50%, #1a6ca1 0);
    --control-background-active: radial-gradient(circle at bottom, #0bfdfa, transparent 65%), linear-gradient(#86a7bc 50%, #092747 0);
    --control-background-close: radial-gradient(circle at -60% 50%, #0007 5% 10%, #0000 50%), radial-gradient(circle at 160% 50%, #0007 5% 10%, #0000 50%), linear-gradient(#e0a197e5, #cf796a 25% 50%, #d54f36 50%);
    --control-background-close-hovered: radial-gradient(circle at 50% 170%, #f4e676 10% 20%, #0000 60%), radial-gradient(circle at -60% 50%, #000a 5% 10%, #0000 50%), radial-gradient(circle at 160% 50%, #000a 5% 10%, #0000 50%), linear-gradient(#fb9d8b, #ee6d56 25% 50%, #d42809 50%);
    --control-background-close-active: radial-gradient(circle at 50% 170%, #dcc03f 10% 20%, #0000 60%), radial-gradient(circle at -60% 50%, #000 5% 10%, #0000 50%), radial-gradient(circle at 160% 50%, #000 5% 10%, #0000 50%), linear-gradient(#d1a894, #b67562 25% 50%, #7d0d01 50%)
}

.window {
    border: var(--window-border) var(--window-border-color);
    box-shadow: 2px 2px 10px 1px var(--window-border-color), inset 0 0 0 1px #fffa;
    font: var(--font);
    position: relative;
    z-index: 0
}

.window,
.window:before {
    border-radius: var(--window-border-radius)
}

.window:before {
    background: linear-gradient(transparent 20%, #ffffffb3 40%, transparent 41%), var(--window-background);
    background-color: var(--window-background-color);
    box-shadow: inset 0 0 0 1px #fffd;
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: -1
}

.window-body {
    color: black;
    background: var(--surface);
    border: var(--window-border) var(--window-border-color);
    box-shadow: 0 0 0 1px #fff9;
    margin: var(--window-spacing);
    margin-top: 0
}

.window-body.has-space {
    padding: var(--window-spacing)
}

.window-body pre {
    margin: calc(var(--window-spacing)*-1)
}

.window footer,
.window-footer {
    background: var(--surface);
    border: var(--window-border) var(--window-border-color);
    border-top: 0;
    box-shadow: 0 .5px 1px .5px #fff;
    margin: var(--window-spacing);
    margin-top: calc(var(--window-spacing)*-1 - 1px);
    padding: 10px;
    position: relative
}

.window footer:before,
.window-footer:before {
    box-shadow: inset 0 1px rgba(0, 0, 0, .3), inset 0 -1px #fff;
    content: "";
    height: 2px;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.window.is-bright .window-body {
    background: #fff
}

.window.is-bright .window-footer,
.window.is-bright footer {
    background: #eee;
    box-shadow: inset 0 1px 3px #ddd, 0 1px 0 #fff9, 1px 1px 0 #fff9, -1px 1px 0 #fff9
}

.window.is-bright .window-footer:before,
.window.is-bright footer:before {
    content: none
}

.window.glass:before {
    opacity: .6
}

.window.glass:after {
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    border: none;
    border-radius: var(--window-border-radius);
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: -10
}

.window.glass>.title-bar {
    background: var(--window-background-glass-stripes)
}

.window>.title-bar {
    border: 0;
    box-shadow: inset 0 1px 0 #fffd, inset 1px 0 0 #fffd, inset -1px 0 0 #fffd
}

.window fieldset {
    margin-bottom: 9px
}

.window[role=dialog] {
    left: 50%;
    opacity: 0;
    position: fixed;
    top: 50%;
    transform: translate(-50%, -50%);
    transition-duration: .2s;
    transition-property: visibility, opacity;
    visibility: hidden;
    z-index: 999
}

.window[role=dialog]:target {
    opacity: 1;
    visibility: visible
}

.title-bar {
    align-items: center;
    background: var(--window-background);
    background-color: var(--window-background-color);
    border: var(--window-border) var(--window-border-color);
    border-radius: var(--window-border-radius) var(--window-border-radius) 0 0;
    box-shadow: inset 0 0 0 1px #fff9;
    display: flex;
    font: var(--font);
    justify-content: space-between;
    padding: var(--window-spacing);
    padding-top: 0
}

.title-bar-text {
    color: #000;
    letter-spacing: 0;
    line-height: 15px;
    padding-top: var(--window-spacing);
    text-shadow: 0 0 10px #fff, 0 0 10px #fff, 0 0 10px #fff, 0 0 10px #fff, 0 0 10px #fff, 0 0 10px #fff, 0 0 10px #fff, 0 0 10px #fff
}

.title-bar-controls {
    background: #fff3;
    border: var(--window-border) var(--control-border-color);
    border-radius: 0 0 var(--control-border-radius) var(--control-border-radius);
    border-top: 0;
    box-shadow: 0 1px 0 #fffa, 1px 0 0 #fffa, -1px 0 0 #fffa;
    display: flex
}

.title-bar-controls button {
    background: none;
    border: 0;
    border-radius: 0;
    border-right: var(--window-border) var(--control-border-color);
    box-shadow: none;
    box-sizing: border-box;
    min-height: 19px;
    min-width: 29px;
    padding: 0;
    position: relative
}

.title-bar-controls button:after {
    content: none
}

.title-bar-controls button:active,
.title-bar-controls button:hover {
    background: none
}

.title-bar-controls button:disabled:before {
    opacity: .4
}

.title-bar-controls button:before {
    border-radius: 0;
    bottom: 0;
    box-shadow: inset 0 0 0 1px #fff5;
    content: "";
    left: 0;
    opacity: 1;
    position: absolute;
    right: 0;
    top: 0
}

.title-bar-controls button:not(:hover):before {
    opacity: 1;
    transition: none
}

.title-bar-controls button.is-minimize:before,
.title-bar-controls button[aria-label=Minimize]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAFCAYAAABxeg0vAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAA7SURBVHgBlY+xDQAgDMNaiQ9B8AHcxAlI/NYtKwj6QOohUzxYc+1HAqQ3e03qXNpwwcyE5QsAaEGjDRf8ZAza6Bz6VQAAAABJRU5ErkJggg==") no-repeat center 10px
}

.title-bar-controls button.is-maximize:before,
.title-bar-controls button[aria-label=Maximize]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAKCAYAAACALL/6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABsSURBVHgBlZHBCYAwDEVT6YaKbqArOILgBLqBosP1VkhAaQ6S1lKad+ihPy8kxLT9+IACG57r2KqKu2GCBpTYtEMOOQELzrnv4z53I4vDjjJnwXsPJWTOAiJGHVNB5pGwLjPk+AlEBLUY7eFebCosBHOR7vYAAAAASUVORK5CYII=") no-repeat 50%
}

.title-bar-controls button.is-help:before,
.title-bar-controls button[aria-label=Help]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAMBAMAAACtsOGuAAAAG1BMVEUAAABTVmb////39/fa2trm5ubj4+Pq6urg4OByvnYHAAAAAXRSTlMAQObYZgAAADtJREFUCNdjYBQUFGAQVFISZBBSUlJkEDYUNmQQFBAvZGBgDA1kYBBLE2RgkGgUYGAQBDIYRBwhBJgLAKBIBQ2X+PZ0AAAAAElFTkSuQmCC") no-repeat 50%
}

.title-bar-controls button.is-restore:before,
.title-bar-controls button[aria-label=Restore]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACVSURBVHgBlVDLCoMwENwU/7ClvRYK7b2lCIJHEQQ/QEHwqujfBXJIQMnihg34iHPJ7GNm2AhYcL2/JwhAxIuhq3aXb48PiFBna2YFERVHzoQLnAQmSCmxeL5+3rBtSsdpBwVKKTcY+1rY197G+8RRoLVejed94igwxrgB/zXeJ+4Jijz2Erjg+8/WE7ZAt6EgSUsIxQwaJEWv7G6SSgAAAABJRU5ErkJggg==") no-repeat 50%
}

.title-bar-controls button.is-close,
.title-bar-controls button[aria-label=Close] {
    min-width: 48px
}

.title-bar-controls button.is-close:before,
.title-bar-controls button[aria-label=Close]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAKCAYAAABi8KSDAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACUSURBVHgBlZFNCoQwDIVbmRvOMHOAAd0K/uDGlYKiCG4VPICi9yu0oCSQoFUXFtKEl6+voZXvr7sKay1TL690FOexY+Hz87i2decOuDKQsNGVe8AGYTRJIh0Y+orBvxszCPllOymlxN06jNE26Qnwg4zdHQLrMhFaawwAqAadDPE1ijwUxhiMKCmxCZk06OMYTz5lAx3xWDeXkLNAAAAAAElFTkSuQmCC") no-repeat 50%
}

.title-bar-controls button:first-child,
.title-bar-controls button:first-child:before {
    border-bottom-left-radius: var(--control-border-radius)
}

.title-bar-controls button:last-child,
.title-bar-controls button:last-child:before {
    border: 0;
    border-bottom-right-radius: var(--control-border-radius)
}

.title-bar-controls button:focus {
    -webkit-animation: none;
    animation: none;
    outline: none
}

.title-bar.active .title-bar-controls,
.window.active .title-bar .title-bar-controls {
    border-color: var(--window-border-color)
}

.title-bar.active .title-bar-controls button,
.window.active .title-bar .title-bar-controls button {
    border-color: var(--window-border-color);
    box-shadow: var(--control-inset-shadow)
}

.title-bar.active .title-bar-controls button:after,
.window.active .title-bar .title-bar-controls button:after {
    content: none
}

.title-bar.active .title-bar-controls button.is-minimize,
.title-bar.active .title-bar-controls button[aria-label=Minimize],
.window.active .title-bar .title-bar-controls button.is-minimize,
.window.active .title-bar .title-bar-controls button[aria-label=Minimize] {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAFCAYAAABxeg0vAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAA7SURBVHgBlY+xDQAgDMNaiQ9B8AHcxAlI/NYtKwj6QOohUzxYc+1HAqQ3e03qXNpwwcyE5QsAaEGjDRf8ZAza6Bz6VQAAAABJRU5ErkJggg==") no-repeat center 10px, var(--control-background)
}

.title-bar.active .title-bar-controls button.is-minimize:before,
.title-bar.active .title-bar-controls button[aria-label=Minimize]:before,
.window.active .title-bar .title-bar-controls button.is-minimize:before,
.window.active .title-bar .title-bar-controls button[aria-label=Minimize]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAFCAYAAABxeg0vAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAA7SURBVHgBlY+xDQAgDMNaiQ9B8AHcxAlI/NYtKwj6QOohUzxYc+1HAqQ3e03qXNpwwcyE5QsAaEGjDRf8ZAza6Bz6VQAAAABJRU5ErkJggg==") no-repeat center 10px, var(--control-background-hovered);
    border-bottom-left-radius: var(--control-border-radius);
    box-shadow: 0 0 7px 3px #5dc4f0, var(--control-inset-shadow);
    content: "";
    opacity: 0;
    transition: opacity .3s linear
}

.title-bar.active .title-bar-controls button.is-minimize:focus-visible:before,
.title-bar.active .title-bar-controls button.is-minimize:hover:before,
.title-bar.active .title-bar-controls button[aria-label=Minimize]:focus-visible:before,
.title-bar.active .title-bar-controls button[aria-label=Minimize]:hover:before,
.window.active .title-bar .title-bar-controls button.is-minimize:focus-visible:before,
.window.active .title-bar .title-bar-controls button.is-minimize:hover:before,
.window.active .title-bar .title-bar-controls button[aria-label=Minimize]:focus-visible:before,
.window.active .title-bar .title-bar-controls button[aria-label=Minimize]:hover:before {
    opacity: 1;
    transition: opacity .1s linear
}

.title-bar.active .title-bar-controls button.is-minimize:active:before,
.title-bar.active .title-bar-controls button[aria-label=Minimize]:active:before,
.window.active .title-bar .title-bar-controls button.is-minimize:active:before,
.window.active .title-bar .title-bar-controls button[aria-label=Minimize]:active:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAFCAYAAABxeg0vAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAA7SURBVHgBlY+xDQAgDMNaiQ9B8AHcxAlI/NYtKwj6QOohUzxYc+1HAqQ3e03qXNpwwcyE5QsAaEGjDRf8ZAza6Bz6VQAAAABJRU5ErkJggg==") no-repeat center 10px, var(--control-background-active)
}

.title-bar.active .title-bar-controls button.is-maximize,
.title-bar.active .title-bar-controls button[aria-label=Maximize],
.window.active .title-bar .title-bar-controls button.is-maximize,
.window.active .title-bar .title-bar-controls button[aria-label=Maximize] {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAKCAYAAACALL/6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABsSURBVHgBlZHBCYAwDEVT6YaKbqArOILgBLqBosP1VkhAaQ6S1lKad+ihPy8kxLT9+IACG57r2KqKu2GCBpTYtEMOOQELzrnv4z53I4vDjjJnwXsPJWTOAiJGHVNB5pGwLjPk+AlEBLUY7eFebCosBHOR7vYAAAAASUVORK5CYII=") no-repeat 50%, var(--control-background)
}

.title-bar.active .title-bar-controls button.is-maximize:before,
.title-bar.active .title-bar-controls button[aria-label=Maximize]:before,
.window.active .title-bar .title-bar-controls button.is-maximize:before,
.window.active .title-bar .title-bar-controls button[aria-label=Maximize]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAKCAYAAACALL/6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABsSURBVHgBlZHBCYAwDEVT6YaKbqArOILgBLqBosP1VkhAaQ6S1lKad+ihPy8kxLT9+IACG57r2KqKu2GCBpTYtEMOOQELzrnv4z53I4vDjjJnwXsPJWTOAiJGHVNB5pGwLjPk+AlEBLUY7eFebCosBHOR7vYAAAAASUVORK5CYII=") no-repeat 50%, var(--control-background-hovered);
    box-shadow: 0 0 7px 3px #5dc4f0, var(--control-inset-shadow);
    content: "";
    opacity: 0;
    transition: opacity .3s linear
}

.title-bar.active .title-bar-controls button.is-maximize:focus-visible:before,
.title-bar.active .title-bar-controls button.is-maximize:hover:before,
.title-bar.active .title-bar-controls button[aria-label=Maximize]:focus-visible:before,
.title-bar.active .title-bar-controls button[aria-label=Maximize]:hover:before,
.window.active .title-bar .title-bar-controls button.is-maximize:focus-visible:before,
.window.active .title-bar .title-bar-controls button.is-maximize:hover:before,
.window.active .title-bar .title-bar-controls button[aria-label=Maximize]:focus-visible:before,
.window.active .title-bar .title-bar-controls button[aria-label=Maximize]:hover:before {
    opacity: 1;
    transition: opacity .1s linear
}

.title-bar.active .title-bar-controls button.is-maximize:active:before,
.title-bar.active .title-bar-controls button[aria-label=Maximize]:active:before,
.window.active .title-bar .title-bar-controls button.is-maximize:active:before,
.window.active .title-bar .title-bar-controls button[aria-label=Maximize]:active:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAKCAYAAACALL/6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABsSURBVHgBlZHBCYAwDEVT6YaKbqArOILgBLqBosP1VkhAaQ6S1lKad+ihPy8kxLT9+IACG57r2KqKu2GCBpTYtEMOOQELzrnv4z53I4vDjjJnwXsPJWTOAiJGHVNB5pGwLjPk+AlEBLUY7eFebCosBHOR7vYAAAAASUVORK5CYII=") no-repeat 50%, var(--control-background-active)
}

.title-bar.active .title-bar-controls button.is-help,
.title-bar.active .title-bar-controls button[aria-label=Help],
.window.active .title-bar .title-bar-controls button.is-help,
.window.active .title-bar .title-bar-controls button[aria-label=Help] {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAMBAMAAACtsOGuAAAAG1BMVEUAAABTVmb////39/fa2trm5ubj4+Pq6urg4OByvnYHAAAAAXRSTlMAQObYZgAAADtJREFUCNdjYBQUFGAQVFISZBBSUlJkEDYUNmQQFBAvZGBgDA1kYBBLE2RgkGgUYGAQBDIYRBwhBJgLAKBIBQ2X+PZ0AAAAAElFTkSuQmCC") no-repeat 50%, var(--control-background)
}

.title-bar.active .title-bar-controls button.is-help:before,
.title-bar.active .title-bar-controls button[aria-label=Help]:before,
.window.active .title-bar .title-bar-controls button.is-help:before,
.window.active .title-bar .title-bar-controls button[aria-label=Help]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAMBAMAAACtsOGuAAAAG1BMVEUAAABTVmb////39/fa2trm5ubj4+Pq6urg4OByvnYHAAAAAXRSTlMAQObYZgAAADtJREFUCNdjYBQUFGAQVFISZBBSUlJkEDYUNmQQFBAvZGBgDA1kYBBLE2RgkGgUYGAQBDIYRBwhBJgLAKBIBQ2X+PZ0AAAAAElFTkSuQmCC") no-repeat 50%, var(--control-background-hovered);
    box-shadow: 0 0 7px 3px #5dc4f0, var(--control-inset-shadow);
    content: "";
    opacity: 0;
    transition: opacity .3s linear
}

.title-bar.active .title-bar-controls button.is-help:focus-visible:before,
.title-bar.active .title-bar-controls button.is-help:hover:before,
.title-bar.active .title-bar-controls button[aria-label=Help]:focus-visible:before,
.title-bar.active .title-bar-controls button[aria-label=Help]:hover:before,
.window.active .title-bar .title-bar-controls button.is-help:focus-visible:before,
.window.active .title-bar .title-bar-controls button.is-help:hover:before,
.window.active .title-bar .title-bar-controls button[aria-label=Help]:focus-visible:before,
.window.active .title-bar .title-bar-controls button[aria-label=Help]:hover:before {
    opacity: 1;
    transition: opacity .1s linear
}

.title-bar.active .title-bar-controls button.is-help:active:before,
.title-bar.active .title-bar-controls button[aria-label=Help]:active:before,
.window.active .title-bar .title-bar-controls button.is-help:active:before,
.window.active .title-bar .title-bar-controls button[aria-label=Help]:active:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAMBAMAAACtsOGuAAAAG1BMVEUAAABTVmb////39/fa2trm5ubj4+Pq6urg4OByvnYHAAAAAXRSTlMAQObYZgAAADtJREFUCNdjYBQUFGAQVFISZBBSUlJkEDYUNmQQFBAvZGBgDA1kYBBLE2RgkGgUYGAQBDIYRBwhBJgLAKBIBQ2X+PZ0AAAAAElFTkSuQmCC") no-repeat 50%, var(--control-background-active)
}

.title-bar.active .title-bar-controls button.is-restore,
.title-bar.active .title-bar-controls button[aria-label=Restore],
.window.active .title-bar .title-bar-controls button.is-restore,
.window.active .title-bar .title-bar-controls button[aria-label=Restore] {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACVSURBVHgBlVDLCoMwENwU/7ClvRYK7b2lCIJHEQQ/QEHwqujfBXJIQMnihg34iHPJ7GNm2AhYcL2/JwhAxIuhq3aXb48PiFBna2YFERVHzoQLnAQmSCmxeL5+3rBtSsdpBwVKKTcY+1rY197G+8RRoLVejed94igwxrgB/zXeJ+4Jijz2Erjg+8/WE7ZAt6EgSUsIxQwaJEWv7G6SSgAAAABJRU5ErkJggg==") no-repeat 50%, var(--control-background)
}

.title-bar.active .title-bar-controls button.is-restore:before,
.title-bar.active .title-bar-controls button[aria-label=Restore]:before,
.window.active .title-bar .title-bar-controls button.is-restore:before,
.window.active .title-bar .title-bar-controls button[aria-label=Restore]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACVSURBVHgBlVDLCoMwENwU/7ClvRYK7b2lCIJHEQQ/QEHwqujfBXJIQMnihg34iHPJ7GNm2AhYcL2/JwhAxIuhq3aXb48PiFBna2YFERVHzoQLnAQmSCmxeL5+3rBtSsdpBwVKKTcY+1rY197G+8RRoLVejed94igwxrgB/zXeJ+4Jijz2Erjg+8/WE7ZAt6EgSUsIxQwaJEWv7G6SSgAAAABJRU5ErkJggg==") no-repeat 50%, var(--control-background-hovered);
    box-shadow: 0 0 7px 3px #5dc4f0, var(--control-inset-shadow);
    content: "";
    opacity: 0;
    transition: opacity .3s linear
}

.title-bar.active .title-bar-controls button.is-restore:focus-visible:before,
.title-bar.active .title-bar-controls button.is-restore:hover:before,
.title-bar.active .title-bar-controls button[aria-label=Restore]:focus-visible:before,
.title-bar.active .title-bar-controls button[aria-label=Restore]:hover:before,
.window.active .title-bar .title-bar-controls button.is-restore:focus-visible:before,
.window.active .title-bar .title-bar-controls button.is-restore:hover:before,
.window.active .title-bar .title-bar-controls button[aria-label=Restore]:focus-visible:before,
.window.active .title-bar .title-bar-controls button[aria-label=Restore]:hover:before {
    opacity: 1;
    transition: opacity .1s linear
}

.title-bar.active .title-bar-controls button.is-restore:active:before,
.title-bar.active .title-bar-controls button[aria-label=Restore]:active:before,
.window.active .title-bar .title-bar-controls button.is-restore:active:before,
.window.active .title-bar .title-bar-controls button[aria-label=Restore]:active:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACVSURBVHgBlVDLCoMwENwU/7ClvRYK7b2lCIJHEQQ/QEHwqujfBXJIQMnihg34iHPJ7GNm2AhYcL2/JwhAxIuhq3aXb48PiFBna2YFERVHzoQLnAQmSCmxeL5+3rBtSsdpBwVKKTcY+1rY197G+8RRoLVejed94igwxrgB/zXeJ+4Jijz2Erjg+8/WE7ZAt6EgSUsIxQwaJEWv7G6SSgAAAABJRU5ErkJggg==") no-repeat 50%, var(--control-background-active)
}

.title-bar.active .title-bar-controls button.is-close,
.title-bar.active .title-bar-controls button[aria-label=Close],
.window.active .title-bar .title-bar-controls button.is-close,
.window.active .title-bar .title-bar-controls button[aria-label=Close] {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAKCAYAAABi8KSDAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACUSURBVHgBlZFNCoQwDIVbmRvOMHOAAd0K/uDGlYKiCG4VPICi9yu0oCSQoFUXFtKEl6+voZXvr7sKay1TL690FOexY+Hz87i2decOuDKQsNGVe8AGYTRJIh0Y+orBvxszCPllOymlxN06jNE26Qnwg4zdHQLrMhFaawwAqAadDPE1ijwUxhiMKCmxCZk06OMYTz5lAx3xWDeXkLNAAAAAAElFTkSuQmCC") no-repeat 50%, var(--control-background), var(--control-background-close);
    box-shadow: var(--control-inset-shadow)
}

.title-bar.active .title-bar-controls button.is-close:before,
.title-bar.active .title-bar-controls button[aria-label=Close]:before,
.window.active .title-bar .title-bar-controls button.is-close:before,
.window.active .title-bar .title-bar-controls button[aria-label=Close]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAKCAYAAABi8KSDAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACUSURBVHgBlZFNCoQwDIVbmRvOMHOAAd0K/uDGlYKiCG4VPICi9yu0oCSQoFUXFtKEl6+voZXvr7sKay1TL690FOexY+Hz87i2decOuDKQsNGVe8AGYTRJIh0Y+orBvxszCPllOymlxN06jNE26Qnwg4zdHQLrMhFaawwAqAadDPE1ijwUxhiMKCmxCZk06OMYTz5lAx3xWDeXkLNAAAAAAElFTkSuQmCC") no-repeat 50%, var(--control-background), var(--control-background-close-hovered);
    border-bottom-right-radius: var(--control-border-radius);
    box-shadow: 0 0 7px 3px #e68e75, var(--control-inset-shadow);
    content: "";
    opacity: 0;
    transition: opacity .3s linear
}

.title-bar.active .title-bar-controls button.is-close:focus-visible:before,
.title-bar.active .title-bar-controls button.is-close:hover:before,
.title-bar.active .title-bar-controls button[aria-label=Close]:focus-visible:before,
.title-bar.active .title-bar-controls button[aria-label=Close]:hover:before,
.window.active .title-bar .title-bar-controls button.is-close:focus-visible:before,
.window.active .title-bar .title-bar-controls button.is-close:hover:before,
.window.active .title-bar .title-bar-controls button[aria-label=Close]:focus-visible:before,
.window.active .title-bar .title-bar-controls button[aria-label=Close]:hover:before {
    opacity: 1;
    transition: opacity .1s linear
}

.title-bar.active .title-bar-controls button.is-close:active:before,
.title-bar.active .title-bar-controls button[aria-label=Close]:active:before,
.window.active .title-bar .title-bar-controls button.is-close:active:before,
.window.active .title-bar .title-bar-controls button[aria-label=Close]:active:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAKCAYAAABi8KSDAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACUSURBVHgBlZFNCoQwDIVbmRvOMHOAAd0K/uDGlYKiCG4VPICi9yu0oCSQoFUXFtKEl6+voZXvr7sKay1TL690FOexY+Hz87i2decOuDKQsNGVe8AGYTRJIh0Y+orBvxszCPllOymlxN06jNE26Qnwg4zdHQLrMhFaawwAqAadDPE1ijwUxhiMKCmxCZk06OMYTz5lAx3xWDeXkLNAAAAAAElFTkSuQmCC") no-repeat 50%, var(--control-background), var(--control-background-close-active)
}

.title-bar.active .title-bar-controls button:disabled.is-maximize,
.title-bar.active .title-bar-controls button:disabled[aria-label=Maximize],
.window.active .title-bar .title-bar-controls button:disabled.is-maximize,
.window.active .title-bar .title-bar-controls button:disabled[aria-label=Maximize] {
    background: var(--control-background)
}

.title-bar.active .title-bar-controls button:disabled.is-maximize:before,
.title-bar.active .title-bar-controls button:disabled[aria-label=Maximize]:before,
.window.active .title-bar .title-bar-controls button:disabled.is-maximize:before,
.window.active .title-bar .title-bar-controls button:disabled[aria-label=Maximize]:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAKCAYAAACALL/6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABsSURBVHgBlZHBCYAwDEVT6YaKbqArOILgBLqBosP1VkhAaQ6S1lKad+ihPy8kxLT9+IACG57r2KqKu2GCBpTYtEMOOQELzrnv4z53I4vDjjJnwXsPJWTOAiJGHVNB5pGwLjPk+AlEBLUY7eFebCosBHOR7vYAAAAASUVORK5CYII=") no-repeat 50%;
    content: "";
    opacity: .4
}

.status-bar {
    color: black;
    background: var(--surface);
    border: var(--window-border) var(--window-border-color);
    border-top: 0;
    box-shadow: 0 1px 0 #fff9, 1px 0 0 #fff9, -1px 0 0 #fff9;
    display: flex;
    margin: var(--window-spacing);
    margin-top: calc(var(--window-spacing)*-1)
}

.status-bar-field {
    border-right: var(--window-border) #cfcfcf;
    flex-grow: 1;
    margin: 0;
    padding: 2px 3px
}

.status-bar-field:last-child {
    border-right: 0
}

/*# sourceMappingURL=7.css.map */