<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>{$_title} - {$_c['CompanyName']}</title>
    <link rel="shortcut icon" href="{$app_url}/ui/ui/images/logo.png" type="image/x-icon" />

    <script>
        var appUrl = '{$app_url}';
    </script>

    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/bootstrap.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/modern-AdminLTE.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/sweetalert2.min.css" />
    <script src="{$app_url}/ui/ui/scripts/sweetalert2.all.min.js"></script>
    <script>
        // Set global language variable for JavaScript
        window.phpnuxbillLanguage = '{$config.language}';
    </script>



</head>

<body id="app" class="app off-canvas body-full">
    <div class="container">
        <div class="form-head mb20">
            <h1 class="site-logo h2 mb5 mt5 text-center text-uppercase text-bold"
                style="text-shadow: 2px 2px 4px #757575;">{$_c['CompanyName']}</h1>
            <hr>
        </div>
        {if isset($notify)}
            <script>
                // Enhanced SweetAlert toast notification
                Swal.fire({
                    icon: '{if $notify_t == "s"}success{elseif $notify_t == "w"}warning{elseif $notify_t == "i"}info{else}error{/if}',
                    title: '{if $notify_t == "s"}Success!{elseif $notify_t == "w"}Warning!{elseif $notify_t == "i"}Information{else}Error!{/if}',
                    html: '{$notify}',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 8000,
                    timerProgressBar: true,
                    background: '{if $notify_t == "s"}#f0fdf4{elseif $notify_t == "w"}#fffbeb{elseif $notify_t == "i"}#eff6ff{else}#fef2f2{/if}',
                    color: '{if $notify_t == "s"}#166534{elseif $notify_t == "w"}#92400e{elseif $notify_t == "i"}#1e40af{else}#991b1b{/if}',
                    iconColor: '{if $notify_t == "s"}#10b981{elseif $notify_t == "w"}#f59e0b{elseif $notify_t == "i"}#3b82f6{else}#ef4444{/if}',
                    customClass: {
                        popup: 'swal-enhanced-toast',
                        title: 'swal-enhanced-title',
                        htmlContainer: 'swal-enhanced-content'
                    },
                    didOpen: (toast) => {
                        toast.addEventListener('mouseenter', Swal.stopTimer);
                        toast.addEventListener('mouseleave', Swal.resumeTimer);

                        // Add enhanced styling
                        const style = document.createElement('style');
                        style.textContent = `
                            .swal-enhanced-toast {
                                border-radius: 12px !important;
                                border-left: 4px solid {if $notify_t == "s"}#10b981{elseif $notify_t == "w"}#f59e0b{elseif $notify_t == "i"}#3b82f6{else}#ef4444{/if} !important;
                                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
                                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                            }
                            .swal-enhanced-title {
                                font-weight: 600 !important;
                                font-size: 0.95rem !important;
                            }
                            .swal-enhanced-content {
                                font-size: 0.9rem !important;
                                line-height: 1.5 !important;
                            }
                        `;
                        document.head.appendChild(style);
                    }
                });
            </script>
        {/if}