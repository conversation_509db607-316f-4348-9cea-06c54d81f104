<?php

/**
 * Plugin Patcher Controller for PHPNuxBill
 * Manages plugin compatibility and patching
 */

_admin();
$ui->assign('_title', 'Plugin Compatibility Patcher');
$ui->assign('_system_menu', 'settings');

$action = $routes['1'];
$ui->assign('_admin', $admin);

if (!in_array($admin['user_type'], ['SuperAdmin', 'Admin'])) {
    _alert(Lang::T('You do not have permission to access this page'), 'danger', "dashboard");
}

switch ($action) {
    case 'scan':
        $plugins_needing_patch = PluginPatcher::getPluginsNeedingPatch();
        $ui->assign('plugins_needing_patch', $plugins_needing_patch);
        $ui->assign('scan_result', true);
        $ui->display('admin/settings/plugin-patcher.tpl');
        break;
        
    case 'patch':
        if ($_app_stage == 'Demo') {
            r2(getUrl('pluginpatcher'), 'e', 'Demo Mode cannot patch plugins');
        }
        
        $plugin = $routes['2'] ?? '';
        if (empty($plugin)) {
            // Patch all plugins
            $patched = PluginPatcher::patchAllPlugins();
            r2(getUrl('pluginpatcher'), 's', "Successfully patched $patched plugin(s)");
        } else {
            // Patch specific plugin
            $plugin_file = $PLUGIN_PATH . DIRECTORY_SEPARATOR . $plugin;
            if (PluginPatcher::patchPlugin($plugin_file)) {
                r2(getUrl('pluginpatcher'), 's', "Successfully patched plugin: $plugin");
            } else {
                r2(getUrl('pluginpatcher'), 'e', "Failed to patch plugin: $plugin");
            }
        }
        break;
        
    case 'restore':
        if ($_app_stage == 'Demo') {
            r2(getUrl('pluginpatcher'), 'e', 'Demo Mode cannot restore plugins');
        }
        
        $plugin = $routes['2'] ?? '';
        if (empty($plugin)) {
            r2(getUrl('pluginpatcher'), 'e', 'Plugin name is required');
        }
        
        $plugin_file = $PLUGIN_PATH . DIRECTORY_SEPARATOR . $plugin;
        if (PluginPatcher::restorePlugin($plugin_file)) {
            r2(getUrl('pluginpatcher'), 's', "Successfully restored plugin: $plugin");
        } else {
            r2(getUrl('pluginpatcher'), 'e', "Failed to restore plugin: $plugin (no backup found)");
        }
        break;
        
    case 'cleanup':
        if ($_app_stage == 'Demo') {
            r2(getUrl('pluginpatcher'), 'e', 'Demo Mode cannot cleanup backups');
        }
        
        $days = intval($routes['2'] ?? 7);
        $cleaned = PluginPatcher::cleanupBackups($days);
        r2(getUrl('pluginpatcher'), 's', "Cleaned up $cleaned backup file(s) older than $days days");
        break;
        
    case 'info':
        $plugin = $routes['2'] ?? '';
        if (empty($plugin)) {
            r2(getUrl('pluginpatcher'), 'e', 'Plugin name is required');
        }
        
        $plugin_file = $PLUGIN_PATH . DIRECTORY_SEPARATOR . $plugin;
        if (!file_exists($plugin_file)) {
            r2(getUrl('pluginpatcher'), 'e', 'Plugin file not found');
        }
        
        $content = file_get_contents($plugin_file);
        $needs_patch = PluginPatcher::needsPatching($plugin_file);
        $backup_files = glob($plugin_file . '.backup.*');
        
        $ui->assign('plugin_name', $plugin);
        $ui->assign('plugin_content', $content);
        $ui->assign('needs_patch', $needs_patch);
        $ui->assign('backup_files', $backup_files);
        $ui->assign('plugin_info', true);
        $ui->display('admin/settings/plugin-patcher.tpl');
        break;
        
    default:
        // Get all plugins
        $all_plugins = glob(File::pathFixer($PLUGIN_PATH . DIRECTORY_SEPARATOR . '*.php'));
        $plugin_list = [];
        
        foreach ($all_plugins as $plugin_file) {
            $plugin_name = basename($plugin_file);
            $needs_patch = PluginPatcher::needsPatching($plugin_file);
            $backup_files = glob($plugin_file . '.backup.*');
            
            $plugin_list[] = [
                'name' => $plugin_name,
                'needs_patch' => $needs_patch,
                'has_backup' => !empty($backup_files),
                'backup_count' => count($backup_files),
                'size' => filesize($plugin_file),
                'modified' => filemtime($plugin_file)
            ];
        }
        
        // Get system compatibility info
        $compatibility_info = [
            'php_os' => PHP_OS_FAMILY,
            'php_version' => PHP_VERSION,
            'functions_available' => [
                'sys_getloadavg' => PluginCompatibility::isFunctionAvailable('sys_getloadavg'),
                'shell_exec' => PluginCompatibility::isFunctionAvailable('shell_exec'),
                'exec' => PluginCompatibility::isFunctionAvailable('exec'),
                'system' => PluginCompatibility::isFunctionAvailable('system'),
                'passthru' => PluginCompatibility::isFunctionAvailable('passthru')
            ],
            'disabled_functions' => PluginCompatibility::getDisabledFunctions()
        ];
        
        $ui->assign('plugin_list', $plugin_list);
        $ui->assign('compatibility_info', $compatibility_info);
        $ui->display('admin/settings/plugin-patcher.tpl');
        break;
}
