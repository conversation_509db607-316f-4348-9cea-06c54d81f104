<!-- Modern Bottom Navigation -->
<div class="bottom-nav" id="bottom-navigation">
    <div class="bottom-nav-items">
        {if isset($_admin)}
            <!-- Admin Navigation -->
            <a href="{Text::url('dashboard')}" class="bottom-nav-item {if $_system_menu eq 'dashboard'}active{/if}">
                <i class="fa fa-tachometer bottom-nav-icon"></i>
                <span class="bottom-nav-label">Dashboard</span>
            </a>

            <a href="{Text::url('customers')}" class="bottom-nav-item {if $_system_menu eq 'customers'}active{/if}">
                <i class="fa fa-users bottom-nav-icon"></i>
                <span class="bottom-nav-label">Customers</span>
            </a>

            {if !in_array($_admin['user_type'],['Report'])}
                <a href="{Text::url('plan/list')}" class="bottom-nav-item {if $_routes[0] eq 'plan' || $_routes[0] eq 'coupons'}active{/if}">
                    <i class="fa fa-ticket bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Services</span>
                </a>
            {/if}

            {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                <a href="{Text::url('services/hotspot')}" class="bottom-nav-item {if $_system_menu eq 'services'}active{/if}">
                    <i class="fa fa-wifi bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Plans</span>
                </a>
            {/if}

            {if $_c['radius_enable']}
                <a href="{Text::url('radius/data-statistics')}" class="bottom-nav-item {if $_routes[0] eq 'radius' and $_routes[1] eq 'data-statistics'}active{/if}">
                    <i class="fa fa-database bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Radius</span>
                </a>
            {else}
                <a href="{Text::url('reports')}" class="bottom-nav-item {if $_system_menu eq 'reports'}active{/if}">
                    <i class="fa fa-bar-chart bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Reports</span>
                </a>
            {/if}

            <!-- More Menu for Admin -->
            <div class="bottom-nav-item" id="admin-more-menu">
                <i class="fa fa-ellipsis-h bottom-nav-icon"></i>
                <span class="bottom-nav-label">More</span>
            </div>
        {else}
            <!-- Customer Navigation -->
            <a href="{Text::url('home')}" class="bottom-nav-item {if $_system_menu eq 'home'}active{/if}">
                <i class="fa fa-home bottom-nav-icon"></i>
                <span class="bottom-nav-label">Home</span>
            </a>

            {if $_c['payment_gateway'] != 'none' or $_c['payment_gateway'] == '' }
                <a href="{Text::url('order/package')}" class="bottom-nav-item {if $_system_menu eq 'package'}active{/if}">
                    <i class="fa fa-shopping-cart bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Beli</span>
                </a>
            {else}
                {if $_c['disable_voucher'] != 'yes'}
                    <a href="{Text::url('voucher/activation')}" class="bottom-nav-item {if $_system_menu eq 'voucher'}active{/if}">
                        <i class="fa fa-ticket bottom-nav-icon"></i>
                        <span class="bottom-nav-label">Voucher</span>
                    </a>
                {/if}
            {/if}

            <a href="{Text::url('data-usage/dashboard')}" class="bottom-nav-item {if $_system_menu eq 'data-usage'}active{/if}">
                <i class="fa fa-bar-chart bottom-nav-icon"></i>
                <span class="bottom-nav-label">Data</span>
            </a>

            <a href="{Text::url('mail')}" class="bottom-nav-item {if $_system_menu eq 'inbox'}active{/if}">
                <i class="fa fa-envelope bottom-nav-icon"></i>
                <span class="bottom-nav-label">Pesan</span>
            </a>

            <!-- More Menu for Customer -->
            <div class="bottom-nav-item" id="customer-more-menu">
                <i class="fa fa-ellipsis-h bottom-nav-icon"></i>
                <span class="bottom-nav-label">More</span>
            </div>
        {/if}
    </div>
</div>

{if isset($_admin)}
<!-- Admin More Menu Overlay -->
<div class="admin-more-overlay" id="admin-more-overlay">
    <div class="admin-more-content">
        <div class="admin-more-header">
            <h3>More Options</h3>
            <button class="admin-more-close" id="admin-more-close">
                <i class="fa fa-times"></i>
            </button>
        </div>

        <div class="admin-more-grid">
            {if !in_array($_admin['user_type'],['Report'])}
                <!-- Refill Customer -->
                {if $_c['disable_voucher'] != 'yes'}
                    <a href="{Text::url('plan/refill')}" class="admin-more-item">
                        <i class="fa fa-plus-circle"></i>
                        <span>Refill</span>
                    </a>
                {/if}

                <!-- Vouchers -->
                {if $_c['disable_voucher'] != 'yes'}
                    <a href="{Text::url('plan/voucher')}" class="admin-more-item">
                        <i class="fa fa-ticket"></i>
                        <span>Vouchers</span>
                    </a>
                {/if}

                <!-- Coupons -->
                {if $_c['enable_coupons'] == 'yes'}
                    <a href="{Text::url('coupons')}" class="admin-more-item">
                        <i class="fa fa-tags"></i>
                        <span>Coupons</span>
                    </a>
                {/if}

                <!-- Recharge Customer -->
                <a href="{Text::url('plan/recharge')}" class="admin-more-item">
                    <i class="fa fa-refresh"></i>
                    <span>Recharge</span>
                </a>

                <!-- Refill Balance -->
                {if $_c['enable_balance'] == 'yes'}
                    <a href="{Text::url('plan/deposit')}" class="admin-more-item">
                        <i class="fa fa-wallet"></i>
                        <span>Deposit</span>
                    </a>
                {/if}
            {/if}

            {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                <!-- Hotspot Plans -->
                <a href="{Text::url('services/hotspot')}" class="admin-more-item">
                    <i class="fa fa-wifi"></i>
                    <span>Hotspot</span>
                </a>

                <!-- PPPOE Plans -->
                <a href="{Text::url('services/pppoe')}" class="admin-more-item">
                    <i class="fa fa-plug"></i>
                    <span>PPPOE</span>
                </a>

                <!-- VPN Plans -->
                <a href="{Text::url('services/vpn')}" class="admin-more-item">
                    <i class="fa fa-shield"></i>
                    <span>VPN</span>
                </a>

                <!-- Bandwidth -->
                <a href="{Text::url('bandwidth/list')}" class="admin-more-item">
                    <i class="fa fa-tachometer"></i>
                    <span>Bandwidth</span>
                </a>

                <!-- Customer Balance -->
                {if $_c['enable_balance'] == 'yes'}
                    <a href="{Text::url('services/balance')}" class="admin-more-item">
                        <i class="fa fa-money"></i>
                        <span>Balance</span>
                    </a>
                {/if}
            {/if}

            <!-- Maps -->
            <a href="{Text::url('maps/customer')}" class="admin-more-item">
                <i class="fa fa-map-marker"></i>
                <span>Maps</span>
            </a>

            <!-- Router Maps -->
            <a href="{Text::url('maps/routers')}" class="admin-more-item">
                <i class="fa fa-sitemap"></i>
                <span>Router Map</span>
            </a>

            <!-- Daily Reports -->
            <a href="{Text::url('reports')}" class="admin-more-item">
                <i class="fa fa-calendar"></i>
                <span>Daily Report</span>
            </a>

            <!-- Activation History -->
            <a href="{Text::url('reports/activation')}" class="admin-more-item">
                <i class="fa fa-history"></i>
                <span>Activation</span>
            </a>

            <!-- Send Message -->
            <a href="{Text::url('message/send')}" class="admin-more-item">
                <i class="fa fa-comment"></i>
                <span>Message</span>
            </a>

            <!-- Bulk Message -->
            <a href="{Text::url('message/send_bulk')}" class="admin-more-item">
                <i class="fa fa-comments"></i>
                <span>Bulk Msg</span>
            </a>

            {if in_array($_admin['user_type'],['SuperAdmin','Admin'])}
                <!-- Network -->
                <a href="{Text::url('routers')}" class="admin-more-item">
                    <i class="fa fa-server"></i>
                    <span>Routers</span>
                </a>

                <!-- IP Pool -->
                <a href="{Text::url('pool/list')}" class="admin-more-item">
                    <i class="fa fa-list-ol"></i>
                    <span>IP Pool</span>
                </a>

                <!-- Port Pool -->
                <a href="{Text::url('pool/port')}" class="admin-more-item">
                    <i class="fa fa-plug"></i>
                    <span>Port Pool</span>
                </a>

                <!-- Radius (if enabled) -->
                {if $_c['radius_enable']}
                    <a href="{Text::url('radius/nas-list')}" class="admin-more-item">
                        <i class="fa fa-database"></i>
                        <span>Radius NAS</span>
                    </a>

                    <a href="{Text::url('radius/data-statistics')}" class="admin-more-item">
                        <i class="fa fa-bar-chart"></i>
                        <span>Data Stats</span>
                    </a>
                {/if}

                <!-- Pages -->
                <a href="{Text::url('pages/Order_Voucher')}" class="admin-more-item">
                    <i class="fa fa-file-text"></i>
                    <span>Pages</span>
                </a>

                <!-- General Settings -->
                <a href="{Text::url('settings/app')}" class="admin-more-item">
                    <i class="fa fa-cog"></i>
                    <span>Settings</span>
                </a>

                <!-- Localisation -->
                <a href="{Text::url('settings/localisation')}" class="admin-more-item">
                    <i class="fa fa-globe"></i>
                    <span>Language</span>
                </a>

                <!-- Custom Fields -->
                <a href="{Text::url('customfield')}" class="admin-more-item">
                    <i class="fa fa-edit"></i>
                    <span>Fields</span>
                </a>

                <!-- Miscellaneous -->
                <a href="{Text::url('settings/miscellaneous')}" class="admin-more-item">
                    <i class="fa fa-wrench"></i>
                    <span>Misc</span>
                </a>

                <!-- Maintenance Mode -->
                <a href="{Text::url('settings/maintenance')}" class="admin-more-item">
                    <i class="fa fa-exclamation-triangle"></i>
                    <span>Maintenance</span>
                </a>

                <!-- Widgets -->
                <a href="{Text::url('widgets')}" class="admin-more-item">
                    <i class="fa fa-th"></i>
                    <span>Widgets</span>
                </a>

                <!-- User Notification -->
                <a href="{Text::url('settings/notifications')}" class="admin-more-item">
                    <i class="fa fa-bell"></i>
                    <span>Notifications</span>
                </a>

                <!-- Devices -->
                <a href="{Text::url('settings/devices')}" class="admin-more-item">
                    <i class="fa fa-mobile"></i>
                    <span>Devices</span>
                </a>

                <!-- Backup/Restore -->
                <a href="{Text::url('settings/dbstatus')}" class="admin-more-item">
                    <i class="fa fa-download"></i>
                    <span>Backup</span>
                </a>

                <!-- Payment Gateway -->
                <a href="{Text::url('paymentgateway')}" class="admin-more-item">
                    <i class="fa fa-credit-card"></i>
                    <span>Payment</span>
                </a>

                <!-- Plugin Manager -->
                <a href="{Text::url('pluginmanager')}" class="admin-more-item">
                    <i class="fa fa-puzzle-piece"></i>
                    <span>Plugins</span>
                </a>

                <!-- Logs -->
                <a href="{Text::url('logs/phpnuxbill')}" class="admin-more-item">
                    <i class="fa fa-file-text"></i>
                    <span>Logs</span>
                </a>

                <!-- Message Logs -->
                <a href="{Text::url('logs/message')}" class="admin-more-item">
                    <i class="fa fa-envelope"></i>
                    <span>Msg Logs</span>
                </a>

                <!-- Documentation -->
                <a href="{if $_c['docs_clicked'] != 'yes'}{Text::url('settings/docs')}{else}{$app_url}/docs{/if}" class="admin-more-item">
                    <i class="fa fa-book"></i>
                    <span>Docs</span>
                </a>

                <!-- Community -->
                <a href="{Text::url('community')}" class="admin-more-item">
                    <i class="fa fa-users"></i>
                    <span>Community</span>
                </a>
            {/if}

            {if in_array($_admin['user_type'],['SuperAdmin','Admin','Agent'])}
                <!-- Admin Users -->
                <a href="{Text::url('settings/users')}" class="admin-more-item">
                    <i class="fa fa-user-secret"></i>
                    <span>Admin Users</span>
                </a>
            {/if}

            <!-- Change Password -->
            <a href="{Text::url('settings/change-password')}" class="admin-more-item">
                <i class="fa fa-key"></i>
                <span>Password</span>
            </a>

            <!-- Logout -->
            <a href="{Text::url('logout')}" class="admin-more-item logout">
                <i class="fa fa-sign-out"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>
</div>
{else}
<!-- Customer More Menu Overlay -->
<div class="admin-more-overlay" id="customer-more-overlay">
    <div class="admin-more-content">
        <div class="admin-more-header">
            <h3>More Options</h3>
            <button class="admin-more-close" id="customer-more-close">
                <i class="fa fa-times"></i>
            </button>
        </div>

        <div class="admin-more-grid">
            <!-- Data Usage Statistics -->
            <a href="{Text::url('data-usage/dashboard')}" class="admin-more-item">
                <i class="fa fa-bar-chart"></i>
                <span>Data Usage</span>
            </a>

            <!-- Activation History -->
            <a href="{Text::url('voucher/list-activated')}" class="admin-more-item">
                <i class="fa fa-list-alt"></i>
                <span>Aktivasi</span>
            </a>

            <!-- Transaction History -->
            <a href="{Text::url('order/history')}" class="admin-more-item">
                <i class="fa fa-file-text"></i>
                <span>Transaksi</span>
            </a>

            {if $_c['disable_voucher'] != 'yes'}
                <!-- Voucher Activation -->
                <a href="{Text::url('voucher/activation')}" class="admin-more-item">
                    <i class="fa fa-ticket"></i>
                    <span>Voucher</span>
                </a>
            {/if}

            {if $_c['payment_gateway'] != 'none' or $_c['payment_gateway'] == '' }
                {if $_c['enable_balance'] == 'yes'}
                    <!-- Buy Balance -->
                    <a href="{Text::url('order/balance')}" class="admin-more-item">
                        <i class="fa fa-wallet"></i>
                        <span>Isi Saldo</span>
                    </a>
                {/if}

                <!-- Buy Package -->
                <a href="{Text::url('order/package')}" class="admin-more-item">
                    <i class="fa fa-shopping-cart"></i>
                    <span>Beli Paket</span>
                </a>
            {/if}

            <!-- Profile -->
            <a href="{Text::url('accounts/profile')}" class="admin-more-item">
                <i class="fa fa-user"></i>
                <span>Profile</span>
            </a>

            <!-- Change Password -->
            <a href="{Text::url('accounts/change-password')}" class="admin-more-item">
                <i class="fa fa-key"></i>
                <span>Password</span>
            </a>

            <!-- Logout -->
            <a href="{Text::url('logout')}" class="admin-more-item logout">
                <i class="fa fa-sign-out"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>
</div>
{/if}

<style>
/* Bottom Navigation Styles */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    border-top: 1px solid #e2e8f0;
    padding: 8px 0 4px;
    z-index: 1000;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
}

.bottom-nav-items {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 500px;
    margin: 0 auto;
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #64748b;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 12px;
    min-width: 60px;
    cursor: pointer;
}

.bottom-nav-item.active,
.bottom-nav-item:hover {
    color: #00BFA5;
    background: rgba(0, 191, 165, 0.1);
    text-decoration: none;
    transform: translateY(-2px);
}

.bottom-nav-icon {
    font-size: 1.25rem;
    margin-bottom: 4px;
}

.bottom-nav-label {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Admin More Menu Overlay */
.admin-more-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: none;
    align-items: flex-end;
    justify-content: center;
}

.admin-more-content {
    background: #ffffff;
    border-radius: 20px 20px 0 0;
    padding: 24px;
    width: 100%;
    max-width: 500px;
    max-height: 70vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.admin-more-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.admin-more-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.admin-more-close {
    background: #f1f5f9;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
}

.admin-more-close:hover {
    background: #e2e8f0;
    color: #1e293b;
}

.admin-more-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.admin-more-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
    text-decoration: none;
    color: #64748b;
    transition: all 0.3s ease;
}

.admin-more-item:hover {
    background: #e2e8f0;
    color: #1e293b;
    text-decoration: none;
    transform: translateY(-2px);
}

.admin-more-item.logout {
    background: #fef2f2;
    color: #dc2626;
}

.admin-more-item.logout:hover {
    background: #fee2e2;
    color: #b91c1c;
}

.admin-more-item i {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.admin-more-item span {
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
}

/* Content padding for bottom nav */
.content-wrapper {
    padding-bottom: 80px !important;
}

/* Hide sidebar on mobile */
@media (max-width: 768px) {
    .main-sidebar {
        display: none !important;
    }

    .content-wrapper {
        margin-left: 0 !important;
    }

    .main-header .navbar {
        margin-left: 0 !important;
    }

    .main-header .logo {
        display: none;
    }
}

/* Desktop adjustments */
@media (min-width: 769px) {
    .bottom-nav {
        display: none;
    }

    .content-wrapper {
        padding-bottom: 20px !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Admin more menu functionality
    const adminMoreMenu = document.getElementById('admin-more-menu');
    const adminMoreOverlay = document.getElementById('admin-more-overlay');
    const adminMoreClose = document.getElementById('admin-more-close');

    if (adminMoreMenu && adminMoreOverlay) {
        adminMoreMenu.addEventListener('click', function() {
            adminMoreOverlay.style.display = 'flex';
        });

        if (adminMoreClose) {
            adminMoreClose.addEventListener('click', function() {
                adminMoreOverlay.style.display = 'none';
            });
        }

        adminMoreOverlay.addEventListener('click', function(e) {
            if (e.target === adminMoreOverlay) {
                adminMoreOverlay.style.display = 'none';
            }
        });
    }

    // Customer more menu functionality
    const customerMoreMenu = document.getElementById('customer-more-menu');
    const customerMoreOverlay = document.getElementById('customer-more-overlay');
    const customerMoreClose = document.getElementById('customer-more-close');

    if (customerMoreMenu && customerMoreOverlay) {
        customerMoreMenu.addEventListener('click', function() {
            customerMoreOverlay.style.display = 'flex';
        });

        if (customerMoreClose) {
            customerMoreClose.addEventListener('click', function() {
                customerMoreOverlay.style.display = 'none';
            });
        }

        customerMoreOverlay.addEventListener('click', function(e) {
            if (e.target === customerMoreOverlay) {
                customerMoreOverlay.style.display = 'none';
            }
        });
    }

    // Handle responsive navigation
    function handleResize() {
        const bottomNav = document.getElementById('bottom-navigation');
        if (window.innerWidth <= 768) {
            if (bottomNav) bottomNav.style.display = 'block';
        } else {
            if (bottomNav) bottomNav.style.display = 'none';
        }
    }

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial check
});
</script>
