/**
 * PHPNuxBill Mobile Table Enhancement
 * Makes all data tables mobile responsive with horizontal scrolling
 * Author: PHPNuxBill Team
 * Version: 1.0.0
 */

(function() {
    'use strict';

    // Mobile table enhancement class
    class MobileTableEnhancer {
        constructor() {
            this.tables = [];
            this.touchStartX = 0;
            this.touchStartY = 0;
            this.isScrolling = false;
            this.init();
        }

        init() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.enhanceAllTables());
            } else {
                this.enhanceAllTables();
            }

            // Re-enhance tables when new content is loaded (AJAX)
            this.observeNewTables();
        }

        enhanceAllTables() {
            // Find all table-responsive containers
            const tableContainers = document.querySelectorAll('.table-responsive');
            
            tableContainers.forEach(container => {
                this.enhanceTable(container);
            });

            // Also enhance tables that might not have the responsive class yet
            const tables = document.querySelectorAll('table.table');
            tables.forEach(table => {
                if (!table.closest('.table-responsive')) {
                    this.wrapTableInResponsiveContainer(table);
                }
            });
        }

        wrapTableInResponsiveContainer(table) {
            // Create responsive wrapper if it doesn't exist
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            
            // Insert wrapper before table
            table.parentNode.insertBefore(wrapper, table);
            
            // Move table into wrapper
            wrapper.appendChild(table);
            
            // Enhance the new wrapper
            this.enhanceTable(wrapper);
        }

        enhanceTable(container) {
            const table = container.querySelector('table');
            if (!table) return;

            // Add mobile table class
            container.classList.add('mobile-enhanced');
            
            // Add scroll indicators
            this.addScrollIndicators(container);
            
            // Add touch scroll support
            this.addTouchScrollSupport(container);
            
            // Add scroll position tracking
            this.addScrollTracking(container);
            
            // Add column sorting if not already present
            this.addColumnSorting(table);
            
            // Add row selection support
            this.addRowSelection(table);
            
            // Add search functionality
            this.addTableSearch(container);
            
            // Add export functionality
            this.addExportButtons(container);
            
            // Store table reference
            this.tables.push({
                container: container,
                table: table,
                enhanced: true
            });

            console.log('Enhanced table:', table.id || 'unnamed table');
        }

        addScrollIndicators(container) {
            // Scroll indicators are handled by CSS
            // This method can be used for dynamic indicator updates
            
            const updateIndicators = () => {
                const scrollLeft = container.scrollLeft;
                const scrollWidth = container.scrollWidth;
                const clientWidth = container.clientWidth;
                
                // Update left indicator
                if (scrollLeft > 10) {
                    container.classList.add('scrolled-left');
                } else {
                    container.classList.remove('scrolled-left');
                }
                
                // Update right indicator
                if (scrollLeft >= scrollWidth - clientWidth - 10) {
                    container.classList.add('scrolled-right');
                } else {
                    container.classList.remove('scrolled-right');
                }
            };

            container.addEventListener('scroll', updateIndicators);
            
            // Initial check
            setTimeout(updateIndicators, 100);
        }

        addTouchScrollSupport(container) {
            let isDown = false;
            let startX;
            let scrollLeft;

            // Mouse events for desktop
            container.addEventListener('mousedown', (e) => {
                isDown = true;
                container.classList.add('active');
                startX = e.pageX - container.offsetLeft;
                scrollLeft = container.scrollLeft;
                e.preventDefault();
            });

            container.addEventListener('mouseleave', () => {
                isDown = false;
                container.classList.remove('active');
            });

            container.addEventListener('mouseup', () => {
                isDown = false;
                container.classList.remove('active');
            });

            container.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - container.offsetLeft;
                const walk = (x - startX) * 2;
                container.scrollLeft = scrollLeft - walk;
            });

            // Touch events for mobile
            container.addEventListener('touchstart', (e) => {
                this.touchStartX = e.touches[0].clientX;
                this.touchStartY = e.touches[0].clientY;
                this.isScrolling = false;
                container.classList.add('interacted');
            }, { passive: true });

            container.addEventListener('touchmove', (e) => {
                if (!this.isScrolling) {
                    const touchX = e.touches[0].clientX;
                    const touchY = e.touches[0].clientY;
                    const diffX = Math.abs(touchX - this.touchStartX);
                    const diffY = Math.abs(touchY - this.touchStartY);
                    
                    // Determine scroll direction
                    if (diffX > diffY) {
                        this.isScrolling = true;
                        container.classList.add('horizontal-scrolling');
                    }
                }
            }, { passive: true });

            container.addEventListener('touchend', () => {
                container.classList.remove('horizontal-scrolling');
                this.isScrolling = false;
            }, { passive: true });
        }

        addScrollTracking(container) {
            let scrollTimeout;
            
            container.addEventListener('scroll', () => {
                container.classList.add('scrolling');
                
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    container.classList.remove('scrolling');
                }, 150);
            });
        }

        addColumnSorting(table) {
            const headers = table.querySelectorAll('thead th');
            
            headers.forEach((header, index) => {
                // Skip if header already has sorting
                if (header.querySelector('.sort-icon') || header.classList.contains('no-sort')) {
                    return;
                }
                
                header.classList.add('sortable');
                header.style.cursor = 'pointer';
                
                header.addEventListener('click', () => {
                    this.sortTable(table, index, header);
                });
            });
        }

        sortTable(table, columnIndex, header) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // Determine sort direction
            const isAsc = header.classList.contains('asc');
            const isDesc = header.classList.contains('desc');
            
            // Clear all sort classes
            table.querySelectorAll('th').forEach(th => {
                th.classList.remove('asc', 'desc');
            });
            
            // Set new sort direction
            if (!isAsc && !isDesc) {
                header.classList.add('asc');
            } else if (isAsc) {
                header.classList.add('desc');
            } else {
                header.classList.add('asc');
            }
            
            const sortDirection = header.classList.contains('asc') ? 1 : -1;
            
            // Sort rows
            rows.sort((a, b) => {
                const aText = a.cells[columnIndex]?.textContent.trim() || '';
                const bText = b.cells[columnIndex]?.textContent.trim() || '';
                
                // Try to parse as numbers
                const aNum = parseFloat(aText.replace(/[^\d.-]/g, ''));
                const bNum = parseFloat(bText.replace(/[^\d.-]/g, ''));
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return (aNum - bNum) * sortDirection;
                }
                
                // String comparison
                return aText.localeCompare(bText) * sortDirection;
            });
            
            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
            
            // Add visual feedback
            table.classList.add('sorting');
            setTimeout(() => table.classList.remove('sorting'), 300);
        }

        addRowSelection(table) {
            const selectAllCheckbox = table.querySelector('thead input[type="checkbox"]');
            const rowCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');
            
            if (!selectAllCheckbox || rowCheckboxes.length === 0) return;
            
            // Select all functionality
            selectAllCheckbox.addEventListener('change', () => {
                const isChecked = selectAllCheckbox.checked;
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                    this.toggleRowSelection(checkbox.closest('tr'), isChecked);
                });
                this.updateBulkActions();
            });
            
            // Individual row selection
            rowCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    const isChecked = checkbox.checked;
                    this.toggleRowSelection(checkbox.closest('tr'), isChecked);
                    
                    // Update select all checkbox
                    const checkedCount = table.querySelectorAll('tbody input[type="checkbox"]:checked').length;
                    selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
                    
                    this.updateBulkActions();
                });
            });
        }

        toggleRowSelection(row, isSelected) {
            if (isSelected) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        }

        updateBulkActions() {
            const selectedRows = document.querySelectorAll('tbody tr.selected');
            const bulkActionsContainer = document.querySelector('.table-bulk-actions-mobile');
            
            if (selectedRows.length > 0) {
                if (bulkActionsContainer) {
                    bulkActionsContainer.classList.add('show');
                    bulkActionsContainer.textContent = `${selectedRows.length} selected`;
                }
            } else {
                if (bulkActionsContainer) {
                    bulkActionsContainer.classList.remove('show');
                }
            }
        }

        addTableSearch(container) {
            const table = container.querySelector('table');
            if (!table) return;
            
            // Check if search already exists
            if (container.querySelector('.table-search-mobile')) return;
            
            // Create search container
            const searchContainer = document.createElement('div');
            searchContainer.className = 'table-search-mobile';
            
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.placeholder = 'Search table...';
            searchInput.className = 'form-control';
            
            searchContainer.appendChild(searchInput);
            container.insertBefore(searchContainer, table);
            
            // Add search functionality
            let searchTimeout;
            searchInput.addEventListener('input', () => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.filterTable(table, searchInput.value);
                }, 300);
            });
        }

        filterTable(table, searchTerm) {
            const rows = table.querySelectorAll('tbody tr');
            const term = searchTerm.toLowerCase();
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(term)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        addExportButtons(container) {
            const table = container.querySelector('table');
            if (!table) return;

            // Check if export buttons already exist
            if (container.querySelector('.table-export-mobile')) return;

            // Create export container
            const exportContainer = document.createElement('div');
            exportContainer.className = 'table-export-mobile';

            // CSV Export button
            const csvBtn = document.createElement('button');
            csvBtn.className = 'btn btn-success btn-sm';
            csvBtn.innerHTML = '<i class="fa fa-download"></i> CSV';
            csvBtn.addEventListener('click', () => this.exportTableToCSV(table));

            // Print button
            const printBtn = document.createElement('button');
            printBtn.className = 'btn btn-info btn-sm';
            printBtn.innerHTML = '<i class="fa fa-print"></i> Print';
            printBtn.addEventListener('click', () => this.printTable(table));

            // Copy button
            const copyBtn = document.createElement('button');
            copyBtn.className = 'btn btn-warning btn-sm';
            copyBtn.innerHTML = '<i class="fa fa-copy"></i> Copy';
            copyBtn.addEventListener('click', () => this.copyTableToClipboard(table));

            exportContainer.appendChild(csvBtn);
            exportContainer.appendChild(printBtn);
            exportContainer.appendChild(copyBtn);

            container.insertBefore(exportContainer, table);
        }

        exportTableToCSV(table) {
            const rows = table.querySelectorAll('tr');
            const csvContent = [];

            rows.forEach(row => {
                const cols = row.querySelectorAll('th, td');
                const rowData = [];

                cols.forEach(col => {
                    // Clean text content
                    let text = col.textContent.trim();
                    // Remove extra whitespace and newlines
                    text = text.replace(/\s+/g, ' ');
                    // Escape quotes
                    text = text.replace(/"/g, '""');
                    // Wrap in quotes if contains comma
                    if (text.includes(',') || text.includes('"') || text.includes('\n')) {
                        text = `"${text}"`;
                    }
                    rowData.push(text);
                });

                csvContent.push(rowData.join(','));
            });

            // Create and download CSV
            const csvString = csvContent.join('\n');
            const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `table_export_${new Date().getTime()}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        printTable(table) {
            const printWindow = window.open('', '_blank');
            const tableClone = table.cloneNode(true);

            // Remove action columns and checkboxes
            const actionCells = tableClone.querySelectorAll('th:last-child, td:last-child');
            actionCells.forEach(cell => {
                if (cell.textContent.toLowerCase().includes('manage') ||
                    cell.textContent.toLowerCase().includes('action')) {
                    cell.remove();
                }
            });

            const checkboxes = tableClone.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.remove());

            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Table Print</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; font-weight: bold; }
                        tr:nth-child(even) { background-color: #f9f9f9; }
                        @media print {
                            body { margin: 0; }
                            table { font-size: 12px; }
                        }
                    </style>
                </head>
                <body>
                    <h2>PHPNuxBill Data Export</h2>
                    <p>Generated on: ${new Date().toLocaleString()}</p>
                    ${tableClone.outerHTML}
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.focus();
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 250);
        }

        copyTableToClipboard(table) {
            const rows = table.querySelectorAll('tr');
            const textContent = [];

            rows.forEach(row => {
                const cols = row.querySelectorAll('th, td');
                const rowData = [];

                cols.forEach(col => {
                    rowData.push(col.textContent.trim());
                });

                textContent.push(rowData.join('\t'));
            });

            const tableText = textContent.join('\n');

            // Try to copy to clipboard
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(tableText).then(() => {
                    this.showNotification('Table copied to clipboard!', 'success');
                }).catch(() => {
                    this.fallbackCopyToClipboard(tableText);
                });
            } else {
                this.fallbackCopyToClipboard(tableText);
            }
        }

        fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                this.showNotification('Table copied to clipboard!', 'success');
            } catch (err) {
                this.showNotification('Failed to copy table', 'error');
            }

            document.body.removeChild(textArea);
        }

        showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} mobile-notification`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                padding: 10px 15px;
                border-radius: 5px;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                animation: slideInRight 0.3s ease;
            `;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        addBulkActionsContainer() {
            // Check if bulk actions container already exists
            if (document.querySelector('.table-bulk-actions-mobile')) return;

            const bulkContainer = document.createElement('div');
            bulkContainer.className = 'table-bulk-actions-mobile';
            bulkContainer.innerHTML = `
                <span class="selected-count">0 selected</span>
                <button class="btn btn-danger btn-sm bulk-delete" style="margin-left: 10px;">
                    <i class="fa fa-trash"></i> Delete
                </button>
                <button class="btn btn-primary btn-sm bulk-export" style="margin-left: 5px;">
                    <i class="fa fa-download"></i> Export
                </button>
            `;

            document.body.appendChild(bulkContainer);

            // Add event listeners for bulk actions
            const deleteBtn = bulkContainer.querySelector('.bulk-delete');
            const exportBtn = bulkContainer.querySelector('.bulk-export');

            deleteBtn.addEventListener('click', () => this.handleBulkDelete());
            exportBtn.addEventListener('click', () => this.handleBulkExport());
        }

        handleBulkDelete() {
            const selectedRows = document.querySelectorAll('tbody tr.selected');
            if (selectedRows.length === 0) return;

            if (confirm(`Are you sure you want to delete ${selectedRows.length} selected items?`)) {
                // Collect IDs or data for deletion
                const selectedIds = [];
                selectedRows.forEach(row => {
                    const checkbox = row.querySelector('input[type="checkbox"]');
                    if (checkbox && checkbox.value) {
                        selectedIds.push(checkbox.value);
                    }
                });

                // Trigger bulk delete event
                const event = new CustomEvent('bulkDelete', {
                    detail: { ids: selectedIds, rows: selectedRows }
                });
                document.dispatchEvent(event);

                this.showNotification(`${selectedRows.length} items marked for deletion`, 'warning');
            }
        }

        handleBulkExport() {
            const selectedRows = document.querySelectorAll('tbody tr.selected');
            if (selectedRows.length === 0) {
                this.showNotification('No rows selected for export', 'warning');
                return;
            }

            // Create temporary table with selected rows
            const originalTable = selectedRows[0].closest('table');
            const tempTable = originalTable.cloneNode(false);

            // Clone header
            const header = originalTable.querySelector('thead');
            if (header) {
                tempTable.appendChild(header.cloneNode(true));
            }

            // Create tbody and add selected rows
            const tbody = document.createElement('tbody');
            selectedRows.forEach(row => {
                tbody.appendChild(row.cloneNode(true));
            });
            tempTable.appendChild(tbody);

            // Export the temporary table
            this.exportTableToCSV(tempTable);
            this.showNotification(`${selectedRows.length} selected rows exported`, 'success');
        }

        updateBulkActions() {
            const selectedRows = document.querySelectorAll('tbody tr.selected');
            let bulkActionsContainer = document.querySelector('.table-bulk-actions-mobile');

            // Create bulk actions container if it doesn't exist
            if (!bulkActionsContainer) {
                this.addBulkActionsContainer();
                bulkActionsContainer = document.querySelector('.table-bulk-actions-mobile');
            }

            const countSpan = bulkActionsContainer.querySelector('.selected-count');

            if (selectedRows.length > 0) {
                bulkActionsContainer.classList.add('show');
                if (countSpan) {
                    countSpan.textContent = `${selectedRows.length} selected`;
                }
            } else {
                bulkActionsContainer.classList.remove('show');
            }
        }

        // Enhanced init method to include bulk actions
        init() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    this.enhanceAllTables();
                    this.addBulkActionsContainer();
                });
            } else {
                this.enhanceAllTables();
                this.addBulkActionsContainer();
            }

            // Re-enhance tables when new content is loaded (AJAX)
            this.observeNewTables();

            // Add CSS animations
            this.addCSSAnimations();
        }

        addCSSAnimations() {
            // Add CSS animations for notifications and interactions
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }

                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }

                .table-responsive.sorting {
                    opacity: 0.8;
                    transition: opacity 0.3s ease;
                }

                .table-responsive.scrolling {
                    box-shadow: inset 0 0 10px rgba(0,0,0,0.1);
                }

                .table-responsive.horizontal-scrolling {
                    cursor: grabbing;
                    user-select: none;
                }

                .mobile-notification {
                    transition: all 0.3s ease;
                }
            `;
            document.head.appendChild(style);
        }

        // Method to handle responsive breakpoints
        handleResponsiveBreakpoints() {
            const checkBreakpoint = () => {
                const isMobile = window.innerWidth <= 768;
                const tables = document.querySelectorAll('.table-responsive');

                tables.forEach(container => {
                    if (isMobile) {
                        container.classList.add('mobile-mode');
                    } else {
                        container.classList.remove('mobile-mode');
                    }
                });
            };

            // Check on load and resize
            checkBreakpoint();
            window.addEventListener('resize', checkBreakpoint);
        }

        // Public method to get table statistics
        getTableStats() {
            return {
                totalTables: this.tables.length,
                enhancedTables: this.tables.filter(t => t.enhanced).length,
                mobileOptimized: document.querySelectorAll('.table-responsive.mobile-enhanced').length
            };
        }

        // Public method to disable/enable table enhancement
        toggleTableEnhancement(enable = true) {
            const tables = document.querySelectorAll('.table-responsive');
            tables.forEach(container => {
                if (enable) {
                    container.classList.add('mobile-enhanced');
                } else {
                    container.classList.remove('mobile-enhanced');
                }
            });
        }

        observeNewTables() {
            // Observe for dynamically added tables
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check for new tables
                            const newTables = node.querySelectorAll ? node.querySelectorAll('table.table') : [];
                            newTables.forEach(table => {
                                if (!table.closest('.mobile-enhanced')) {
                                    this.wrapTableInResponsiveContainer(table);
                                }
                            });
                            
                            // Check if the node itself is a table
                            if (node.tagName === 'TABLE' && node.classList.contains('table')) {
                                if (!node.closest('.mobile-enhanced')) {
                                    this.wrapTableInResponsiveContainer(node);
                                }
                            }
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        // Public method to manually enhance a specific table
        enhanceSpecificTable(tableSelector) {
            const table = document.querySelector(tableSelector);
            if (table) {
                if (!table.closest('.table-responsive')) {
                    this.wrapTableInResponsiveContainer(table);
                } else {
                    this.enhanceTable(table.closest('.table-responsive'));
                }
            }
        }

        // Public method to refresh all tables
        refreshAllTables() {
            this.tables = [];
            this.enhanceAllTables();
        }
    }

    // Initialize mobile table enhancer
    window.MobileTableEnhancer = new MobileTableEnhancer();

    // Expose public methods
    window.enhanceTable = (selector) => window.MobileTableEnhancer.enhanceSpecificTable(selector);
    window.refreshTables = () => window.MobileTableEnhancer.refreshAllTables();

    console.log('PHPNuxBill Mobile Table Enhancement loaded');

})();
