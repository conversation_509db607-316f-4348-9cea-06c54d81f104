{include file="customer/header.tpl"}
<!-- Modern Voucher Activation -->

<div class="row">
    <div class="col-md-8">
        <div class="modern-card" style="padding: 24px;">
            <div class="box-header" style="margin-bottom: 20px;">
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #1e293b; margin: 0;">
                    <i class="fa fa-info-circle" style="color: #00BFA5; margin-right: 8px;"></i>
                    {Lang::T('Order Voucher')}
                </h3>
            </div>
            <div style="padding: 16px; background: #f8fafc; border-radius: 12px; border-left: 4px solid #00BFA5;">
                {if file_exists("$PAGES_PATH/Order_Voucher.html")}
                    {include file="$PAGES_PATH/Order_Voucher.html"}
                {else}
                    <p style="color: #64748b; margin: 0;">
                        <i class="fa fa-store" style="margin-right: 8px;"></i>
                        Dapatkan voucher internet di warung terdekat atau hubungi customer service untuk informasi lebih lanjut.
                    </p>
                {/if}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="modern-card" style="padding: 24px;">
            <div class="box-header" style="margin-bottom: 20px;">
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #1e293b; margin: 0;">
                    <i class="fa fa-ticket" style="color: #00BFA5; margin-right: 8px;"></i>
                    {Lang::T('Voucher Activation')}
                </h3>
            </div>
            <form method="post" role="form" action="{Text::url('voucher/activation-post')}">
                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 8px; display: block;">
                        Kode Voucher
                    </label>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="code"
                               name="code"
                               value="{$code|escape:'html'}"
                               placeholder="{Lang::T('Enter voucher code here')}"
                               style="border: 2px solid #e2e8f0; border-radius: 12px; padding: 12px 16px; font-size: 0.875rem; transition: all 0.3s ease;"
                               onfocus="this.style.borderColor='#00BFA5'; this.style.boxShadow='0 0 0 3px rgba(0, 191, 165, 0.1)'"
                               onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='none'">
                        <span class="input-group-btn">
                            <a class="btn"
                               href="{$app_url|escape:'html'}/scan/?back={urlencode(Text::url('voucher/activation&code='))}"
                               style="background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                                      color: white;
                                      border: none;
                                      border-radius: 12px;
                                      padding: 12px 16px;
                                      margin-left: 8px;
                                      transition: all 0.3s ease;"
                               onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(0, 191, 165, 0.3)'"
                               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fa fa-qrcode"></i>
                            </a>
                        </span>
                    </div>
                    <small style="color: #64748b; font-size: 0.75rem; margin-top: 4px; display: block;">
                        <i class="fa fa-info-circle" style="margin-right: 4px;"></i>
                        Masukkan kode voucher atau scan QR code
                    </small>
                </div>
                <div class="form-group">
                    <button class="btn"
                            type="submit"
                            style="background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 100%);
                                   color: white;
                                   border: none;
                                   border-radius: 12px;
                                   padding: 12px 24px;
                                   font-size: 0.875rem;
                                   font-weight: 600;
                                   width: 100%;
                                   transition: all 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(0, 191, 165, 0.3)'"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 191, 165, 0.2)'">
                        <i class="fa fa-check-circle" style="margin-right: 8px;"></i>
                        {Lang::T('Recharge')}
                    </button>
                    <div style="text-align: center; margin-top: 16px;">
                        <a href="{Text::url('home')}"
                           style="color: #64748b; font-size: 0.875rem; text-decoration: none; transition: color 0.3s ease;"
                           onmouseover="this.style.color='#00BFA5'"
                           onmouseout="this.style.color='#64748b'">
                            <i class="fa fa-arrow-left" style="margin-right: 4px;"></i>
                            {Lang::T('Cancel')}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

{include file="customer/footer.tpl"}
