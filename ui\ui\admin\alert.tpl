<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{ucwords(Lang::T($type))} - {$_c['CompanyName']}</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="shortcut icon" href="{$app_url}/ui/ui/images/logo.png" type="image/x-icon" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <meta http-equiv="refresh" content="{$time}; url={$url}">

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 50%, #26A69A 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        .alert-container {
            position: relative;
            z-index: 2;
            max-width: 500px;
            width: 90%;
            margin: 0 auto;
            animation: slideInUp 0.6s ease-out;
        }

        .alert-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 8px 25px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .alert-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                {if $type == 'success'}#10b981, #059669{elseif $type == 'warning'}#f59e0b, #d97706{elseif $type == 'danger'}#ef4444, #dc2626{else}#3b82f6, #1d4ed8{/if});
            border-radius: 24px 24px 0 0;
        }

        .alert-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg,
                {if $type == 'success'}#10b981, #059669{elseif $type == 'warning'}#f59e0b, #d97706{elseif $type == 'danger'}#ef4444, #dc2626{else}#3b82f6, #1d4ed8{/if});
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            box-shadow: 0 8px 25px rgba({if $type == 'success'}16, 185, 129{elseif $type == 'warning'}245, 158, 11{elseif $type == 'danger'}239, 68, 68{else}59, 130, 246{/if}, 0.4);
            animation: pulse 2s infinite;
        }

        .alert-icon i {
            font-size: 2.5rem;
            color: white;
        }

        .alert-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            letter-spacing: -0.025em;
        }

        .alert-message {
            font-size: 1.1rem;
            color: #475569;
            line-height: 1.6;
            margin-bottom: 32px;
            font-weight: 500;
        }

        .alert-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 16px 32px;
            background: linear-gradient(135deg,
                {if $type == 'success'}#10b981, #059669{elseif $type == 'warning'}#f59e0b, #d97706{elseif $type == 'danger'}#ef4444, #dc2626{else}#3b82f6, #1d4ed8{/if});
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-width: 200px;
        }

        .alert-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .alert-button:hover::before {
            left: 100%;
        }

        .alert-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba({if $type == 'success'}16, 185, 129{elseif $type == 'warning'}245, 158, 11{elseif $type == 'danger'}239, 68, 68{else}59, 130, 246{/if}, 0.4);
            color: white;
            text-decoration: none;
        }

        .alert-button i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .countdown {
            font-weight: 700;
            color: rgba(255, 255, 255, 0.9);
        }

        .company-footer {
            margin-top: 24px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.95rem;
            font-weight: 500;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg,
                {if $type == 'success'}#10b981, #059669{elseif $type == 'warning'}#f59e0b, #d97706{elseif $type == 'danger'}#ef4444, #dc2626{else}#3b82f6, #1d4ed8{/if});
            border-radius: 0 0 24px 24px;
            transition: width 0.1s linear;
            width: 100%;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @media (max-width: 480px) {
            .alert-container {
                width: 95%;
            }

            .alert-card {
                padding: 32px 24px;
                border-radius: 20px;
            }

            .alert-icon {
                width: 70px;
                height: 70px;
            }

            .alert-icon i {
                font-size: 2rem;
            }

            .alert-title {
                font-size: 1.5rem;
            }

            .alert-message {
                font-size: 1rem;
            }

            .alert-button {
                padding: 14px 24px;
                font-size: 0.95rem;
                min-width: 180px;
            }
        }
    </style>
</head>

<body>
    <div class="alert-container">
        <div class="alert-card">
            <div class="alert-icon">
                <i class="fa {if $type == 'success'}fa-check{elseif $type == 'warning'}fa-exclamation-triangle{elseif $type == 'danger'}fa-times{else}fa-info{/if}"></i>
            </div>

            <h1 class="alert-title">
                {if $type == 'success'}Success!{elseif $type == 'warning'}Warning!{elseif $type == 'danger'}Error!{else}Information{/if}
            </h1>

            <p class="alert-message">{$text}</p>

            <a href="{$url}" id="alertButton" class="alert-button">
                <i class="fa fa-arrow-right"></i>
                <span id="buttonText">{Lang::T('Click Here')}</span>
                <span class="countdown">(<span id="countdown">{$time}</span>)</span>
            </a>

            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div class="company-footer">
            {$_c['CompanyName']}
        </div>
    </div>

    <script>
        let time = {$time};
        const totalTime = {$time};
        const progressBar = document.getElementById('progressBar');
        const countdown = document.getElementById('countdown');

        function updateProgress() {
            const percentage = (time / totalTime) * 100;
            progressBar.style.width = percentage + '%';
        }

        function timer() {
            updateProgress();

            if (time > 0) {
                countdown.textContent = time;
                time--;
                setTimeout(timer, 1000);
            } else {
                countdown.textContent = '0';
                progressBar.style.width = '0%';
                window.location.href = '{$url}';
            }
        }

        // Start the timer
        timer();

        // Add click handler for immediate redirect
        document.getElementById('alertButton').addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = '{$url}';
        });

        // Add keyboard support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                window.location.href = '{$url}';
            }
        });
    </script>
</body>

</html>