<?php

/**
 *  PHP Mikrotik Billing (https://github.com/hotspotbilling/phpnuxbill/)
 *  by https://t.me/ibnux
 **/


/**
 * Validator class
 */
class Validator
{

    /**
     * String text finder
     *
     * @access private
     * @param string $string
     * @param array $hits
     * @return void
     */
    public static function textHit($string, $exclude = "")
    {
        if (empty($exclude)) return false;
        if (is_array($exclude)) {
            foreach ($exclude as $text) {
                if (strstr($string, $text)) return true;
            }
        } else {
            if (strstr($string, $exclude)) return true;
        }
        return false;
    }

    /**
     * Number compare
     *
     * @access private
     * @param int $integer
     * @param int $max
     * @param int $min
     * @return bool
     */
    private static function numberBetween($integer, $max = null, $min = 0)
    {
        if (is_numeric($min) && $integer <= $min) return false;
        if (is_numeric($max) && $integer >= $max) return false;
        return true;
    }

    /**
     * Email addres check
     *
     * @access public
     * @param string $string
     * @param array $exclude
     * @return bool
     */
    public static function Email($string, $exclude = "")
    {
        if (self::textHit($string, $exclude)) return false;
        return (bool)preg_match("/^([a-z0-9])(([-a-z0-9._])*([a-z0-9]))*\@([a-z0-9])(([a-z0-9-])*([a-z0-9]))+(\.([a-z0-9])([-a-z0-9_-])?([a-z0-9])+)+$/i", $string);
    }

    /**
     * URL check
     *
     * @access public
     * @param strin $string
     * @return bool
     */
    public static function Url($string, $exclude = "")
    {
        if (self::textHit($string, $exclude)) return false;
        return (bool)preg_match("/^(http|https|ftp):\/\/([A-Z0-9][A-Z0-9_-]*(?:\.[A-Z0-9][A-Z0-9_-]*)+):?(\d+)?\/?/i", $string);
    }

    /**
     * IP
     *
     * @access public
     * @param string $string
     * @return void
     */
    public static function Ip($string)
    {
        return (bool)preg_match("/^(1?\d{1,2}|2([0-4]\d|5[0-5]))(\.(1?\d{1,2}|2([0-4]\d|5[0-5]))){3}$/", $string);
    }

    /**
     * Check if it is an number
     *
     * @access public
     * @param int $integer
     * @param int $max
     * @param int $min
     * @return bool
     */
    public static function Number($integer, $max = null, $min = 0)
    {
        if (preg_match("/^\-?\+?[0-9e1-9]+$/", $integer)) {
            if (!self::numberBetween($integer, $max, $min)) return false;
            return true;
        }
        return false;
    }

    /**
     * Check if it is an unsigned number
     *
     * @access public
     * @param int $integer
     * @return bool
     */
    public static function UnsignedNumber($integer)
    {
        return (bool)preg_match("/^\+?[0-9]+$/", $integer);
    }

    /**
     * Float
     *
     * @access public
     * @param string $string
     * @return bool
     */
    public static function Float($string)
    {
        return (bool)($string == strval(floatval($string))) ? true : false;
    }

    /**
     * Alpha check
     *
     * @access public
     * @param string $string
     * @return void
     */
    public static function Alpha($string)
    {
        return (bool)preg_match("/^[a-zA-Z]+$/", $string);
    }

    /**
     * Alpha numeric check
     *
     * @access public
     * @param string $string
     * @return void
     */
    public static function AlphaNumeric($string)
    {
        return (bool)preg_match("/^[0-9a-zA-Z]+$/", $string);
    }

    /**
     * Specific chars check
     *
     * @access public
     * @param string $string
     * @param array $allowed
     * @return void
     */
    public static function Chars($string, $allowed = array("a-z"))
    {
        return (bool)preg_match("/^[" . implode("", $allowed) . "]+$/", $string);
    }

    /**
     * Check length of an string
     *
     * @access public
     * @param string $stirng
     * @param int $max
     * @param int $min
     * @return bool
     */
    public static function Length($string, $max = null, $min = 0)
    {
        $length = strlen($string);
        if (!self::numberBetween($length, $max, $min)) return false;
        return true;
    }

    /**
     * Hex color check
     *
     * @access public
     * @param string $string
     * @return void
     */
    public static function HexColor($string)
    {
        return (bool)preg_match("/^(#)?([0-9a-f]{1,2}){3}$/i", $string);
    }

    /**
     * Data validation
     *
     * Does'nt matter how you provide the date
     * dd/mm/yyyy
     * dd-mm-yyyy
     * yyyy/mm/dd
     * yyyy-mm-dd
     *
     * @access public
     * @param string $string
     * @return bool
     */
    public static function Date($string)
    {
        $date = date('Y', strtotime($string));
        return ($date == "1970" || $date == '') ? false : true;
    }

    /**
     * Older than check
     *
     * @access public
     * @param string $string
     * @param int $age
     * @return bool
     */
    public static function OlderThan($string, $age)
    {
        $date = date('Y', strtotime($string));
        if ($date == "1970" || $date == '') return false;
        return (date('Y') - $date) > $age ? true : false;
    }

    /**
     * XML valid
     *
     * @access public
     * @param string $string
     * @return bool
     */
    public static function Xml($string)
    {
        $Xml = @simplexml_load_string($string);
        return ($Xml === false) ? false : true;
    }

    /**
     * Is filesize between
     *
     * @access public
     * @param string $file
     * @param int $max
     * @param int $min
     * @return bool
     */
    public static function FilesizeBetween($file, $max = null, $min = 0)
    {
        $filesize = filesize($file);
        return self::numberBetween($filesize, $max, $min);
    }

    /**
     * Is image width between
     *
     * @access public
     * @param string $image
     * @param int $max_width
     * @param int $min_width
     * @param int $max_height
     * @param int $min_height
     * @return void
     */
    public static function ImageSizeBetween($image, $max_width = "", $min_width = 0, $max_height = "", $min_height = 0)
    {
        $size = getimagesize($image);
        if (!self::numberBetween($size[0], $max_width, $min_width)) return false;
        if (!self::numberBetween($size[1], $max_height, $min_height)) return false;
        return true;
    }

    /**
     * Phone numbers
     *
     * @access public
     * @param string $phone
     * @return bool
     */
    public static function Phone($phone)
    {
        $formats = array(
            '###-###-####',
            '####-###-###',
            '(###) ###-###',
            '####-####-####',
            '##-###-####-####',
            '####-####',
            '###-###-###',
            '#####-###-###',
            '##########',
            '####-##-##-##'
        );
        $format = trim(preg_replace("/[0-9]/", "#", $phone));
        return (bool)in_array($format, $formats);
    }

    public static function countRouterPlan($plans, $router)
    {
        $n = 0;
        foreach ($plans as $plan) {
            if ($plan['routers'] == $router) {
                $n++;
            }
        }
        return $n;
    }

    public static function isRouterHasPlan($plans, $router)
    {
        foreach ($plans as $plan) {
            if ($plan['routers'] == $router) {
                return true;
            }
        }
        return false;
    }

    public static function containsKeyword($string, $keywords = ['mikrotik', 'hotspot', 'pppoe', 'radius', 'dummy'])
    {
        $regex = '/' . implode('|', $keywords) . '/i';
        return preg_match($regex, strtolower($string));
    }

    /**
     * Validate and normalize service_type to prevent data truncation
     * @param string $service_type
     * @return string Valid service_type value
     */
    public static function ServiceType($service_type)
    {
        $valid_service_types = ['Hotspot', 'PPPoE', 'VPN', 'Others'];

        // Handle NULL, empty, or invalid input
        if (empty($service_type) || is_null($service_type)) {
            return 'Others';
        }

        // Normalize common variations
        $service_type = trim(strval($service_type));

        // Convert to lowercase for comparison
        $lower_service_type = strtolower($service_type);

        // Comprehensive normalization mapping
        $normalized = [
            // PPPoE variations
            'pppoe' => 'PPPoE',
            'PPPOE' => 'PPPoE',
            'Pppoe' => 'PPPoE',
            'ppp' => 'PPPoE',
            'PPP' => 'PPPoE',
            'point-to-point' => 'PPPoE',

            // Hotspot variations
            'hotspot' => 'Hotspot',
            'HOTSPOT' => 'Hotspot',
            'Hotspot' => 'Hotspot',
            'hot' => 'Hotspot',
            'HOT' => 'Hotspot',
            'wifi' => 'Hotspot',
            'WIFI' => 'Hotspot',

            // VPN variations
            'vpn' => 'VPN',
            'VPN' => 'VPN',
            'Vpn' => 'VPN',
            'virtual' => 'VPN',
            'VIRTUAL' => 'VPN',

            // Others variations
            'others' => 'Others',
            'OTHERS' => 'Others',
            'Others' => 'Others',
            'other' => 'Others',
            'OTHER' => 'Others',
            'default' => 'Others',
            'DEFAULT' => 'Others'
        ];

        // Check exact match first
        if (in_array($service_type, $valid_service_types)) {
            return $service_type;
        }

        // Check normalized mapping
        if (isset($normalized[$service_type])) {
            return $normalized[$service_type];
        }

        // Check lowercase mapping
        if (isset($normalized[$lower_service_type])) {
            return $normalized[$lower_service_type];
        }

        // Pattern matching for partial matches
        if (stripos($service_type, 'ppp') !== false) {
            return 'PPPoE';
        }
        if (stripos($service_type, 'hot') !== false || stripos($service_type, 'wifi') !== false) {
            return 'Hotspot';
        }
        if (stripos($service_type, 'vpn') !== false || stripos($service_type, 'virtual') !== false) {
            return 'VPN';
        }

        // Default fallback
        return 'Others';
    }

    /**
     * Force fix all service_type data in database
     * This function should be called once to fix existing data
     */
    public static function fixServiceTypeData()
    {
        try {
            // Set SQL mode to allow data updates
            ORM::raw_execute("SET SQL_MODE = ''");

            // Fix NULL and empty values
            ORM::raw_execute("UPDATE `tbl_customers` SET `service_type` = 'Others' WHERE `service_type` IS NULL OR `service_type` = ''");

            // Fix common variations
            ORM::raw_execute("UPDATE `tbl_customers` SET `service_type` = 'PPPoE' WHERE `service_type` IN ('PPPOE', 'pppoe', 'Pppoe', 'ppp', 'PPP')");
            ORM::raw_execute("UPDATE `tbl_customers` SET `service_type` = 'Hotspot' WHERE `service_type` IN ('HOTSPOT', 'hotspot', 'hot', 'HOT', 'wifi', 'WIFI')");
            ORM::raw_execute("UPDATE `tbl_customers` SET `service_type` = 'VPN' WHERE `service_type` IN ('vpn', 'VPN', 'Vpn', 'virtual', 'VIRTUAL')");

            // Fix any remaining invalid values
            ORM::raw_execute("UPDATE `tbl_customers` SET `service_type` = 'Others' WHERE `service_type` NOT IN ('Hotspot', 'PPPoE', 'VPN', 'Others')");

            // Ensure ENUM structure is correct
            ORM::raw_execute("ALTER TABLE `tbl_customers` CHANGE `service_type` `service_type` ENUM('Hotspot','PPPoE','VPN','Others') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'Others' COMMENT 'For selecting user type'");

            return true;
        } catch (Exception $e) {
            error_log("Error fixing service_type data: " . $e->getMessage());
            return false;
        }
    }
}
