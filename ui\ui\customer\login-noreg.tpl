<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>{Lang::T('Login')} - {$_c['CompanyName']}</title>
    <link rel="shortcut icon" href="{$app_url}/ui/ui/images/logo.png" type="image/x-icon" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="{$app_url}/ui/ui/scripts/jquery.min.js"></script>
    <script src="{$app_url}/ui/ui/scripts/bootstrap.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #00BFA5 0%, #4DB6AC 50%, #26A69A 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        .voucher-container {
            position: relative;
            z-index: 2;
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 20px;
        }

        .voucher-content {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 450px;
            gap: 40px;
            align-items: start;
        }

        .announcement-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .announcement-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }

        .announcement-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }

        .announcement-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .announcement-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
        }

        .announcement-content {
            color: #475569;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .voucher-forms {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .voucher-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .voucher-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00BFA5, #4DB6AC, #26A69A);
            border-radius: 24px 24px 0 0;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #00BFA5, #4DB6AC);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }

        .card-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input-group {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 500;
            color: #1e293b;
            background: #ffffff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #00BFA5;
            box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
            transform: translateY(-1px);
        }

        .form-input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }

        .form-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .form-input:focus + .form-icon {
            color: #00BFA5;
        }

        .voucher-input-group {
            display: flex;
            gap: 8px;
        }

        .voucher-input {
            flex: 1;
            padding: 16px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 500;
            color: #1e293b;
            background: #ffffff;
            transition: all 0.3s ease;
            outline: none;
        }

        .voucher-input:focus {
            border-color: #00BFA5;
            box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
            transform: translateY(-1px);
        }

        .qr-scan-btn {
            padding: 16px 20px;
            background: linear-gradient(135deg, #6b7280, #4b5563);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
        }

        .qr-scan-btn:hover {
            background: linear-gradient(135deg, #4b5563, #374151);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
            color: white;
            text-decoration: none;
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #00BFA5, #4DB6AC);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-top: 8px;
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 191, 165, 0.4);
        }

        .policy-links {
            text-align: center;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .policy-links a {
            color: #00BFA5;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .policy-links a:hover {
            color: #26A69A;
            text-decoration: none;
        }

        .policy-separator {
            color: #cbd5e1;
            margin: 0 12px;
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .voucher-content {
                grid-template-columns: 1fr;
                gap: 24px;
                max-width: 600px;
            }

            .announcement-section {
                order: 2;
            }

            .voucher-forms {
                order: 1;
            }
        }

        @media (max-width: 480px) {
            .voucher-container {
                padding: 10px;
            }

            .voucher-card,
            .announcement-section {
                padding: 24px;
                border-radius: 20px;
            }

            .form-input {
                padding: 14px 18px 14px 45px;
            }

            .voucher-input {
                padding: 14px 18px;
            }

            .voucher-input-group {
                flex-direction: column;
                gap: 12px;
            }

            .qr-scan-btn {
                width: 100%;
                min-width: auto;
            }
        }
    </style>
</head>

<body>
    <div class="voucher-container">
        <div class="voucher-content">
            <div class="announcement-section">
                <div class="announcement-header">
                    <div class="announcement-icon">
                        <i class="fa fa-bullhorn"></i>
                    </div>
                    <h2 class="announcement-title">{Lang::T('Announcement')}</h2>
                </div>
                <div class="announcement-content">
                    {include file="$_path/../pages/Announcement.html"}
                </div>

                {if isset($notify)}
                    <div class="alert alert-{if $notify_t == 's'}success{elseif $notify_t == 'i'}info{elseif $notify_t == 'w'}warning{else}danger{/if}" id="voucherNotify" style="margin-top: 20px;">
                        <div class="alert-content">
                            <i class="fa {if $notify_t == 's'}fa-check-circle{elseif $notify_t == 'i'}fa-info-circle{elseif $notify_t == 'w'}fa-exclamation-triangle{else}fa-times-circle{/if}"></i>
                            <span class="alert-message">{$notify}</span>
                        </div>
                        {if $notify_t == 'e'}
                            <div class="alert-suggestion">
                                <small><i class="fa fa-info-circle"></i> Please check your voucher code and try again. Contact support if you need assistance.</small>
                            </div>
                        {/if}
                    </div>
                {/if}
            </div>

            <div class="voucher-forms">
                <!-- Login with Voucher -->
                <div class="voucher-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-sign-in"></i>
                        </div>
                        <h3 class="card-title">{Lang::T('Login / Activate Voucher')}</h3>
                    </div>

                    <form action="{Text::url('login/activation')}" method="post" id="loginVoucherForm">
                        <input type="hidden" name="csrf_token" value="{$csrf_token}">

                        <div class="form-group">
                            <label class="form-label">
                                {if $_c['registration_username'] == 'phone'}
                                    {Lang::T('Phone Number')}
                                {elseif $_c['registration_username'] == 'email'}
                                    {Lang::T('Email')}
                                {else}
                                    {Lang::T('Usernames')}
                                {/if}
                            </label>
                            <div class="form-input-group">
                                <input type="text"
                                       class="form-input"
                                       name="username"
                                       placeholder="{if $_c['country_code_phone']!= '' || $_c['registration_username'] == 'phone'}{$_c['country_code_phone']} {Lang::T('Phone Number')}{elseif $_c['registration_username'] == 'email'}{Lang::T('Email')}{else}{Lang::T('Usernames')}{/if}">
                                <i class="fa {if $_c['registration_username'] == 'phone'}fa-phone{elseif $_c['registration_username'] == 'email'}fa-envelope{else}fa-user{/if} form-icon"></i>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">{Lang::T('Enter voucher code here')}</label>
                            <div class="voucher-input-group">
                                <input type="text"
                                       class="voucher-input"
                                       id="voucher"
                                       name="voucher"
                                       required
                                       value="{$code}"
                                       placeholder="{Lang::T('Enter voucher code here')}">
                                <a class="qr-scan-btn" href="{APP_URL}/scan/?back={urlencode(Text::url('login&code='))}">
                                    <i class="fa fa-qrcode"></i>
                                </a>
                            </div>
                        </div>

                        <button type="submit" class="submit-btn">
                            <i class="fa fa-sign-in" style="margin-right: 8px;"></i>
                            {Lang::T('Login / Activate Voucher')}
                        </button>
                    </form>
                </div>

                <!-- Activate Voucher Only -->
                <div class="voucher-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-ticket"></i>
                        </div>
                        <h3 class="card-title">{Lang::T('Activate Voucher')}</h3>
                    </div>

                    <form action="{Text::url('login/activation')}" method="post" id="activateVoucherForm">
                        <input type="hidden" name="csrf_token" value="{$csrf_token}">

                        <div class="form-group">
                            <label class="form-label">{Lang::T('Enter voucher code here')}</label>
                            <div class="voucher-input-group">
                                <input type="text"
                                       class="voucher-input"
                                       id="voucher_only"
                                       name="voucher_only"
                                       required
                                       value="{$code}"
                                       placeholder="{Lang::T('Enter voucher code here')}">
                                <a class="qr-scan-btn" href="{APP_URL}/scan/?back={urlencode(Text::url('login&code='))}">
                                    <i class="fa fa-qrcode"></i>
                                </a>
                            </div>
                        </div>

                        <button type="submit" class="submit-btn">
                            <i class="fa fa-ticket" style="margin-right: 8px;"></i>
                            {Lang::T('Activate Voucher')}
                        </button>
                    </form>

                    <div class="policy-links">
                        <a href="./pages/Privacy_Policy.html" target="_blank">Privacy Policy</a>
                        <span class="policy-separator">•</span>
                        <a href="./pages/Terms_of_Conditions.html" target="_blank">Terms & Conditions</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle notifications
            const voucherNotify = document.getElementById('voucherNotify');
            if (voucherNotify) {
                // Auto-dismiss notification after 8 seconds
                setTimeout(() => {
                    voucherNotify.style.opacity = '0';
                    voucherNotify.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        voucherNotify.style.display = 'none';
                    }, 300);
                }, 8000);

                // Focus voucher input if there's an error
                const notifyType = '{if isset($notify_t)}{$notify_t}{/if}';
                if (notifyType === 'e') {
                    const voucherInput = document.querySelector('#voucher');
                    if (voucherInput) {
                        voucherInput.focus();
                        voucherInput.select();
                    }
                } else {
                    // Auto-focus first input for other cases
                    const firstInputLogin = document.querySelector('#loginVoucherForm input[type="text"]');
                    if (firstInputLogin) {
                        firstInputLogin.focus();
                    }
                }
            } else {
                // Auto-focus first input in each form if no notification
                const firstInputLogin = document.querySelector('#loginVoucherForm input[type="text"]');
                if (firstInputLogin) {
                    firstInputLogin.focus();
                }
            }

            // Form validation and enhancement
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('.submit-btn');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>';
                    }
                });
            });

            // Add CSS animation for loading spinner
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>

</html>