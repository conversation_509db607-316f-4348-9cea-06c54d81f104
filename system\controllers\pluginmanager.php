<?php

/**
 *  PHP Mikrotik Billing (https://github.com/hotspotbilling/phpnuxbill/)
 *  by https://t.me/ibnux
 **/

_admin();
$ui->assign('_title', 'Plugin Manager');
$ui->assign('_system_menu', 'settings');

$plugin_repository = 'https://hotspotbilling.github.io/Plugin-Repository/repository.json';

$action = $routes['1'];
$ui->assign('_admin', $admin);


if (!in_array($admin['user_type'], ['SuperAdmin', 'Admin'])) {
    _alert(Lang::T('You do not have permission to access this page'), 'danger', "dashboard");
}

$cache = $CACHE_PATH . File::pathFixer('/plugin_repository.json');
if (file_exists($cache) && time() - filemtime($cache) < (24 * 60 * 60)) {
    $txt = file_get_contents($cache);
    $json = json_decode($txt, true);
    if (empty($json['plugins']) && empty($json['payment_gateway'])) {
        unlink($cache);
        r2(getUrl('pluginmanager'));
    }
} else {
    $data = Http::getData($plugin_repository);
    if (empty($data) || strpos($data, 'Error') !== false) {
        // Fallback to empty arrays if repository is not accessible
        $json = [
            'plugins' => [],
            'payment_gateway' => [],
            'devices' => []
        ];
    } else {
        file_put_contents($cache, $data);
        $json = json_decode($data, true);
        if (!is_array($json)) {
            // Fallback if JSON is invalid
            $json = [
                'plugins' => [],
                'payment_gateway' => [],
                'devices' => []
            ];
        }
    }
}

// Ensure arrays exist
if (!isset($json['plugins'])) $json['plugins'] = [];
if (!isset($json['payment_gateway'])) $json['payment_gateway'] = [];
if (!isset($json['devices'])) $json['devices'] = [];
switch ($action) {
    case 'refresh':
        if (file_exists($cache))
            unlink($cache);
        r2(getUrl('pluginmanager'), 's', 'Refresh success');
        break;
    case 'dlinstall':
        if ($_app_stage == 'Demo') {
            r2(getUrl('pluginmanager'), 'e', 'Demo Mode cannot install as it Security risk');
        }
        if (!is_writeable($CACHE_PATH)) {
            r2(getUrl('pluginmanager'), 'e', 'Folder cache/ is not writable');
        }
        if (!is_writeable($PLUGIN_PATH)) {
            r2(getUrl('pluginmanager'), 'e', 'Folder plugin/ is not writable');
        }
        if (!is_writeable($DEVICE_PATH)) {
            r2(getUrl('pluginmanager'), 'e', 'Folder devices/ is not writable');
        }
        if (!is_writeable($UI_PATH . DIRECTORY_SEPARATOR . 'themes')) {
            r2(getUrl('pluginmanager'), 'e', 'Folder themes/ is not writable');
        }

        // Check if ZipArchive class exists
        if (!class_exists('ZipArchive')) {
            r2(getUrl('pluginmanager'), 'e', 'PHP ZipArchive extension is not installed');
        }

        $cache = $CACHE_PATH . DIRECTORY_SEPARATOR . 'installer' . DIRECTORY_SEPARATOR;
        if (!file_exists($cache)) {
            mkdir($cache, 0755, true);
        }
        if (file_exists($_FILES['zip_plugin']['tmp_name'])) {
            $zip = new ZipArchive();
            $zipResult = $zip->open($_FILES['zip_plugin']['tmp_name']);

            if ($zipResult !== TRUE) {
                unlink($_FILES['zip_plugin']['tmp_name']);
                r2(getUrl('pluginmanager'), 'e', 'Failed to open ZIP file. Error code: ' . $zipResult);
            }

            if (!$zip->extractTo($cache)) {
                $zip->close();
                unlink($_FILES['zip_plugin']['tmp_name']);
                r2(getUrl('pluginmanager'), 'e', 'Failed to extract ZIP file');
            }

            $zip->close();
            $plugin = basename($_FILES['zip_plugin']['name'], '.zip');
            unlink($_FILES['zip_plugin']['tmp_name']);
            $success = 0;

            //moving - check for structured plugin format first
            if (file_exists($cache . 'plugin')) {
                File::copyFolder($cache . 'plugin' . DIRECTORY_SEPARATOR, $PLUGIN_PATH . DIRECTORY_SEPARATOR);
                $success++;
            }
            if (file_exists($cache . 'paymentgateway')) {
                File::copyFolder($cache . 'paymentgateway' . DIRECTORY_SEPARATOR, $PAYMENTGATEWAY_PATH . DIRECTORY_SEPARATOR);
                $success++;
            }
            if (file_exists($cache . 'theme')) {
                File::copyFolder($cache . 'theme' . DIRECTORY_SEPARATOR, $UI_PATH . DIRECTORY_SEPARATOR . 'themes' . DIRECTORY_SEPARATOR);
                $success++;
            }
            if (file_exists($cache . 'device')) {
                File::copyFolder($cache . 'device' . DIRECTORY_SEPARATOR, $DEVICE_PATH . DIRECTORY_SEPARATOR);
                $success++;
            }

            if ($success == 0) {
                // Try to find extracted folder for old plugin format
                $extractedFolders = glob($cache . '*', GLOB_ONLYDIR);
                if (!empty($extractedFolders)) {
                    $folder = $extractedFolders[0] . DIRECTORY_SEPARATOR;
                    $pluginName = strtolower($plugin);

                    // Detect plugin type from filename or folder content
                    if (strpos($pluginName, 'plugin') !== false || file_exists($folder . 'plugin.php')) {
                        File::copyFolder($folder, $PLUGIN_PATH . DIRECTORY_SEPARATOR);
                        $success++;
                    } else if (strpos($pluginName, 'payment') !== false || strpos($pluginName, 'gateway') !== false) {
                        File::copyFolder($folder, $PAYMENTGATEWAY_PATH . DIRECTORY_SEPARATOR);
                        $success++;
                    } else if (strpos($pluginName, 'theme') !== false) {
                        $themeName = basename($folder);
                        rename($folder, $UI_PATH . DIRECTORY_SEPARATOR . 'themes' . DIRECTORY_SEPARATOR . $themeName);
                        $success++;
                    } else if (strpos($pluginName, 'device') !== false) {
                        File::copyFolder($folder, $DEVICE_PATH . DIRECTORY_SEPARATOR);
                        $success++;
                    } else {
                        // Default to plugin if can't determine type
                        File::copyFolder($folder, $PLUGIN_PATH . DIRECTORY_SEPARATOR);
                        $success++;
                    }
                }
            }

            //Cleaning
            File::deleteFolder($cache);

            if ($success > 0) {
                // Clear any opcode cache to ensure new plugins are loaded properly
                if (function_exists('opcache_reset')) {
                    opcache_reset();
                }

                // Check plugin compatibility after installation
                try {
                    $compatibilityWarnings = checkInstalledPluginsCompatibility();
                    if (!empty($compatibilityWarnings)) {
                        $warningMsg = 'Installation success, but some compatibility issues detected: ' . implode('; ', $compatibilityWarnings);
                        r2(getUrl('pluginmanager'), 'w', $warningMsg);
                    } else {
                        r2(getUrl('pluginmanager'), 's', 'Installation success');
                    }
                } catch (Exception $e) {
                    // If compatibility check fails, still show success but with warning
                    r2(getUrl('pluginmanager'), 's', 'Installation success (compatibility check skipped)');
                }
            } else {
                r2(getUrl('pluginmanager'), 'e', 'No valid plugin structure found in ZIP file');
            }
        } else if (_post('gh_url', '') != '') {
            $ghUrl = _post('gh_url', '');

            // Validate GitHub URL
            if (!filter_var($ghUrl, FILTER_VALIDATE_URL) || strpos($ghUrl, 'github.com') === false) {
                r2(getUrl('pluginmanager'), 'e', 'Invalid GitHub URL provided');
            }

            if (!empty($config['github_token']) && !empty($config['github_username'])) {
                $ghUrl = str_replace('https://github.com', 'https://' . $config['github_username'] . ':' . $config['github_token'] . '@github.com', $ghUrl);
            }

            $plugin = basename($ghUrl);
            $file = $cache . $plugin . '.zip';
            $fp = fopen($file, 'w+');

            if (!$fp) {
                r2(getUrl('pluginmanager'), 'e', 'Failed to create temporary file for download');
            }

            $ch = curl_init($ghUrl . '/archive/refs/heads/master.zip');
            curl_setopt($ch, CURLOPT_POST, 0);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_FILE, $fp);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'PHPNuxBill Plugin Manager');

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            fclose($fp);

            if ($result === false || !empty($error)) {
                unlink($file);
                r2(getUrl('pluginmanager'), 'e', 'Failed to download from GitHub: ' . $error);
            }

            if ($httpCode !== 200) {
                unlink($file);
                r2(getUrl('pluginmanager'), 'e', 'GitHub download failed with HTTP code: ' . $httpCode);
            }

            if (filesize($file) == 0) {
                unlink($file);
                r2(getUrl('pluginmanager'), 'e', 'Downloaded file is empty. Repository may not exist or is private.');
            }

            $zip = new ZipArchive();
            $zipResult = $zip->open($file);

            if ($zipResult !== TRUE) {
                unlink($file);
                r2(getUrl('pluginmanager'), 'e', 'Failed to open downloaded ZIP file. Error code: ' . $zipResult);
            }

            if (!$zip->extractTo($cache)) {
                $zip->close();
                unlink($file);
                r2(getUrl('pluginmanager'), 'e', 'Failed to extract downloaded ZIP file');
            }

            $zip->close();
            $folder = $cache . DIRECTORY_SEPARATOR . $plugin . '-main' . DIRECTORY_SEPARATOR;
            if (!file_exists($folder)) {
                $folder = $cache . DIRECTORY_SEPARATOR . $plugin . '-master' . DIRECTORY_SEPARATOR;
            }

            if (!file_exists($folder)) {
                // Try to find any extracted folder
                $extractedFolders = glob($cache . '*', GLOB_ONLYDIR);
                if (!empty($extractedFolders)) {
                    $folder = $extractedFolders[0] . DIRECTORY_SEPARATOR;
                } else {
                    unlink($file);
                    File::deleteFolder($cache);
                    r2(getUrl('pluginmanager'), 'e', 'No valid folder found in extracted ZIP');
                }
            }

            $success = 0;
            if (file_exists($folder . 'plugin')) {
                File::copyFolder($folder . 'plugin' . DIRECTORY_SEPARATOR, $PLUGIN_PATH . DIRECTORY_SEPARATOR);
                $success++;
            }
            if (file_exists($folder . 'paymentgateway')) {
                File::copyFolder($folder . 'paymentgateway' . DIRECTORY_SEPARATOR, $PAYMENTGATEWAY_PATH . DIRECTORY_SEPARATOR);
                $success++;
            }
            if (file_exists($folder . 'theme')) {
                File::copyFolder($folder . 'theme' . DIRECTORY_SEPARATOR, $UI_PATH . DIRECTORY_SEPARATOR . 'themes' . DIRECTORY_SEPARATOR);
                $success++;
            }
            if (file_exists($folder . 'device')) {
                File::copyFolder($folder . 'device' . DIRECTORY_SEPARATOR, $DEVICE_PATH . DIRECTORY_SEPARATOR);
                $success++;
            }

            if ($success == 0) {
                // old plugin and theme using this
                $check = strtolower($ghUrl);
                if (strpos($check, 'plugin') !== false || file_exists($folder . 'plugin.php')) {
                    File::copyFolder($folder, $PLUGIN_PATH . DIRECTORY_SEPARATOR);
                    $success++;
                } else if (strpos($check, 'payment') !== false || strpos($check, 'gateway') !== false) {
                    File::copyFolder($folder, $PAYMENTGATEWAY_PATH . DIRECTORY_SEPARATOR);
                    $success++;
                } else if (strpos($check, 'theme') !== false) {
                    $themeName = basename($folder);
                    rename($folder, $UI_PATH . DIRECTORY_SEPARATOR . 'themes' . DIRECTORY_SEPARATOR . $themeName);
                    $success++;
                } else if (strpos($check, 'device') !== false) {
                    File::copyFolder($folder, $DEVICE_PATH . DIRECTORY_SEPARATOR);
                    $success++;
                } else {
                    // Default to plugin if can't determine type
                    File::copyFolder($folder, $PLUGIN_PATH . DIRECTORY_SEPARATOR);
                    $success++;
                }
            }

            unlink($file);
            File::deleteFolder($cache);

            if ($success > 0) {
                // Clear any opcode cache to ensure new plugins are loaded properly
                if (function_exists('opcache_reset')) {
                    opcache_reset();
                }

                // Check plugin compatibility after installation
                try {
                    $compatibilityWarnings = checkInstalledPluginsCompatibility();
                    if (!empty($compatibilityWarnings)) {
                        $warningMsg = 'Installation success, but some compatibility issues detected: ' . implode('; ', $compatibilityWarnings);
                        r2(getUrl('pluginmanager'), 'w', $warningMsg);
                    } else {
                        r2(getUrl('pluginmanager'), 's', 'Installation success');
                    }
                } catch (Exception $e) {
                    // If compatibility check fails, still show success but with warning
                    r2(getUrl('pluginmanager'), 's', 'Installation success (compatibility check skipped)');
                }
            } else {
                r2(getUrl('pluginmanager'), 'e', 'No valid plugin structure found in downloaded repository');
            }
        } else {
            r2(getUrl('pluginmanager'), 'e', 'Nothing Installed');
        }
        break;
    case 'delete':
        if ($_app_stage == 'Demo') {
            r2(getUrl('pluginmanager'), 'e', 'You cannot perform this action in Demo mode');
        }
        if (!is_writeable($CACHE_PATH)) {
            r2(getUrl('pluginmanager'), 'e', 'Folder cache/ is not writable');
        }
        if (!is_writeable($PLUGIN_PATH)) {
            r2(getUrl('pluginmanager'), 'e', 'Folder plugin/ is not writable');
        }
        set_time_limit(-1);
        $tipe = $routes['2'];
        $plugin = $routes['3'];
        $file = $CACHE_PATH . DIRECTORY_SEPARATOR . $plugin . '.zip';
        if (file_exists($file))
            unlink($file);
        if ($tipe == 'plugin') {
            foreach ($json['plugins'] as $plg) {
                if ($plg['id'] == $plugin) {
                    if (!empty($config['github_token']) && !empty($config['github_username'])) {
                        $plg['github'] = str_replace('https://github.com', 'https://' . $config['github_username'] . ':' . $config['github_token'] . '@github.com', $plg['github']);
                    }
                    $fp = fopen($file, 'w+');
                    $ch = curl_init($plg['github'] . '/archive/refs/heads/master.zip');
                    curl_setopt($ch, CURLOPT_POST, 0);
                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_FILE, $fp);
                    curl_exec($ch);
                    curl_close($ch);
                    fclose($fp);

                    $zip = new ZipArchive();
                    $zip->open($file);
                    $zip->extractTo($CACHE_PATH);
                    $zip->close();
                    $folder = $CACHE_PATH . File::pathFixer('/' . $plugin . '-main/');
                    if (!file_exists($folder)) {
                        $folder = $CACHE_PATH . File::pathFixer('/' . $plugin . '-master/');
                    }
                    if (!file_exists($folder)) {
                        r2(getUrl('pluginmanager'), 'e', 'Extracted Folder is unknown');
                    }
                    scanAndRemovePath($folder, $PLUGIN_PATH . DIRECTORY_SEPARATOR);
                    File::deleteFolder($folder);
                    unlink($file);
                    r2(getUrl('pluginmanager'), 's', 'Plugin ' . $plugin . ' has been deleted');
                    break;
                }
            }
            break;
        }
        break;
    case 'install':
        if ($_app_stage == 'Demo') {
            r2(getUrl('pluginmanager'), 'e', 'You cannot perform this action in Demo mode');
        }
        if (!is_writeable($CACHE_PATH)) {
            r2(getUrl('pluginmanager'), 'e', 'Folder cache/ is not writable');
        }
        if (!is_writeable($PLUGIN_PATH)) {
            r2(getUrl('pluginmanager'), 'e', 'Folder plugin/ is not writable');
        }
        set_time_limit(-1);
        $tipe = $routes['2'];
        $plugin = $routes['3'];
        $file = $CACHE_PATH . DIRECTORY_SEPARATOR . $plugin . '.zip';
        if (file_exists($file))
            unlink($file);
        if ($tipe == 'plugin') {
            foreach ($json['plugins'] as $plg) {
                if ($plg['id'] == $plugin) {
                    if (!empty($config['github_token']) && !empty($config['github_username'])) {
                        $plg['github'] = str_replace('https://github.com', 'https://' . $config['github_username'] . ':' . $config['github_token'] . '@github.com', $plg['github']);
                    }
                    $fp = fopen($file, 'w+');
                    $ch = curl_init($plg['github'] . '/archive/refs/heads/master.zip');
                    curl_setopt($ch, CURLOPT_POST, 0);
                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_FILE, $fp);
                    curl_exec($ch);
                    curl_close($ch);
                    fclose($fp);

                    $zip = new ZipArchive();
                    $zip->open($file);
                    $zip->extractTo($CACHE_PATH);
                    $zip->close();
                    $folder = $CACHE_PATH . File::pathFixer('/' . $plugin . '-main/');
                    if (!file_exists($folder)) {
                        $folder = $CACHE_PATH . File::pathFixer('/' . $plugin . '-master/');
                    }
                    if (!file_exists($folder)) {
                        r2(getUrl('pluginmanager'), 'e', 'Extracted Folder is unknown');
                    }
                    File::copyFolder($folder, $PLUGIN_PATH . DIRECTORY_SEPARATOR, ['README.md', 'LICENSE']);
                    File::deleteFolder($folder);
                    unlink($file);
                    r2(getUrl('pluginmanager'), 's', 'Plugin ' . $plugin . ' has been installed');
                    break;
                }
            }
            break;
        } else if ($tipe == 'payment') {
            foreach ($json['payment_gateway'] as $plg) {
                if ($plg['id'] == $plugin) {
                    if (!empty($config['github_token']) && !empty($config['github_username'])) {
                        $plg['github'] = str_replace('https://github.com', 'https://' . $config['github_username'] . ':' . $config['github_token'] . '@github.com', $plg['github']);
                    }
                    $fp = fopen($file, 'w+');
                    $ch = curl_init($plg['github'] . '/archive/refs/heads/master.zip');
                    curl_setopt($ch, CURLOPT_POST, 0);
                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_FILE, $fp);
                    curl_exec($ch);
                    curl_close($ch);
                    fclose($fp);

                    $zip = new ZipArchive();
                    $zip->open($file);
                    $zip->extractTo($CACHE_PATH);
                    $zip->close();
                    $folder = $CACHE_PATH . File::pathFixer('/' . $plugin . '-main/');
                    if (!file_exists($folder)) {
                        $folder = $CACHE_PATH . File::pathFixer('/' . $plugin . '-master/');
                    }
                    if (!file_exists($folder)) {
                        r2(getUrl('pluginmanager'), 'e', 'Extracted Folder is unknown');
                    }
                    File::copyFolder($folder, $PAYMENTGATEWAY_PATH . DIRECTORY_SEPARATOR, ['README.md', 'LICENSE']);
                    File::deleteFolder($folder);
                    unlink($file);
                    r2(getUrl('paymentgateway'), 's', 'Payment Gateway ' . $plugin . ' has been installed');
                    break;
                }
            }
            break;
        } else if ($tipe == 'device') {
            foreach ($json['devices'] as $d) {
                if ($d['id'] == $plugin) {
                    if (!empty($config['github_token']) && !empty($config['github_username'])) {
                        $d['github'] = str_replace('https://github.com', 'https://' . $config['github_username'] . ':' . $config['github_token'] . '@github.com', $d['github']);
                    }
                    $fp = fopen($file, 'w+');
                    $ch = curl_init($d['github'] . '/archive/refs/heads/master.zip');
                    curl_setopt($ch, CURLOPT_POST, 0);
                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_FILE, $fp);
                    curl_exec($ch);
                    curl_close($ch);
                    fclose($fp);

                    $zip = new ZipArchive();
                    $zip->open($file);
                    $zip->extractTo($CACHE_PATH);
                    $zip->close();
                    $folder = $CACHE_PATH . File::pathFixer('/' . $plugin . '-main/');
                    if (!file_exists($folder)) {
                        $folder = $CACHE_PATH . File::pathFixer('/' . $plugin . '-master/');
                    }
                    if (!file_exists($folder)) {
                        r2(getUrl('pluginmanager'), 'e', 'Extracted Folder is unknown');
                    }
                    File::copyFolder($folder, $DEVICE_PATH . DIRECTORY_SEPARATOR, ['README.md', 'LICENSE']);
                    File::deleteFolder($folder);
                    unlink($file);
                    r2(getUrl('settings/devices'), 's', 'Device ' . $plugin . ' has been installed');
                    break;
                }
            }
            break;
        }
    default:
        if (class_exists('ZipArchive')) {
            $zipExt = true;
        } else {
            $zipExt = false;
        }

        // Check compatibility of installed plugins
        try {
            $compatibilityWarnings = checkInstalledPluginsCompatibility();
            $serverEnv = class_exists('Compatibility') ? Compatibility::getServerEnvironment() : [];
        } catch (Exception $e) {
            $compatibilityWarnings = [];
            $serverEnv = [];
        }

        $ui->assign('zipExt', $zipExt);
        $ui->assign('plugins', $json['plugins']);
        $ui->assign('pgs', $json['payment_gateway']);
        $ui->assign('dvcs', $json['devices']);
        $ui->assign('server_env', $serverEnv);
        $ui->assign('compatibility_warnings', $compatibilityWarnings);
        $ui->display('admin/settings/plugin-manager.tpl');
}

/**
 * Check compatibility of installed plugins
 */
function checkInstalledPluginsCompatibility()
{
    global $PLUGIN_PATH;
    $warnings = [];

    try {
        if (!class_exists('Compatibility')) {
            return $warnings; // Skip if Compatibility class not available
        }

        $plugins = glob(File::pathFixer($PLUGIN_PATH . DIRECTORY_SEPARATOR . '*.php'));
        if (!$plugins) {
            return $warnings; // No plugins found
        }

        foreach ($plugins as $plugin) {
            try {
                $compatibility = Compatibility::checkPluginCompatibility($plugin);
                if (!$compatibility['compatible']) {
                    $pluginName = basename($plugin, '.php');
                    $warnings[] = "$pluginName: " . implode(', ', $compatibility['issues']);
                }
            } catch (Exception $e) {
                // Skip individual plugin check if it fails
                continue;
            }
        }
    } catch (Exception $e) {
        // If entire compatibility check fails, return empty array
        return [];
    }

    return $warnings;
}

function scanAndRemovePath($source, $target)
{
    $files = scandir($source);
    foreach ($files as $file) {
        if (is_file($source . $file)) {
            if (file_exists($target . $file)) {
                unlink($target . $file);
            }
        } else if (is_dir($source . $file) && !in_array($file, ['.', '..'])) {
            scanAndRemovePath($source . $file . DIRECTORY_SEPARATOR, $target . $file . DIRECTORY_SEPARATOR);
            if (file_exists($target . $file)) {
                rmdir($target . $file);
            }
        }
    }
    if (file_exists($target)) {
        rmdir($target);
    }
}
