{include file="sections/header.tpl"}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.4/jspdf.min.js"></script>
<style>
    /* Enhanced Invoice Preview Styles */
    .invoice-preview {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .invoice-content {
        background: white;
        border-radius: 6px;
        padding: 15px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
        min-height: 300px;
        border: 1px solid #e9ecef;
    }

    .invoice-actions {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
    }

    .invoice-actions .btn {
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .invoice-url {
        margin-top: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }

    .invoice-logo {
        text-align: center;
        margin-bottom: 20px;
    }

    .invoice-logo img {
        max-width: 200px;
        height: auto;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 768px) {
        .invoice-preview {
            margin: 10px -15px;
            border-radius: 0;
        }

        .invoice-content {
            font-size: 12px;
            padding: 10px;
        }

        .invoice-actions .btn {
            width: 100%;
            margin-right: 0;
        }
    }
</style>
<div class="row">
    <div class="col-md-8 col-sm-12 col-md-offset-2">
        <div class="panel panel-hovered panel-primary panel-stacked mb30">
            <div class="panel-heading">
                <i class="fa fa-file-text-o"></i> {$in['invoice']} - {Lang::T('Invoice Preview')}
            </div>
            <div class="panel-body">
                <div class="invoice-preview">
                    {if !empty($logo)}
                        <div class="invoice-logo">
                            <img src="{$app_url}/{$logo}?" alt="Company Logo">
                        </div>
                    {/if}

                    <pre class="invoice-content" id="content">{$invoice}</pre>

                    <form class="form-horizontal" method="post" action="{Text::url('')}plan/print" target="_blank">
                        <textarea class="hidden" id="formcontent" name="content">{$invoice}</textarea>
                        <input type="hidden" name="id" value="{$in['id']}">

                        <div class="invoice-actions">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="fa fa-cogs"></i> {Lang::T('Actions')}</h5>
                                    <a href="{Text::url('plan/list')}" class="btn btn-default btn-sm">
                                        <i class="fa fa-arrow-left"></i> {Lang::T('Back to List')}
                                    </a>
                                    <a href="javascript:download()" class="btn btn-success btn-sm">
                                        <i class="fa fa-download"></i> {Lang::T('Download PDF')}
                                    </a>
                                    <a href="{Text::url('')}plan/view/{$in['id']}/send" class="btn btn-info btn-sm">
                                        <i class="fa fa-envelope"></i> {Lang::T("Resend Email")}
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <h5><i class="fa fa-share"></i> {Lang::T('Share')}</h5>
                                    <a href="https://api.whatsapp.com/send/?text={$whatsapp}" target="_blank"
                                        class="btn btn-success btn-sm">
                                        <i class="fa fa-whatsapp"></i> WhatsApp
                                    </a>
                                    <a href="{Text::url('')}plan/print/{$in['id']}" target="_print"
                                        class="btn btn-primary btn-sm">
                                        <i class="fa fa-print"></i> {Lang::T('Print HTML')}
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fa fa-print"></i> {Lang::T('Print Text')}
                                    </button>
                                </div>
                            </div>

                            <div class="row" style="margin-top: 15px;">
                                <div class="col-md-12">
                                    <h5><i class="fa fa-mobile"></i> {Lang::T('Mobile Printing')}</h5>
                                    <a href="nux://print?text={urlencode($invoice)}"
                                        class="btn btn-warning btn-sm hidden-md hidden-lg">
                                        <i class="fa fa-mobile"></i> NuxPrint Mobile
                                    </a>
                                    <a href="https://github.com/hotspotbilling/android-printer"
                                        class="btn btn-warning btn-sm hidden-xs hidden-sm" target="_blank">
                                        <i class="fa fa-android"></i> Get NuxPrint App
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="invoice-url">
                            <label><i class="fa fa-link"></i> {Lang::T('Public Invoice URL')}:</label>
                            <input type="text" class="form-control" readonly onclick="this.select()" value="{$public_url}">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext('2d');
    ctx.font = '16px Courier';
    var text = document.getElementById("content").textContent || document.getElementById("content").innerText;
    var lines = text.split(/\r\n|\r|\n/).length;
    var meas = ctx.measureText("A");
    let width = Math.round({$_c['printer_cols']} * 9.6);
    var height = Math.round((14 * lines));
    console.log(width, height, lines);
    var paid = new Image();
    paid.src = '{$app_url}/system/uploads/paid.png';
    {if !empty($logo)}
        var img = new Image();
        img.src = '{$app_url}/{$logo}?{time()}';
        var new_width = (width / 4) * 2;
        var new_height = Math.ceil({$hlogo} * (new_width/{$wlogo}));
        height = height + new_height;
    {/if}

    function download() {
        var doc = new jsPDF('p', 'px', [width, height]);
        {if !empty($logo)}
            try {
                doc.addImage(img, 'PNG', (width - new_width) / 2, 10, new_width, new_height);
            } catch (err) {}
        {/if}
        try {
            doc.addImage(paid, 'PNG', (width - 200) / 2, (height - 145) / 2, 200, 145);
        } catch (err) {}
        doc.setFont("Courier");
        doc.setFontSize(16);
        doc.text($('#content').text(), width / 2, new_height + 30, 'center');
        doc.save('{$in['invoice']}.pdf');
    }
</script>
{include file="sections/footer.tpl"}